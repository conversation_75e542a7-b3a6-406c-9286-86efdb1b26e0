package com.nnepoch.rendermanager.infrastructure.event.store;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.nnepoch.rendermanager.domain.shared.event.DomainEvent;
import com.nnepoch.rendermanager.domain.shared.valueobject.EntityId;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * PostgreSQL事件存储实现
 * <AUTHOR> Architect
 */
@Repository
@RequiredArgsConstructor
@Slf4j
public class PostgreSQLEventStore implements EventStore {
    
    private final JdbcTemplate jdbcTemplate;
    private final ObjectMapper objectMapper;
    
    private static final String INSERT_EVENT = """
        INSERT INTO event_store (
            event_id, aggregate_id, aggregate_type, event_type, 
            event_data, metadata, version, occurred_on, stored_on
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        """;
    
    private static final String SELECT_EVENTS_BY_AGGREGATE = """
        SELECT * FROM event_store 
        WHERE aggregate_id = ? AND aggregate_type = ? 
        ORDER BY version ASC
        """;
    
    private static final String SELECT_EVENTS_AFTER_VERSION = """
        SELECT * FROM event_store 
        WHERE aggregate_id = ? AND aggregate_type = ? AND version > ? 
        ORDER BY version ASC
        """;
    
    private static final String SELECT_LATEST_VERSION = """
        SELECT MAX(version) FROM event_store 
        WHERE aggregate_id = ? AND aggregate_type = ?
        """;
    
    @Override
    @Transactional
    public void saveEvent(DomainEvent event, EntityId aggregateId, String aggregateType, Long version) {
        try {
            String eventData = objectMapper.writeValueAsString(event);
            String metadata = createMetadata(event);
            
            jdbcTemplate.update(INSERT_EVENT,
                event.getEventId(),
                aggregateId.getValue().toString(),
                aggregateType,
                event.getEventType(),
                eventData,
                metadata,
                version,
                event.getOccurredOn(),
                LocalDateTime.now()
            );
            
            log.debug("Saved event: {} for aggregate: {}", event.getEventType(), aggregateId);
            
        } catch (DataIntegrityViolationException e) {
            throw new ConcurrencyException("Concurrent modification detected for aggregate: " + aggregateId, e);
        } catch (Exception e) {
            throw new EventStoreException("Failed to save event: " + event.getEventType(), e);
        }
    }
    
    @Override
    @Transactional
    public void saveEvents(List<DomainEvent> events, EntityId aggregateId, String aggregateType, Long baseVersion) {
        for (int i = 0; i < events.size(); i++) {
            saveEvent(events.get(i), aggregateId, aggregateType, baseVersion + i + 1);
        }
    }
    
    @Override
    public List<StoredEvent> getEventsForAggregate(EntityId aggregateId, String aggregateType) {
        return jdbcTemplate.query(SELECT_EVENTS_BY_AGGREGATE, 
            new StoredEventRowMapper(), 
            aggregateId.getValue().toString(), 
            aggregateType);
    }
    
    @Override
    public List<StoredEvent> getEventsForAggregateAfterVersion(EntityId aggregateId, String aggregateType, Long version) {
        return jdbcTemplate.query(SELECT_EVENTS_AFTER_VERSION, 
            new StoredEventRowMapper(), 
            aggregateId.getValue().toString(), 
            aggregateType, 
            version);
    }
    
    @Override
    public List<StoredEvent> getEventsBetween(LocalDateTime start, LocalDateTime end) {
        String sql = "SELECT * FROM event_store WHERE occurred_on BETWEEN ? AND ? ORDER BY occurred_on ASC";
        return jdbcTemplate.query(sql, new StoredEventRowMapper(), start, end);
    }
    
    @Override
    public List<StoredEvent> getEventsByType(String eventType) {
        String sql = "SELECT * FROM event_store WHERE event_type = ? ORDER BY occurred_on ASC";
        return jdbcTemplate.query(sql, new StoredEventRowMapper(), eventType);
    }
    
    @Override
    public Optional<Long> getLatestVersion(EntityId aggregateId, String aggregateType) {
        Long version = jdbcTemplate.queryForObject(SELECT_LATEST_VERSION, 
            Long.class, 
            aggregateId.getValue().toString(), 
            aggregateType);
        return Optional.ofNullable(version);
    }
    
    @Override
    @Transactional
    public void saveSnapshot(EntityId aggregateId, String aggregateType, Object snapshot, Long version) {
        try {
            String snapshotData = objectMapper.writeValueAsString(snapshot);
            String sql = """
                INSERT INTO aggregate_snapshots (aggregate_id, aggregate_type, snapshot_data, version, created_at)
                VALUES (?, ?, ?, ?, ?)
                ON CONFLICT (aggregate_id, aggregate_type) 
                DO UPDATE SET snapshot_data = ?, version = ?, created_at = ?
                """;
            
            LocalDateTime now = LocalDateTime.now();
            jdbcTemplate.update(sql,
                aggregateId.getValue().toString(),
                aggregateType,
                snapshotData,
                version,
                now,
                snapshotData,
                version,
                now
            );
            
            log.debug("Saved snapshot for aggregate: {} at version: {}", aggregateId, version);
            
        } catch (Exception e) {
            throw new EventStoreException("Failed to save snapshot for aggregate: " + aggregateId, e);
        }
    }
    
    @Override
    public Optional<StoredSnapshot> getLatestSnapshot(EntityId aggregateId, String aggregateType) {
        String sql = """
            SELECT * FROM aggregate_snapshots 
            WHERE aggregate_id = ? AND aggregate_type = ?
            """;
        
        try {
            StoredSnapshot snapshot = jdbcTemplate.queryForObject(sql, 
                new StoredSnapshotRowMapper(), 
                aggregateId.getValue().toString(), 
                aggregateType);
            return Optional.ofNullable(snapshot);
        } catch (Exception e) {
            return Optional.empty();
        }
    }
    
    private String createMetadata(DomainEvent event) {
        // 创建事件元数据
        return "{}"; // 简化实现
    }
    
    private static class StoredEventRowMapper implements RowMapper<StoredEvent> {
        @Override
        public StoredEvent mapRow(ResultSet rs, int rowNum) throws SQLException {
            return StoredEvent.builder()
                .eventId(rs.getString("event_id"))
                .aggregateId(rs.getString("aggregate_id"))
                .aggregateType(rs.getString("aggregate_type"))
                .eventType(rs.getString("event_type"))
                .eventData(rs.getString("event_data"))
                .metadata(rs.getString("metadata"))
                .version(rs.getLong("version"))
                .occurredOn(rs.getTimestamp("occurred_on").toLocalDateTime())
                .storedOn(rs.getTimestamp("stored_on").toLocalDateTime())
                .build();
        }
    }
    
    private static class StoredSnapshotRowMapper implements RowMapper<StoredSnapshot> {
        @Override
        public StoredSnapshot mapRow(ResultSet rs, int rowNum) throws SQLException {
            return StoredSnapshot.builder()
                .aggregateId(rs.getString("aggregate_id"))
                .aggregateType(rs.getString("aggregate_type"))
                .snapshotData(rs.getString("snapshot_data"))
                .version(rs.getLong("version"))
                .createdAt(rs.getTimestamp("created_at").toLocalDateTime())
                .build();
        }
    }
}

/**
 * 并发异常
 */
class ConcurrencyException extends RuntimeException {
    public ConcurrencyException(String message, Throwable cause) {
        super(message, cause);
    }
}

/**
 * 事件存储异常
 */
class EventStoreException extends RuntimeException {
    public EventStoreException(String message, Throwable cause) {
        super(message, cause);
    }
}
