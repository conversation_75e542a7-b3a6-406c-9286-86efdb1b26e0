package com.nnepoch.rendermanager.api.dto.request.base;

import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR> 2024/6/14
 */
@Data
@Schema(description = "分页查询参数")
public class PageQueryDTO {
    @Parameter(description = "页码", example = "1")
    @NotNull(message = "页码不能为空")
    private Integer page = 1;

    @Parameter(description = "每页数量", example = "10")
    @NotNull(message = "每页数量不能为空")
    private Integer size = 10;

    @Parameter(description = "排序字段 (如：id,desc)")
    private String sort;
}
