package com.nnepoch.rendermanager.domain.user.event;

import com.nnepoch.rendermanager.domain.shared.event.DomainEvent;
import com.nnepoch.rendermanager.domain.shared.valueobject.UserId;
import lombok.Getter;

/**
 * 用户登录事件
 * <AUTHOR> Migration
 */
@Getter
public class UserLoginEvent extends DomainEvent {
    private final UserId userId;
    private final String username;
    
    public UserLoginEvent(UserId userId, String username) {
        super();
        this.userId = userId;
        this.username = username;
    }
    
    @Override
    public String getEventType() {
        return "UserLogin";
    }
}
