package com.nnepoch.rendermanager.domain.scene.event;

import com.nnepoch.rendermanager.domain.shared.event.DomainEvent;
import com.nnepoch.rendermanager.domain.shared.valueobject.SceneId;
import lombok.Getter;

/**
 * 场景封禁事件
 * <AUTHOR> Architect
 */
@Getter
public class SceneBannedEvent extends DomainEvent {
    private final SceneId sceneId;
    private final String sceneName;
    private final Integer bannedTimeInMinutes;
    
    public SceneBannedEvent(SceneId sceneId, String sceneName, Integer bannedTimeInMinutes) {
        super();
        this.sceneId = sceneId;
        this.sceneName = sceneName;
        this.bannedTimeInMinutes = bannedTimeInMinutes;
    }
    
    @Override
    public String getEventType() {
        return "SceneBanned";
    }
}
