<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nnepoch.rendermanager.biz.mapper.SceneExperienceMapper">
    <resultMap id="BaseResultMap" type="com.nnepoch.rendermanager.api.dto.response.SceneExperienceResDTO">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="hashId" column="hash_id" jdbcType="VARCHAR"/>
        <result property="sceneId" column="scene_id" jdbcType="BIGINT"/>
        <result property="sceneName" column="scene_name" jdbcType="VARCHAR"/>
        <result property="sceneLink" column="scene_link" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="SMALLINT"/>
        <result property="isBanned" column="is_banned" jdbcType="BOOLEAN"/>
        <result property="username" column="username" jdbcType="VARCHAR"/>
        <result property="organization" column="organization" jdbcType="VARCHAR"/>
        <result property="mobile" column="mobile" jdbcType="VARCHAR"/>
        <result property="email" column="email" jdbcType="VARCHAR"/>
        <result property="avatar" column="avatar" jdbcType="VARCHAR"/>
        <result property="duration" column="duration" jdbcType="INTEGER"/>
        <result property="keepDuration" column="keep_duration" jdbcType="INTEGER"/>
        <result property="maxConcurrent" column="max_concurrent" jdbcType="INTEGER"/>
        <result property="expiredAt" column="expired_at" jdbcType="TIMESTAMP"/>
        <result property="viewedAt" column="viewed_at" jdbcType="TIMESTAMP"/>
        <result property="createdBy" column="created_by" jdbcType="BIGINT"/>
        <result property="createdAt" column="created_at" jdbcType="TIMESTAMP"/>
        <result property="updatedAt" column="updated_at" jdbcType="TIMESTAMP"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="deletedAt" column="deleted_at" jdbcType="TIMESTAMP"/>
        <association property="creator" javaType="com.nnepoch.rendermanager.api.dto.response.SimpleUserResDTO">
            <id property="id" column="creator_id" jdbcType="BIGINT"/>
            <result property="username" column="creator_username" jdbcType="VARCHAR"/>
            <result property="email" column="creator_email" jdbcType="VARCHAR"/>
            <result property="mobile" column="creator_mobile" jdbcType="VARCHAR"/>
            <result property="avatar" column="creator_avatar" jdbcType="VARCHAR"/>
        </association>
    </resultMap>

    <sql id="Base_Column_List">
        se.*,
        CONCAT('/avatar/9.x/big-smile/svg?seed=', se.id) AS avatar,
        s.name AS scene_name,
        s.link AS scene_link,
        CASE
            WHEN se.status = 2 THEN FLOOR(EXTRACT(EPOCH FROM NOW() - se.viewed_at) / 60)
            ELSE NULL
        END AS keep_duration,
        u.id AS creator_id,
        u.username AS creator_username,
        u.email AS creator_email,
        u.mobile AS creator_mobile,
        CONCAT('/avatar/9.x/big-smile/svg?seed=', u.id) AS creator_avatar
    </sql>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM public.scene_experience se
        INNER JOIN public.scene s ON se.scene_id = s.id AND se.id = #{id}
        LEFT JOIN public.user u ON se.created_by = u.id
    </select>

    <select id="selectByPage" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM public.scene_experience se
        INNER JOIN public.scene s ON se.scene_id = s.id
        LEFT JOIN public.user u ON se.created_by = u.id
        ${ew.customSqlSegment}
    </select>
</mapper>
