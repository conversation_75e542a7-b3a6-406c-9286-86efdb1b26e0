package com.nnepoch.rendermanager.config.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> <PERSON> 2024/6/20
 */
@Component
@Validated
@Data
@ConfigurationProperties(prefix = "app.secure.ignore")
public class AppSecureProperties {
    private List<String> urls = new ArrayList<>();
}
