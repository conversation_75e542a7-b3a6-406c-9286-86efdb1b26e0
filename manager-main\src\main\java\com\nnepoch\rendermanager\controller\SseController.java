package com.nnepoch.rendermanager.controller;

import com.nnepoch.rendermanager.biz.service.SseService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.data.repository.query.Param;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import static org.springframework.http.MediaType.TEXT_EVENT_STREAM_VALUE;

/**
 * <AUTHOR> 2024/8/20
 */
@Controller
@RequestMapping(path = "/api/v1/sse", produces = TEXT_EVENT_STREAM_VALUE)
@RequiredArgsConstructor
@Tag(name = "SSE", description = "SSE接口")
@SecurityRequirement(name = "bearerAuth")
public class SseController {
    private final SseService sseService;

    @PreAuthorize("hasAnyRole('ADMIN', 'OPERATOR')")
    @GetMapping("/nodes/{nodeSn}/metrics")
    @Operation(summary = "获取节点的实时监控数据")
    public SseEmitter sendNodeMetric(@PathVariable String nodeSn, @Param("frequency") String frequency) {
        return sseService.createEmitter(nodeSn, frequency);
    }

}
