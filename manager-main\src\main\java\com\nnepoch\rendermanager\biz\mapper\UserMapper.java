package com.nnepoch.rendermanager.biz.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.nnepoch.rendermanager.api.dto.response.SimpleUserResDTO;
import com.nnepoch.rendermanager.biz.entity.UserEntity;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

/**
 * <AUTHOR> 2024/6/13
 */
public interface UserMapper extends BaseMapper<UserEntity> {
    @Update("DELETE FROM public.user WHERE id = #{id}")
    void physicalDeleteById(@Param("id") Long id);

    @Select("SELECT u.*, r.permissions FROM public.user u, public.role r WHERE u.role = r.name AND u.mobile = #{mobile}")
    UserEntity getUserByMobileWithPassword(@Param("mobile") String mobile);

    @Select("SELECT id, username, mobile, email FROM public.user WHERE id = #{id}")
    SimpleUserResDTO getSimpleUserById(@Param("id") Long id);
}
