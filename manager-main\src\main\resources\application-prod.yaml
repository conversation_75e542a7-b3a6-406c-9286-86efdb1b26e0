spring:
  datasource:
    url: jdbc:postgresql://${DATABASE_HOST:localhost}:${DATABASE_PORT:5432}/${DATABASE_NAME:render-manager}?useSSL=false&useUnicode=true&characterEncoding=utf-8&serverTimezone=UTC
    username: ${DATABASE_USERNAME:render-manager}
    password: ${DATABASE_PASSWORD:render-manager}
    driver-class-name: org.postgresql.Driver
    hikari:
      auto-commit: true
      connection-test-query: SELECT 1
      pool-name: HikariCP
      maximum-pool-size: 20
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
      minimum-idle: 10

springdoc:
  api-docs:
    enabled: false
  swagger-ui:
    enabled: false

app:
  minio:
    url: ${MINIO_URL:http://oss.dtc.io}
    endpoint: ${MINIO_ENDPOINT:http://minio-api-svc.middleware:9000}
    access-key: ${MINIO_ACCESS_KEY:X03ln4VwZYqcTEMMWEPe}
    secret-key: ${MINIO_SECRET_KEY:MIdFvfeXbKPN46ezNGQGRfn2PTjYJTcr33FgbOAY}
    bucket-name: ${MINIO_BUCKET_NAME:render-manager}