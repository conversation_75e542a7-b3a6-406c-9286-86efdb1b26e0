package com.nnepoch.rendermanager.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> 2024/6/19
 */
@AllArgsConstructor
@Getter
public enum UserRoleEnum {
    ADMIN("admin", "系统管理员"),
    OPERATOR("operator", "运维人员"),
    MEMBER("member", "普通用户");

    public final String code;
    public final String desc;

    public static UserRoleEnum fromCode(String code) {
        for (UserRoleEnum role : UserRoleEnum.values()) {
            if (role.code.equals(code)) {
                return role;
            }
        }
        return null;
    }

    public static UserRoleEnum fromName(String name) {
        for (UserRoleEnum role : UserRoleEnum.values()) {
            if (role.name().equals(name)) {
                return role;
            }
        }
        return null;
    }
}
