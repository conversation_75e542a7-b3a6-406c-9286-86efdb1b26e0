spring:
  application:
    name: render-manager
  servlet:
    multipart:
      max-file-size: 5GB
  profiles:
    active: ${SPRING_PROFILES_ACTIVE:dev}
  devtools:
    restart:
      enabled: false
  flyway:
    enabled: true
    baseline-on-migrate: true
    locations: classpath:db/migration
    encoding: UTF-8
    validate-migration-naming: true
  rabbitmq:
    host: ${RABBITMQ_HOST:dtc.io}
    port: ${RABBITMQ_PORT:30006}
    username: ${RABBITMQ_USERNAME:cloud}
    password: ${RABBITMQ_PASSWORD:cloud}
    virtual-host: ${RABBITMQ_VIRTUAL_HOST:/render-manager-local}
  data:
    redis:
      host: ${REDIS_HOST:redis}
      port: ${REDIS_PORT:6379}
      database: 0
      password: ${REDIS_PASSWORD:tV0zD6zY3kI0fL4w}
      timeout: 10000

server:
  port: 8080
  shutdown: graceful

app:
  jwt:
    key: ${JWT_KEY:5255cb96d7b90281ac96265ed95d1ef75d4dbd2b3ca3d608262669eb16494e4a}
    issuer: nnepoch.com
    algorithm: HS256
    expiresIn: 2h
  secure:
    ignore:
      urls:
        - /
        - /rest/actuator/**
        - /swagger/**
        - /api/ws
        - /api/v1/auth/**
        - /api/v1/dingtalk/auth
        - /api/v1/settings/public
        - /open/**
  minio:
    url: ${MINIO_URL:http://oss.dtc.io}
    endpoint: ${MINIO_ENDPOINT:http://oss.dtc.io}
    access-key: ${MINIO_ACCESS_KEY:X03ln4VwZYqcTEMMWEPe}
    secret-key: ${MINIO_SECRET_KEY:MIdFvfeXbKPN46ezNGQGRfn2PTjYJTcr33FgbOAY}
    public-bucket-name: ${MINIO_BUCKET_NAME:render-manager}
  dingtalk:
    client-id: ${DINGTALK_CLIENT_ID:dingrdzgkx3xriswefze}
    client-secret: ${DINGTALK_CLIENT_SECRET:fhXHkQexwWJryJrXkcI8ER1KV_RKgOMY28eJ79u_PQvqv6TI8kByPZ3aATMlWYcF}
  openapi:
    client-id: ${CLIENT_ID:081fcf4505ad93e22e5d}
    client-secret: ${CLIENT_SECRET:8dfaa2d4c8fcf08f4fcada76d739c3942ab89b4e}

logging:
  file:
    name: logs/render-manager.log
  level:
    root: ${LOG_LEVEL:INFO}
  pattern:
    correlation: "[${spring.application.name:},%X{traceId:-},%X{spanId:-}] "
  include-application-name: false

management:
  info:
    git:
      enabled: true
      mode: full
  tracing:
    enabled: true
  endpoint:
    health:
      show-details: always
  endpoints:
    web:
      base-path: /rest/actuator
      exposure:
        include: '*'

springdoc:
  version: "@springdoc.version@"
  api-docs:
    version: openapi_3_1
    path: /swagger/api-docs
    resolve-schema-properties: true
  swagger-ui:
    path: /swagger/index.html
    display-request-duration: true
    groups-order: DESC
    operationsSorter: method
    disable-swagger-default-url: true
    use-root-path: true
    try-it-out-enabled: true
  cache:
    disabled: true
  show-actuator: true
  writer-with-default-pretty-printer: true
  show-login-endpoint: true
  remove-broken-reference-definitions: true
  group-configs:
    - group: "render-manager"
      display-name: "Render Manager"
      paths-to-match: /api/**
    - group: "actuator"
      display-name: "Actuator"
      paths-to-match: /rest/actuator/**

mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  type-handlers-package: com.nnepoch.rendermanager.biz.entity.handler
