package com.nnepoch.rendermanager.domain.shared.specification;

/**
 * 规约模式基类
 * <AUTHOR> Migration
 */
public interface Specification<T> {
    
    /**
     * 检查对象是否满足规约
     */
    boolean isSatisfiedBy(T candidate);
    
    /**
     * 与操作
     */
    default Specification<T> and(Specification<T> other) {
        return new AndSpecification<>(this, other);
    }
    
    /**
     * 或操作
     */
    default Specification<T> or(Specification<T> other) {
        return new OrSpecification<>(this, other);
    }
    
    /**
     * 非操作
     */
    default Specification<T> not() {
        return new NotSpecification<>(this);
    }
}

/**
 * 与规约
 */
class AndSpecification<T> implements Specification<T> {
    private final Specification<T> left;
    private final Specification<T> right;
    
    public AndSpecification(Specification<T> left, Specification<T> right) {
        this.left = left;
        this.right = right;
    }
    
    @Override
    public boolean isSatisfiedBy(T candidate) {
        return left.isSatisfiedBy(candidate) && right.isSatisfiedBy(candidate);
    }
}

/**
 * 或规约
 */
class OrSpecification<T> implements Specification<T> {
    private final Specification<T> left;
    private final Specification<T> right;
    
    public OrSpecification(Specification<T> left, Specification<T> right) {
        this.left = left;
        this.right = right;
    }
    
    @Override
    public boolean isSatisfiedBy(T candidate) {
        return left.isSatisfiedBy(candidate) || right.isSatisfiedBy(candidate);
    }
}

/**
 * 非规约
 */
class NotSpecification<T> implements Specification<T> {
    private final Specification<T> specification;
    
    public NotSpecification(Specification<T> specification) {
        this.specification = specification;
    }
    
    @Override
    public boolean isSatisfiedBy(T candidate) {
        return !specification.isSatisfiedBy(candidate);
    }
}
