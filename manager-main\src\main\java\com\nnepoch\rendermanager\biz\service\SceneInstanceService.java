package com.nnepoch.rendermanager.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.nnepoch.rendermanager.api.dto.request.SceneInstanceQueryDTO;
import com.nnepoch.rendermanager.api.dto.request.SceneStartOptionDTO;
import com.nnepoch.rendermanager.api.dto.request.SceneStopOptionDTO;
import com.nnepoch.rendermanager.api.dto.response.NodeResDTO;
import com.nnepoch.rendermanager.api.dto.response.SceneInstanceResDTO;
import com.nnepoch.rendermanager.api.dto.response.base.ActionResDTO;
import com.nnepoch.rendermanager.api.dto.response.base.PageResDTO;
import com.nnepoch.rendermanager.biz.dto.mq.SceneInfoMQDTO;
import com.nnepoch.rendermanager.biz.dto.mq.SceneProcessMQDTO;
import com.nnepoch.rendermanager.biz.entity.SceneEntity;
import com.nnepoch.rendermanager.biz.entity.SceneInstanceEntity;

import javax.annotation.Nullable;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR> Che 2024/8/8
 */
public interface SceneInstanceService extends IService<SceneInstanceEntity> {
    PageResDTO<SceneInstanceResDTO> getList(SceneInstanceQueryDTO queryDTO);

    ActionResDTO<Void> freeInstance(Long id);

    ActionResDTO<Void> restartInstance(String sessionId, SceneStartOptionDTO options);

    SceneInstanceEntity getBySessionId(String hashId);

    void syncSceneInstance(SceneEntity sceneEntity, NodeResDTO node, SceneInfoMQDTO sceneInfo, List<SceneProcessMQDTO> sceneProcesses, @Nullable SceneStartOptionDTO startOptions) throws IOException;

    void removeSceneInstance(NodeResDTO node, SceneStopOptionDTO options);
}
