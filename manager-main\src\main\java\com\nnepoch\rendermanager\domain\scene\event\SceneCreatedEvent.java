package com.nnepoch.rendermanager.domain.scene.event;

import com.nnepoch.rendermanager.domain.shared.event.DomainEvent;
import com.nnepoch.rendermanager.domain.shared.valueobject.SceneId;
import lombok.Getter;

/**
 * 场景创建事件
 * <AUTHOR> Architect
 */
@Getter
public class SceneCreatedEvent extends DomainEvent {
    private final SceneId sceneId;
    private final String name;
    private final String version;
    
    public SceneCreatedEvent(SceneId sceneId, String name, String version) {
        super();
        this.sceneId = sceneId;
        this.name = name;
        this.version = version;
    }
    
    @Override
    public String getEventType() {
        return "SceneCreated";
    }
}
