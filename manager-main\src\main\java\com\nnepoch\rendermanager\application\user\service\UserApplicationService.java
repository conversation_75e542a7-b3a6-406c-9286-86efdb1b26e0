package com.nnepoch.rendermanager.application.user.service;

import com.nnepoch.rendermanager.application.user.command.CreateUserCommand;
import com.nnepoch.rendermanager.application.user.dto.UserDTO;
import com.nnepoch.rendermanager.domain.shared.valueobject.UserId;
import com.nnepoch.rendermanager.domain.user.aggregate.User;
import com.nnepoch.rendermanager.domain.user.repository.UserRepository;
import com.nnepoch.rendermanager.domain.user.service.UserDomainService;
import com.nnepoch.rendermanager.domain.user.valueobject.UserProfile;
import lombok.RequiredArgsConstructor;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

/**
 * 用户应用服务
 * <AUTHOR> Migration
 */
@Service
@RequiredArgsConstructor
@Transactional
public class UserApplicationService {
    
    private final UserRepository userRepository;
    private final UserDomainService userDomainService;
    private final PasswordEncoder passwordEncoder;
    
    /**
     * 创建用户
     */
    public UserDTO createUser(CreateUserCommand command) {
        // 1. 验证业务规则
        userDomainService.validateUserCreation(command.getUsername(), command.getEmail(), command.getMobile());
        
        // 2. 生成密码
        String password = command.getPassword() != null ? command.getPassword() : generateRandomPassword();
        String passwordHash = passwordEncoder.encode(password);
        
        // 3. 创建用户档案
        UserProfile profile = new UserProfile(command.getUsername(), command.getEmail(), command.getMobile());
        
        // 4. 创建用户聚合
        UserId userId = UserId.of(generateUserId());
        User user = User.create(userId, profile, passwordHash, command.getRole());
        
        // 5. 保存用户
        userRepository.save(user);
        
        // 6. 返回DTO
        UserDTO userDTO = UserDTO.fromDomain(user);
        userDTO.setRawPassword(password); // 仅在创建时返回明文密码
        return userDTO;
    }
    
    /**
     * 用户登录
     */
    public UserDTO login(String username, String password) {
        // 1. 查找用户
        Optional<User> userOpt = userRepository.findByUsername(username);
        if (userOpt.isEmpty()) {
            throw new IllegalArgumentException("用户不存在");
        }
        
        User user = userOpt.get();
        
        // 2. 验证密码
        if (!passwordEncoder.matches(password, user.getPasswordHash())) {
            throw new IllegalArgumentException("密码错误");
        }
        
        // 3. 执行登录
        user.login();
        
        // 4. 保存用户状态
        userRepository.save(user);
        
        return UserDTO.fromDomain(user);
    }
    
    /**
     * 根据ID获取用户
     */
    @Transactional(readOnly = true)
    public Optional<UserDTO> getUserById(Long id) {
        return userRepository.findById(UserId.of(id))
                .map(UserDTO::fromDomain);
    }
    
    /**
     * 封禁用户
     */
    public void banUser(Long id) {
        User user = userRepository.findById(UserId.of(id))
                .orElseThrow(() -> new IllegalArgumentException("用户不存在"));
        
        user.ban();
        userRepository.save(user);
    }
    
    /**
     * 解封用户
     */
    public void unbanUser(Long id) {
        User user = userRepository.findById(UserId.of(id))
                .orElseThrow(() -> new IllegalArgumentException("用户不存在"));
        
        user.unban();
        userRepository.save(user);
    }
    
    private String generateRandomPassword() {
        // 生成8位随机密码的逻辑
        return "temp123456"; // 简化实现
    }
    
    private Long generateUserId() {
        // 生成用户ID的逻辑
        return System.currentTimeMillis(); // 简化实现
    }
}
