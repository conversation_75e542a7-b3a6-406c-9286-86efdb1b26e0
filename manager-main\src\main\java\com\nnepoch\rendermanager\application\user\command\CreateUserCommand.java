package com.nnepoch.rendermanager.application.user.command;

import com.nnepoch.rendermanager.api.enums.UserRoleEnum;
import lombok.Data;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

/**
 * 创建用户命令
 * <AUTHOR> Migration
 */
@Data
public class CreateUserCommand {
    
    @NotBlank(message = "用户名不能为空")
    private String username;
    
    @NotBlank(message = "邮箱不能为空")
    @Email(message = "邮箱格式不正确")
    private String email;
    
    @NotBlank(message = "手机号不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String mobile;
    
    @NotNull(message = "角色不能为空")
    private UserRoleEnum role;
    
    private String password; // 可选，如果为空则自动生成
}
