package com.nnepoch.rendermanager.api.dto.request;

import com.nnepoch.rendermanager.api.enums.SceneIndustryEnum;
import com.nnepoch.rendermanager.api.enums.SceneStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR> 2024/6/18
 */
@Data
@Schema(description = "场景请求DTO")
public class SceneCreateDTO {
    private String name;
    private String thumb;
    private SceneStatusEnum status;
    private SceneIndustryEnum industry;
    private String link;
    private Boolean isOfficial;
    private String intro;
    private Date viewedAt;
}
