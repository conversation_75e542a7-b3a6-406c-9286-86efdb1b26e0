package com.nnepoch.rendermanager.infrastructure.projection;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 投影处理器注解
 * <AUTHOR> Architect
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface ProjectionHandler {
    /**
     * 处理的事件类型
     */
    Class<?>[] events() default {};
    
    /**
     * 投影名称
     */
    String projection() default "";
    
    /**
     * 是否异步处理
     */
    boolean async() default true;
    
    /**
     * 重试次数
     */
    int retryCount() default 3;
}
