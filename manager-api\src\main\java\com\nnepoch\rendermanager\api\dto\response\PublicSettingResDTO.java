package com.nnepoch.rendermanager.api.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> 2024/6/24
 */
@Data
@Schema(description = "公共设置响应DTO")
@Component
public class PublicSettingResDTO {
    @Schema
    private String name = "云渲染管理系统";

    @Schema(description = "OSS域名")
    @Value("${app.minio.url}")
    private String ossHost;
}
