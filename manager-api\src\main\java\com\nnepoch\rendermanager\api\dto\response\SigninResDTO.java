package com.nnepoch.rendermanager.api.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> 2024/6/20
 */

@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "登录响应DTO")
public class SigninResDTO extends UserResDTO {
    private String accessToken;

    public static SigninResDTO from(UserResDTO userResDTO) {
        SigninResDTO resDTO = new SigninResDTO();
        resDTO.setId(userResDTO.getId());
        resDTO.setUsername(userResDTO.getUsername());
        resDTO.setRole(userResDTO.getRole());
        resDTO.setEmail(userResDTO.getEmail());
        resDTO.setMobile(userResDTO.getMobile());
        resDTO.setAvatar(userResDTO.getAvatar());
        resDTO.setIsBanned(userResDTO.getIsBanned());
        resDTO.setRawPassword(userResDTO.getRawPassword());
        resDTO.setLoginAt(userResDTO.getLoginAt());
        resDTO.setCreatedAt(userResDTO.getCreatedAt());
        resDTO.setUpdatedAt(userResDTO.getUpdatedAt());
        resDTO.setDeletedAt(userResDTO.getDeletedAt());
        return resDTO;
    }
}
