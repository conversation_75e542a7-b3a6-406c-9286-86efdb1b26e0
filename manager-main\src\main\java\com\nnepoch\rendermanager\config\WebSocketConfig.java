package com.nnepoch.rendermanager.config;

import com.nnepoch.rendermanager.biz.service.*;
import com.nnepoch.rendermanager.biz.ws.SceneControlChannel;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.socket.config.annotation.EnableWebSocket;
import org.springframework.web.socket.config.annotation.WebSocketConfigurer;
import org.springframework.web.socket.config.annotation.WebSocketHandlerRegistry;

/**
 * <AUTHOR> <PERSON>e 2024/7/2
 */
@Configuration
@EnableWebSocket
@RequiredArgsConstructor
public class WebSocketConfig implements WebSocketConfigurer {
    private final JwtService jwtService;
    private final UserService userService;
    private final SceneService sceneService;
    private final SceneExperienceService sceneExperienceService;
    private final SceneInstanceService sceneInstanceService;

    @Override
    public void registerWebSocketHandlers(WebSocketHandlerRegistry registry) {
        registry.addHandler(new SceneControlChannel(jwtService, userService, sceneService, sceneExperienceService, sceneInstanceService), "/api/ws")
            .setAllowedOrigins("*");
    }
}
