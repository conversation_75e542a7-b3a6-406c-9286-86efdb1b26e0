package com.nnepoch.rendermanager.domain.user.repository;

import com.nnepoch.rendermanager.api.enums.UserRoleEnum;
import com.nnepoch.rendermanager.domain.shared.repository.Repository;
import com.nnepoch.rendermanager.domain.shared.valueobject.UserId;
import com.nnepoch.rendermanager.domain.user.aggregate.User;

import java.util.List;
import java.util.Optional;

/**
 * 用户仓储接口
 * <AUTHOR> Migration
 */
public interface UserRepository extends Repository<User, UserId> {
    
    /**
     * 根据用户名查找用户
     */
    Optional<User> findByUsername(String username);
    
    /**
     * 根据手机号查找用户
     */
    Optional<User> findByMobile(String mobile);
    
    /**
     * 根据邮箱查找用户
     */
    Optional<User> findByEmail(String email);
    
    /**
     * 根据角色查找用户列表
     */
    List<User> findByRole(UserRoleEnum role);
    
    /**
     * 检查用户名是否存在
     */
    boolean existsByUsername(String username);
    
    /**
     * 检查手机号是否存在
     */
    boolean existsByMobile(String mobile);
    
    /**
     * 检查邮箱是否存在
     */
    boolean existsByEmail(String email);
}
