package com.nnepoch.rendermanager.utils;

import com.nnepoch.rendermanager.exception.SignatureException;
import org.apache.commons.codec.binary.Hex;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.Map;
import java.util.TreeMap;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

public class SignatureUtil {
    private static final String ALGORITHM = "HmacSHA256";
    public static final String CLIENT_ID_PARAM = "clientId";
    public static final String SIGN_PARAM = "sign";
    public static final String TIMESTAMP_PARAM = "timestamp";
    public static final String NONCE_PARAM = "nonce";
    private static final long MAX_TIME_DIFF = 1000 * 60 * 5;

    private static final ConcurrentHashMap<String, Long> USED_NONCES = new ConcurrentHashMap<>();
    private static final ScheduledExecutorService CLEANUP_EXECUTOR = new ScheduledThreadPoolExecutor(1);

    static {
        CLEANUP_EXECUTOR.scheduleAtFixedRate(() -> {
            long currentTime = System.currentTimeMillis();
            USED_NONCES.entrySet().removeIf(entry -> currentTime - entry.getValue() > MAX_TIME_DIFF);
        }, 5, 5, TimeUnit.MINUTES);
    }

    public static String generateSignature(Map<String, String> params, String appSecret) throws SignatureException {
        TreeMap<String, String> sortedParams = new TreeMap<>(params);

        String signStr = sortedParams.entrySet().stream()
            .filter(entry -> !SIGN_PARAM.equals(entry.getKey()))
            .map(entry -> entry.getKey() + "=" + entry.getValue())
            .collect(Collectors.joining("&"));

        try {
            Mac hmac = Mac.getInstance(ALGORITHM);
            SecretKeySpec keySpec = new SecretKeySpec(appSecret.getBytes(StandardCharsets.UTF_8), ALGORITHM);
            hmac.init(keySpec);

            byte[] hash = hmac.doFinal(signStr.getBytes(StandardCharsets.UTF_8));

            return Hex.encodeHexString(hash);
        } catch (NoSuchAlgorithmException | InvalidKeyException e) {
            throw new SignatureException("生成签名失败:" + e.getMessage());
        }
    }

    public static void verifySignature(Map<String, String> params, String secretKey) throws SignatureException {
        // 1. 验证参数是否完整
        if (!params.containsKey(SIGN_PARAM) ||
            !params.containsKey(TIMESTAMP_PARAM) ||
            !params.containsKey(NONCE_PARAM) ||
            !params.containsKey(CLIENT_ID_PARAM)) {
            throw new SignatureException("缺少必要的签名参数");
        }

        // 2. 验证时间戳是否有效
//        long timestamp = Long.parseLong(params.get(TIMESTAMP_PARAM));
//        long currentTime = System.currentTimeMillis();
//        if (Math.abs(currentTime - timestamp) > MAX_TIME_DIFF) {
//            throw new SignatureException("请求已过期");
//        }

        // 3. 验证 nonce 是否重复
//        String nonce = params.get(NONCE_PARAM);
//        if (USED_NONCES.putIfAbsent(nonce, currentTime) != null) {
//            throw new SignatureException("重复的请求");
//        }

        // 4. 获取传入的签名
        String providedSign = params.get(SIGN_PARAM);

        // 5. 生成签名进行对比
        String calculatedSign = generateSignature(params, secretKey);
        if (!providedSign.equals(calculatedSign)) {
            throw new SignatureException("签名验证失败", Map.of(SIGN_PARAM, calculatedSign));
        }
    }
}
