package com.nnepoch.rendermanager.biz.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.nnepoch.rendermanager.api.dto.response.SimpleUserResDTO;
import lombok.Data;

import java.sql.Timestamp;

/**
 * <AUTHOR> 2024/7/1
 */
@Data
@TableName("scene_instance")
public class SceneInstanceEntity {
    @TableId(type = IdType.ASSIGN_ID)
    @OrderBy(sort = 10)
    private Long id;

    private Long sceneId;

    private Long nodeId;

    private String sessionId;

    private String displayName;

    private String programName;

    private String exeName;

    private String version;

    private String resolution;

    private Short fps;

    private Integer bitrate;

    private String appDir;

    private String exe;

    private Integer processId;

    private Timestamp startTime;

    private Integer runTime;

    private Long memory;

    private Float cpuUsage;

    private Long ownedBy;

    @TableField(exist = false)
    private SimpleUserResDTO owner;

    private Long sceneExperienceId;
}
