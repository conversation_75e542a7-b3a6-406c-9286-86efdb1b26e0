package com.nnepoch.rendermanager.api.dto.request;

import com.nnepoch.rendermanager.api.dto.request.base.PageQueryDTO;
import com.nnepoch.rendermanager.api.enums.SceneIndustryEnum;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> <PERSON> 2024/6/13
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "场景查询参数")
public class SceneQueryDTO extends PageQueryDTO {
    @Parameter(description = "是否官方")
    @NotNull
    private Boolean isOfficial = true;

    @Parameter(description = "场景名称")
    private String name;

    @Parameter(description = "行业")
    private SceneIndustryEnum industry;

    @Parameter(description = "节点")
    private Long nodeId;
}
