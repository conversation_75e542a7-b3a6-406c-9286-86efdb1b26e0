package com.nnepoch.rendermanager.biz.mq;

import cn.hutool.core.util.ObjectUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.nnepoch.rendermanager.api.dto.request.SceneStartOptionDTO;
import com.nnepoch.rendermanager.api.dto.request.SceneStopOptionDTO;
import com.nnepoch.rendermanager.api.dto.response.NodeResDTO;
import com.nnepoch.rendermanager.biz.dto.mq.*;
import com.nnepoch.rendermanager.biz.entity.SceneEntity;
import com.nnepoch.rendermanager.biz.service.NodeService;
import com.nnepoch.rendermanager.biz.service.SceneInstanceService;
import com.nnepoch.rendermanager.biz.service.SceneService;
import com.nnepoch.rendermanager.biz.service.SseService;
import com.nnepoch.rendermanager.biz.ws.SceneControlChannel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

import static com.nnepoch.rendermanager.biz.mq.NodeListener.TRIGGER;

/**
 * <AUTHOR> Che 2024/8/16
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class NodeListenerHandler {
    private final NodeService nodeService;
    private final SceneService sceneService;
    private final SseService sseService;
    private final SceneInstanceService sceneInstanceService;
    private final SceneControlChannel sceneControlChannel;

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void handleNodeRegister(ObjectMapper mapper, Object dataJson) {
        NodeInfoMQDTO nodeReqDTO = mapper.convertValue(dataJson, NodeInfoMQDTO.class);
        nodeService.syncNode(nodeReqDTO);
    }

    public void handleNodeUpdate(ObjectMapper mapper, Object dataJson) {
        NodeInfoMQDTO nodeReqDTO = mapper.convertValue(dataJson, NodeInfoMQDTO.class);
        nodeService.syncNode(nodeReqDTO);
    }

    public void handleNodeMetric(ObjectMapper mapper, Object dataJson, String nodeSn, String frequency) {
        NodeMetricMQDTO metric = mapper.convertValue(dataJson, NodeMetricMQDTO.class);
        CompletableFuture.runAsync(() -> sseService.sendNodeMetric(nodeSn, metric, frequency));
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void handleSceneList(ObjectMapper mapper, Object dataJson, String nodeSn) throws IOException {
        List<SceneInfoMQDTO> sceneList = mapper.convertValue(dataJson, new TypeReference<>() {
        });

        NodeResDTO node = nodeService.getNodeBySn(nodeSn);

        if (ObjectUtil.isNotEmpty(sceneList)) {
            for (SceneInfoMQDTO scene : sceneList) {
                SceneEntity sceneEntity = sceneService.syncScene(scene, node);
                sceneInstanceService.syncSceneInstance(sceneEntity, node, scene, scene.getProcesses(), null);
            }
        }
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void handleSceneUpdate(ObjectMapper mapper, Object dataJson, String nodeSn) throws IOException {
        SceneInfoMQDTO sceneInfo = mapper.convertValue(dataJson, SceneInfoMQDTO.class);
        NodeResDTO node = nodeService.getNodeBySn(nodeSn);
        SceneEntity sceneEntity = sceneService.syncScene(sceneInfo, node);
        sceneInstanceService.syncSceneInstance(sceneEntity, node, sceneInfo, sceneInfo.getProcesses(), null);
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void handleSceneStart(ObjectMapper mapper, Object dataJson) throws IOException {
        SceneCallbackMQDTO<SceneStartOptionDTO> callback = mapper.convertValue(dataJson, new TypeReference<>() {
        });
        NodeInfoMQDTO nodeInfo = callback.getNode();
        SceneInfoMQDTO sceneInfo = callback.getScene();
        SceneProcessMQDTO sceneProcess = callback.getInstance();
        SceneStartOptionDTO options = callback.getOptions();

        NodeResDTO node = nodeService.syncNode(nodeInfo);
        SceneEntity scene = sceneService.syncScene(sceneInfo, node);

        sceneInstanceService.syncSceneInstance(scene, node, sceneInfo, List.of(sceneProcess), options);

        if (options != null && options.getSessionId() != null) {
            Map<String, Object> data = new HashMap<>();
            if (options.getIsRestart()) {
                data.put(TRIGGER, "scene.restart");
            } else {
                data.put(TRIGGER, "scene.start");
            }
            data.put("success", true);
            data.put("data", Map.of(
                "centerHost", sceneInfo.getCenterConfig().getHost(),
                "centerToken", sceneInfo.getCenterConfig().getToken(),
                "sceneName", sceneInfo.getCenterConfig().getSceneName()
            ));

            sceneControlChannel.sendControlCallback(options.getSessionId(), data);
        }
    }

    public void handleSceneStop(ObjectMapper mapper, Object dataJson) throws IOException {
        SceneCallbackMQDTO<SceneStopOptionDTO> callback = mapper.convertValue(dataJson, new TypeReference<>() {
        });
        NodeInfoMQDTO nodeInfo = callback.getNode();
        SceneStopOptionDTO options = callback.getOptions();

        NodeResDTO node = nodeService.getNodeBySn(nodeInfo.getSn());

        sceneInstanceService.removeSceneInstance(node, options);

        if (options.getSessionId() != null) {
            Map<String, Object> data = new HashMap<>();
            data.put(TRIGGER, "scene.stop");
            data.put("success", true);
            data.put("data", Map.of(
                "nodeId", node.getId(),
                "nodeSn", nodeInfo.getSn(),
                "processId", options.getProcessId()
            ));

            sceneControlChannel.sendControlCallback(options.getSessionId(), data);
        }
    }
}
