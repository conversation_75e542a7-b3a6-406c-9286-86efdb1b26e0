package com.nnepoch.rendermanager.controller;

import cn.hutool.core.bean.BeanUtil;
import com.nnepoch.rendermanager.api.dto.request.NodeQueryDTO;
import com.nnepoch.rendermanager.api.dto.request.SceneQueryDTO;
import com.nnepoch.rendermanager.api.dto.request.SignatureReqDTO;
import com.nnepoch.rendermanager.api.dto.response.NodeResDTO;
import com.nnepoch.rendermanager.api.dto.response.OpenapiSceneResDTO;
import com.nnepoch.rendermanager.api.dto.response.SceneResDTO;
import com.nnepoch.rendermanager.api.dto.response.base.PageResDTO;
import com.nnepoch.rendermanager.biz.service.NodeService;
import com.nnepoch.rendermanager.biz.service.SceneService;
import com.nnepoch.rendermanager.config.properties.AppOpenApiProperties;
import com.nnepoch.rendermanager.utils.SignatureUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequiredArgsConstructor
@Tag(name = "Openapi", description = "Openapi相关接口")
public class OpenapiController {
    private final AppOpenApiProperties appOpenApiProperties;
    private final SceneService sceneService;
    private final NodeService nodeService;

    @GetMapping("/open/signature")
    @Operation(summary = "获取签名")
    @ApiResponse(responseCode = "200", description = "成功获取签名")
    public Map<String, String> get(@ParameterObject SignatureReqDTO signReq) {
        Map<String, String> params = new HashMap<>();
        params.put(SignatureUtil.CLIENT_ID_PARAM, signReq.getClientId());
        params.put(SignatureUtil.TIMESTAMP_PARAM, String.valueOf(signReq.getTimestamp()));
        params.put(SignatureUtil.NONCE_PARAM, signReq.getNonce());
        params.put(SignatureUtil.SIGN_PARAM, signReq.getSign());

        String signature = SignatureUtil.generateSignature(params, this.appOpenApiProperties.getClientSecret());
        return Map.of("sign", signature);
    }

    @GetMapping("/open/api/v1/scenes")
    @Operation(summary = "获取场景列表", description = "根据查询条件获取场景列表")
    @ApiResponse(responseCode = "200", description = "场景列表")
    public PageResDTO<SceneResDTO> getSceneList(@ParameterObject SceneQueryDTO queryDTO) {
        return sceneService.getSceneList(queryDTO);
    }

    @GetMapping("/open/api/v1/scenes/{id}")
    @Operation(summary = "获取场景详情", description = "根据场景ID获取场景详情, 返回ws连接信息")
    @ApiResponse(responseCode = "200", description = "场景详情")
    public OpenapiSceneResDTO getSceneInfo(@PathVariable("id") Long id, HttpServletRequest request) {
        String accessToken = (String) request.getAttribute("accessToken");
        SceneResDTO scene = sceneService.getSceneInfo(id);
        OpenapiSceneResDTO openapiScene = new OpenapiSceneResDTO();
        BeanUtil.copyProperties(scene, openapiScene);
        OpenapiSceneResDTO.WebSocketConfigDTO webSocketConfig = new OpenapiSceneResDTO.WebSocketConfigDTO();
        webSocketConfig.setAccessToken(accessToken);
        openapiScene.setWebSocketConfig(webSocketConfig);
        return openapiScene;
    }

    @GetMapping("/open/api/v1/nodes")
    @Operation(summary = "获取节点列表", description = "获取节点列表")
    @ApiResponse(responseCode = "200", description = "节点列表")
    public PageResDTO<NodeResDTO> getNodeList(@ParameterObject NodeQueryDTO queryDTO) {
        return nodeService.getNodeList(queryDTO);
    }
}
