package com.nnepoch.rendermanager.domain.user.event;

import com.nnepoch.rendermanager.api.enums.UserRoleEnum;
import com.nnepoch.rendermanager.domain.shared.event.DomainEvent;
import com.nnepoch.rendermanager.domain.shared.valueobject.UserId;
import lombok.Getter;

/**
 * 用户角色变更事件
 * <AUTHOR> Migration
 */
@Getter
public class UserRoleChangedEvent extends DomainEvent {
    private final UserId userId;
    private final UserRoleEnum oldRole;
    private final UserRoleEnum newRole;
    
    public UserRoleChangedEvent(UserId userId, UserRoleEnum oldRole, UserRoleEnum newRole) {
        super();
        this.userId = userId;
        this.oldRole = oldRole;
        this.newRole = newRole;
    }
    
    @Override
    public String getEventType() {
        return "UserRoleChanged";
    }
}
