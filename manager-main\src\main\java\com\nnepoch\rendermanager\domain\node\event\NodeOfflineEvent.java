package com.nnepoch.rendermanager.domain.node.event;

import com.nnepoch.rendermanager.domain.shared.event.DomainEvent;
import com.nnepoch.rendermanager.domain.shared.valueobject.NodeId;
import lombok.Getter;

/**
 * 节点下线事件
 * <AUTHOR> Migration
 */
@Getter
public class NodeOfflineEvent extends DomainEvent {
    private final NodeId nodeId;
    private final String sn;
    
    public NodeOfflineEvent(NodeId nodeId, String sn) {
        super();
        this.nodeId = nodeId;
        this.sn = sn;
    }
    
    @Override
    public String getEventType() {
        return "NodeOffline";
    }
}
