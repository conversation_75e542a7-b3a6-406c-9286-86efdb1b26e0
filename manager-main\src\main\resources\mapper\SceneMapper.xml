<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nnepoch.rendermanager.biz.mapper.SceneMapper">

    <resultMap id="BaseResultMap" type="com.nnepoch.rendermanager.api.dto.response.SceneResDTO">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="thumb" column="thumb" jdbcType="VARCHAR"/>
        <result property="version" column="version" jdbcType="VARCHAR"/>
        <result property="industry" column="industry"
                typeHandler="com.nnepoch.rendermanager.biz.entity.handler.SceneIndustryEnumTypeHandler"/>
        <result property="link" column="link" jdbcType="VARCHAR"/>
        <result property="isOfficial" column="is_official" jdbcType="BOOLEAN"/>
        <result property="isBanned" column="is_banned" jdbcType="BOOLEAN"/>
        <result property="bannedTime" column="banned_time" jdbcType="INTEGER"/>
        <result property="intro" column="intro" jdbcType="VARCHAR"/>
        <result property="changelog" column="changelog" jdbcType="VARCHAR"/>
        <result property="maxSeatCount" column="max_seat_count" jdbcType="INTEGER"/>
        <result property="viewedAt" column="viewed_at" jdbcType="TIMESTAMP"/>
        <result property="createdAt" column="created_at" jdbcType="TIMESTAMP"/>
        <result property="updatedAt" column="updated_at" jdbcType="TIMESTAMP"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="deletedAt" column="deleted_at" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="BaseSqlSegment">
        WITH si AS (SELECT scene_id,
                           COUNT(*) AS seats_total
                    FROM public.scene_instance
                    GROUP BY scene_id),
             si_used AS (SELECT scene_id,
                                COUNT(*) AS seats_taken
                         FROM public.scene_instance
                         WHERE session_id IS NOT NULL
                         GROUP BY scene_id),
             sn_seat AS (SELECT scene_id,
                                SUM(max_seat_count) AS max_seat_count
                         FROM public.scene_node
                         GROUP BY scene_id)
        SELECT DISTINCT s.id,
                        s.name,
                        CASE
                            WHEN s.thumb IS NOT NULL THEN
                                CONCAT(#{minioURL}, s.thumb)
                            ELSE NULL END                AS thumb,
                        s.version,
                        s.industry,
                        s.link,
                        s.is_official,
                        s.is_banned,
                        s.banned_time,
                        s.intro,
                        sn_seat.max_seat_count,
                        s.changelog,
                        s.viewed_at,
                        s.created_at,
                        s.updated_at,
                        COALESCE(si.seats_total, 0)      AS seats_total,
                        COALESCE(si_used.seats_taken, 0) AS seats_taken,
                        s.remark,
                        s.deleted_at
        FROM public.scene s
                 LEFT JOIN si ON si.scene_id = s.id
                 LEFT JOIN public.scene_node sn ON sn.scene_id = s.id
                 LEFT JOIN sn_seat ON sn_seat.scene_id = s.id
                 LEFT JOIN si_used ON si_used.scene_id = s.id
    </sql>

    <select id="selectScenePage" resultMap="BaseResultMap">
        <include refid="BaseSqlSegment"/>
        ${ew.customSqlSegment}
    </select>

    <select id="selectSceneById" resultMap="BaseResultMap">
        <include refid="BaseSqlSegment"/>
        WHERE s.id = #{id}
    </select>
</mapper>
