package com.nnepoch.rendermanager.biz.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nnepoch.rendermanager.api.enums.SceneIndustryEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <AUTHOR> 2024/6/17
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("scene")
public class SceneEntity extends BaseEntity {
    private String name;

    private String thumb;

    private String version;

    private String description;

    private SceneIndustryEnum industry;

    private String link;

    @TableField(exist = false)
    private Integer seatsTaken;

    @TableField(exist = false)
    private Integer seatsTotal;

    @TableField(exist = false)
    private Integer maxSeatCount;

    private Boolean isOfficial;

    private Boolean isBanned;

    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private Integer bannedTime;

    private String intro;

    private String changelog;

    private Date viewedAt;
}
