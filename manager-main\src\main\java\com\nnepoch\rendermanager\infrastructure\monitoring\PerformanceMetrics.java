package com.nnepoch.rendermanager.infrastructure.monitoring;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.Gauge;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * DDD性能指标收集器
 * 收集聚合、事件、缓存等关键性能指标
 * <AUTHOR> Architect
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class PerformanceMetrics {
    
    private final MeterRegistry meterRegistry;
    
    // 计数器缓存
    private final ConcurrentHashMap<String, Counter> counters = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, Timer> timers = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, AtomicLong> gaugeValues = new ConcurrentHashMap<>();
    
    /**
     * 记录聚合操作
     */
    public void recordAggregateOperation(String aggregateType, String operation, Duration duration) {
        // 操作计数
        getCounter("aggregate.operations", 
            "type", aggregateType, 
            "operation", operation).increment();
        
        // 操作耗时
        getTimer("aggregate.operation.duration", 
            "type", aggregateType, 
            "operation", operation).record(duration);
    }
    
    /**
     * 记录事件处理
     */
    public void recordEventProcessing(String eventType, String handler, Duration duration, boolean success) {
        // 事件处理计数
        getCounter("event.processing", 
            "type", eventType, 
            "handler", handler, 
            "status", success ? "success" : "failure").increment();
        
        // 事件处理耗时
        getTimer("event.processing.duration", 
            "type", eventType, 
            "handler", handler).record(duration);
    }
    
    /**
     * 记录缓存操作
     */
    public void recordCacheOperation(String cacheType, String operation, boolean hit) {
        // 缓存操作计数
        getCounter("cache.operations", 
            "type", cacheType, 
            "operation", operation, 
            "result", hit ? "hit" : "miss").increment();
    }
    
    /**
     * 记录数据库操作
     */
    public void recordDatabaseOperation(String repository, String operation, Duration duration) {
        // 数据库操作计数
        getCounter("database.operations", 
            "repository", repository, 
            "operation", operation).increment();
        
        // 数据库操作耗时
        getTimer("database.operation.duration", 
            "repository", repository, 
            "operation", operation).record(duration);
    }
    
    /**
     * 记录API调用
     */
    public void recordApiCall(String endpoint, String method, int statusCode, Duration duration) {
        // API调用计数
        getCounter("api.calls", 
            "endpoint", endpoint, 
            "method", method, 
            "status", String.valueOf(statusCode)).increment();
        
        // API响应时间
        getTimer("api.response.duration", 
            "endpoint", endpoint, 
            "method", method).record(duration);
    }
    
    /**
     * 记录业务指标
     */
    public void recordBusinessMetric(String metric, String... tags) {
        getCounter("business.metrics", tags).increment();
    }
    
    /**
     * 设置仪表盘指标
     */
    public void setGaugeValue(String name, long value, String... tags) {
        String key = buildKey(name, tags);
        AtomicLong gaugeValue = gaugeValues.computeIfAbsent(key, k -> {
            AtomicLong atomicValue = new AtomicLong(value);
            Gauge.builder(name, atomicValue, AtomicLong::doubleValue)
                .tags(tags)
                .register(meterRegistry);
            return atomicValue;
        });
        gaugeValue.set(value);
    }
    
    /**
     * 记录自定义指标
     */
    public Timer.Sample startTimer(String name, String... tags) {
        return Timer.start(meterRegistry);
    }
    
    public void stopTimer(Timer.Sample sample, String name, String... tags) {
        sample.stop(getTimer(name, tags));
    }
    
    /**
     * 批量记录指标
     */
    public void recordBatch(MetricsBatch batch) {
        batch.getMetrics().forEach(metric -> {
            switch (metric.getType()) {
                case COUNTER -> getCounter(metric.getName(), metric.getTags()).increment(metric.getValue());
                case TIMER -> getTimer(metric.getName(), metric.getTags()).record(Duration.ofMillis((long) metric.getValue()));
                case GAUGE -> setGaugeValue(metric.getName(), (long) metric.getValue(), metric.getTags());
            }
        });
    }
    
    /**
     * 获取性能报告
     */
    public PerformanceReport getPerformanceReport() {
        return PerformanceReport.builder()
            .aggregateMetrics(getAggregateMetrics())
            .eventMetrics(getEventMetrics())
            .cacheMetrics(getCacheMetrics())
            .databaseMetrics(getDatabaseMetrics())
            .apiMetrics(getApiMetrics())
            .build();
    }
    
    private Counter getCounter(String name, String... tags) {
        String key = buildKey(name, tags);
        return counters.computeIfAbsent(key, k -> 
            Counter.builder(name).tags(tags).register(meterRegistry));
    }
    
    private Timer getTimer(String name, String... tags) {
        String key = buildKey(name, tags);
        return timers.computeIfAbsent(key, k -> 
            Timer.builder(name).tags(tags).register(meterRegistry));
    }
    
    private String buildKey(String name, String... tags) {
        StringBuilder key = new StringBuilder(name);
        for (int i = 0; i < tags.length; i += 2) {
            if (i + 1 < tags.length) {
                key.append(":").append(tags[i]).append("=").append(tags[i + 1]);
            }
        }
        return key.toString();
    }
    
    private AggregateMetrics getAggregateMetrics() {
        // 收集聚合相关指标
        return AggregateMetrics.builder()
            .totalOperations(getCounterValue("aggregate.operations"))
            .averageOperationTime(getTimerMean("aggregate.operation.duration"))
            .build();
    }
    
    private EventMetrics getEventMetrics() {
        // 收集事件相关指标
        return EventMetrics.builder()
            .totalEvents(getCounterValue("event.processing"))
            .averageProcessingTime(getTimerMean("event.processing.duration"))
            .successRate(calculateEventSuccessRate())
            .build();
    }
    
    private CacheMetrics getCacheMetrics() {
        // 收集缓存相关指标
        long hits = getCounterValue("cache.operations", "result", "hit");
        long misses = getCounterValue("cache.operations", "result", "miss");
        double hitRate = hits + misses > 0 ? (double) hits / (hits + misses) : 0.0;
        
        return CacheMetrics.builder()
            .hitRate(hitRate)
            .totalOperations(hits + misses)
            .build();
    }
    
    private DatabaseMetrics getDatabaseMetrics() {
        // 收集数据库相关指标
        return DatabaseMetrics.builder()
            .totalOperations(getCounterValue("database.operations"))
            .averageQueryTime(getTimerMean("database.operation.duration"))
            .build();
    }
    
    private ApiMetrics getApiMetrics() {
        // 收集API相关指标
        return ApiMetrics.builder()
            .totalRequests(getCounterValue("api.calls"))
            .averageResponseTime(getTimerMean("api.response.duration"))
            .errorRate(calculateApiErrorRate())
            .build();
    }
    
    private long getCounterValue(String name, String... tags) {
        String key = buildKey(name, tags);
        Counter counter = counters.get(key);
        return counter != null ? (long) counter.count() : 0;
    }
    
    private double getTimerMean(String name, String... tags) {
        String key = buildKey(name, tags);
        Timer timer = timers.get(key);
        return timer != null ? timer.mean(java.util.concurrent.TimeUnit.MILLISECONDS) : 0.0;
    }
    
    private double calculateEventSuccessRate() {
        long success = getCounterValue("event.processing", "status", "success");
        long failure = getCounterValue("event.processing", "status", "failure");
        return success + failure > 0 ? (double) success / (success + failure) : 1.0;
    }
    
    private double calculateApiErrorRate() {
        long total = getCounterValue("api.calls");
        long errors = getCounterValue("api.calls", "status", "500") + 
                     getCounterValue("api.calls", "status", "400");
        return total > 0 ? (double) errors / total : 0.0;
    }
}

/**
 * 指标批次
 */
@lombok.Data
@lombok.Builder
class MetricsBatch {
    private java.util.List<MetricEntry> metrics;
}

/**
 * 指标条目
 */
@lombok.Data
@lombok.Builder
class MetricEntry {
    private String name;
    private MetricType type;
    private double value;
    private String[] tags;
    
    enum MetricType {
        COUNTER, TIMER, GAUGE
    }
}

/**
 * 性能报告
 */
@lombok.Data
@lombok.Builder
class PerformanceReport {
    private AggregateMetrics aggregateMetrics;
    private EventMetrics eventMetrics;
    private CacheMetrics cacheMetrics;
    private DatabaseMetrics databaseMetrics;
    private ApiMetrics apiMetrics;
}

@lombok.Data
@lombok.Builder
class AggregateMetrics {
    private long totalOperations;
    private double averageOperationTime;
}

@lombok.Data
@lombok.Builder
class EventMetrics {
    private long totalEvents;
    private double averageProcessingTime;
    private double successRate;
}

@lombok.Data
@lombok.Builder
class CacheMetrics {
    private double hitRate;
    private long totalOperations;
}

@lombok.Data
@lombok.Builder
class DatabaseMetrics {
    private long totalOperations;
    private double averageQueryTime;
}

@lombok.Data
@lombok.Builder
class ApiMetrics {
    private long totalRequests;
    private double averageResponseTime;
    private double errorRate;
}
