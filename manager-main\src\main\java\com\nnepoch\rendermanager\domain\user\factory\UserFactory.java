package com.nnepoch.rendermanager.domain.user.factory;

import com.nnepoch.rendermanager.api.enums.UserRoleEnum;
import com.nnepoch.rendermanager.domain.shared.valueobject.UserId;
import com.nnepoch.rendermanager.domain.user.aggregate.User;
import com.nnepoch.rendermanager.domain.user.repository.UserRepository;
import com.nnepoch.rendermanager.domain.user.valueobject.UserProfile;
import lombok.RequiredArgsConstructor;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Component;

import java.security.SecureRandom;

/**
 * 用户工厂
 * <AUTHOR> Migration
 */
@Component
@RequiredArgsConstructor
public class UserFactory {
    
    private final UserRepository userRepository;
    private final PasswordEncoder passwordEncoder;
    private final SecureRandom secureRandom = new SecureRandom();
    
    /**
     * 创建普通用户
     */
    public User createMember(String username, String email, String mobile) {
        return createUser(username, email, mobile, UserRoleEnum.MEMBER, null);
    }
    
    /**
     * 创建管理员用户
     */
    public User createAdmin(String username, String email, String mobile, String password) {
        return createUser(username, email, mobile, UserRoleEnum.ADMIN, password);
    }
    
    /**
     * 创建运维用户
     */
    public User createOperator(String username, String email, String mobile) {
        return createUser(username, email, mobile, UserRoleEnum.OPERATOR, null);
    }
    
    /**
     * 从钉钉信息创建用户
     */
    public User createFromDingtalk(String dingtalkId, String name, String email, String mobile) {
        // 钉钉用户默认为普通成员
        String username = generateUsernameFromDingtalk(dingtalkId, name);
        return createUser(username, email, mobile, UserRoleEnum.MEMBER, null);
    }
    
    /**
     * 批量创建用户
     */
    public User createBatchUser(String prefix, int index, String domain) {
        String username = String.format("%s%03d", prefix, index);
        String email = String.format("%s@%s", username, domain);
        String mobile = generateMobile();
        
        return createUser(username, email, mobile, UserRoleEnum.MEMBER, null);
    }
    
    private User createUser(String username, String email, String mobile, 
                           UserRoleEnum role, String password) {
        // 1. 验证唯一性
        validateUniqueness(username, email, mobile);
        
        // 2. 生成密码
        String finalPassword = password != null ? password : generateRandomPassword();
        String passwordHash = passwordEncoder.encode(finalPassword);
        
        // 3. 创建用户档案
        UserProfile profile = new UserProfile(username, email, mobile);
        
        // 4. 生成用户ID
        UserId userId = UserId.of(generateUserId());
        
        // 5. 创建用户聚合
        User user = User.create(userId, profile, passwordHash, role);
        
        // 6. 设置原始密码 (用于返回给调用者)
        // 注意: 这里需要通过反射或其他方式设置，因为User聚合不应该暴露这个方法
        
        return user;
    }
    
    private void validateUniqueness(String username, String email, String mobile) {
        if (userRepository.existsByUsername(username)) {
            throw new IllegalArgumentException("用户名已存在: " + username);
        }
        if (userRepository.existsByEmail(email)) {
            throw new IllegalArgumentException("邮箱已存在: " + email);
        }
        if (userRepository.existsByMobile(mobile)) {
            throw new IllegalArgumentException("手机号已存在: " + mobile);
        }
    }
    
    private String generateUsernameFromDingtalk(String dingtalkId, String name) {
        // 从钉钉信息生成用户名的逻辑
        String baseUsername = name.replaceAll("[^a-zA-Z0-9]", "").toLowerCase();
        if (baseUsername.length() < 3) {
            baseUsername = "user" + dingtalkId.substring(0, Math.min(4, dingtalkId.length()));
        }
        
        // 确保用户名唯一
        String username = baseUsername;
        int suffix = 1;
        while (userRepository.existsByUsername(username)) {
            username = baseUsername + suffix++;
        }
        
        return username;
    }
    
    private String generateRandomPassword() {
        // 生成8位随机密码: 包含大小写字母和数字
        String chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
        StringBuilder password = new StringBuilder();
        
        for (int i = 0; i < 8; i++) {
            password.append(chars.charAt(secureRandom.nextInt(chars.length())));
        }
        
        return password.toString();
    }
    
    private String generateMobile() {
        // 生成随机手机号 (仅用于测试)
        return "138" + String.format("%08d", secureRandom.nextInt(100000000));
    }
    
    private Long generateUserId() {
        // 生成用户ID的逻辑 (实际项目中可能使用雪花算法等)
        return System.currentTimeMillis() + secureRandom.nextInt(1000);
    }
}
