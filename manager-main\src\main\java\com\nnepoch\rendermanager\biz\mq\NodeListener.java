package com.nnepoch.rendermanager.biz.mq;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.nnepoch.rendermanager.config.RabbitConfig;
import com.nnepoch.rendermanager.exception.BizException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR> 2024/6/23
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class NodeListener {
    public static final String TRIGGER = "trigger";
    private final ObjectMapper objectMapper = new ObjectMapper()
        .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
        .setPropertyNamingStrategy(PropertyNamingStrategies.SNAKE_CASE);
    private final NodeListenerHandler nodeListenerHandler;

    // 通过通配 routing key (agent.#) 监听节点队列
    @RabbitListener(bindings = @QueueBinding(
        value = @Queue(value = "node.queue", autoDelete = "true"),
        exchange = @Exchange(value = RabbitConfig.EXCHANGE_NAME, type = "topic"),
        key = RabbitConfig.NODE_ROUTING_KEY
    ))
    public void onNodeEvent(Message message) {
        String routingKey = message.getMessageProperties().getReceivedRoutingKey();
        String[] routingKeyTuple = routingKey.split("\\.");
        String from = routingKeyTuple[0];
        String nodeSn = routingKeyTuple[1];

        if (from.equals("cloud")) return;

        String body = new String(message.getBody());

        CompletableFuture.runAsync(() -> {
            try {
                Map<String, Object> outerJson = objectMapper.readValue(body, new TypeReference<>() {
                });

                if (!outerJson.containsKey("data") || !outerJson.containsKey(TRIGGER)) {
                    log.error("Missing data or trigger in node queue message: {}", body);
                    throw new BizException("Missing data or trigger in node queue message");
                }

                String trigger = outerJson.get(TRIGGER).toString();
                Object dataJson = objectMapper.convertValue(outerJson.get("data"), new TypeReference<>() {
                });
                Integer timestamp = (Integer) outerJson.get("timestamp");

                log.info("Routing key: {}, Trigger: {}, Timestamp: {}", routingKey, trigger, timestamp);

                switch (trigger) {
                    case "node.register":
                        nodeListenerHandler.handleNodeRegister(objectMapper, dataJson);
                        break;
                    case "node.update":
                        nodeListenerHandler.handleNodeUpdate(objectMapper, dataJson);
                        break;
                    case "node.metric.low":
                        nodeListenerHandler.handleNodeMetric(objectMapper, dataJson, nodeSn, "low");
                        break;
                    case "node.metric.medium":
                        nodeListenerHandler.handleNodeMetric(objectMapper, dataJson, nodeSn, "medium");
                        break;
                    case "node.metric.high":
                        nodeListenerHandler.handleNodeMetric(objectMapper, dataJson, nodeSn, "high");
                        break;
                    case "scene.list":
                        nodeListenerHandler.handleSceneList(objectMapper, dataJson, nodeSn);
                        break;
                    case "scene.update":
                        nodeListenerHandler.handleSceneUpdate(objectMapper, dataJson, nodeSn);
                        break;
                    case "scene.start.callback":
                        nodeListenerHandler.handleSceneStart(objectMapper, dataJson);
                        break;
                    case "scene.stop.callback":
                        nodeListenerHandler.handleSceneStop(objectMapper, dataJson);
                        break;
                    default:
                        log.error("Unsupported trigger in node queue Routing key: {},  body: {}", routingKey, body);
                        break;
                }

            } catch (JsonProcessingException e) {
                log.error("Failed to parse node queue message: {}", body, e);
                throw new BizException("JsonProcessingException", "Failed to parse node queue message", e);
            } catch (Exception e) {
                log.error("Unexpected error occurred while node queue Routing key: {}, message: {}", routingKey, body, e);
                throw new BizException(e.getClass().getSimpleName(), "Unexpected error occurred while node queue message", e);
            }
        });
    }
}
