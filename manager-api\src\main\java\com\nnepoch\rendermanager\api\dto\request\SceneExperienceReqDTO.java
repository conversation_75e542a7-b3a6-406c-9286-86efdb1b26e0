package com.nnepoch.rendermanager.api.dto.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.nnepoch.rendermanager.api.enums.SceneExperienceStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR> 2024/6/18
 */
@Data
@Schema(description = "场景体验请求对象")
public class SceneExperienceReqDTO {
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long sceneId;
    private SceneExperienceStatusEnum status = SceneExperienceStatusEnum.IDLE;
    private String username;
    private String organization;
    private String mobile;
    private String email;

    @Schema(description = "体验时长，单位：分钟")
    private Integer duration;
    private Date expiredAt;
}
