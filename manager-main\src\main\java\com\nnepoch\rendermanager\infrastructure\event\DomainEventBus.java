package com.nnepoch.rendermanager.infrastructure.event;

import com.nnepoch.rendermanager.domain.shared.event.DomainEvent;
import com.nnepoch.rendermanager.infrastructure.event.store.EventStore;
import com.nnepoch.rendermanager.infrastructure.event.store.StoredEvent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * 领域事件总线
 * 负责事件的发布、存储、分发和重放
 * <AUTHOR> Architect
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class DomainEventBus {
    
    private final ApplicationEventPublisher applicationEventPublisher;
    private final EventStore eventStore;
    private final List<DomainEventHandler> eventHandlers;
    
    /**
     * 发布领域事件
     */
    @Transactional
    public void publish(DomainEvent event) {
        log.debug("Publishing domain event: {} with ID: {}", event.getEventType(), event.getEventId());
        
        // 1. 立即发布到Spring事件系统（同步处理）
        applicationEventPublisher.publishEvent(event);
        
        // 2. 存储事件到事件存储（用于事件溯源和重放）
        // 注意：这里需要聚合信息，实际实现中应该从事件中获取或作为参数传入
        // eventStore.saveEvent(event, aggregateId, aggregateType, version);
    }
    
    /**
     * 批量发布事件
     */
    @Transactional
    public void publishAll(List<DomainEvent> events) {
        events.forEach(this::publish);
    }
    
    /**
     * 异步发布事件（事务提交后）
     */
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    @Async
    public void handleAfterCommit(DomainEvent event) {
        log.debug("Handling event after transaction commit: {}", event.getEventType());
        
        // 发送到外部系统（消息队列、Webhook等）
        publishToExternalSystems(event);
    }
    
    /**
     * 重放事件
     */
    public void replayEvents(String aggregateId, String aggregateType) {
        log.info("Replaying events for aggregate: {} of type: {}", aggregateId, aggregateType);
        
        List<StoredEvent> events = eventStore.getEventsForAggregate(
            createEntityId(aggregateId), aggregateType);
        
        events.forEach(storedEvent -> {
            try {
                DomainEvent event = deserializeEvent(storedEvent);
                applicationEventPublisher.publishEvent(event);
                log.debug("Replayed event: {}", event.getEventType());
            } catch (Exception e) {
                log.error("Failed to replay event: {}", storedEvent.getEventId(), e);
            }
        });
    }
    
    /**
     * 重放指定时间范围的事件
     */
    public void replayEventsBetween(LocalDateTime start, LocalDateTime end) {
        log.info("Replaying events between {} and {}", start, end);
        
        List<StoredEvent> events = eventStore.getEventsBetween(start, end);
        
        events.forEach(storedEvent -> {
            try {
                DomainEvent event = deserializeEvent(storedEvent);
                applicationEventPublisher.publishEvent(event);
            } catch (Exception e) {
                log.error("Failed to replay event: {}", storedEvent.getEventId(), e);
            }
        });
    }
    
    /**
     * 异步处理事件
     */
    @Async("eventExecutor")
    public CompletableFuture<Void> publishAsync(DomainEvent event) {
        return CompletableFuture.runAsync(() -> {
            try {
                publish(event);
            } catch (Exception e) {
                log.error("Failed to publish event asynchronously: {}", event.getEventType(), e);
                throw new RuntimeException(e);
            }
        });
    }
    
    /**
     * 发布到外部系统
     */
    private void publishToExternalSystems(DomainEvent event) {
        try {
            // 发送到RabbitMQ
            publishToMessageQueue(event);
            
            // 发送Webhook通知
            publishToWebhooks(event);
            
            // 更新搜索索引
            updateSearchIndex(event);
            
        } catch (Exception e) {
            log.error("Failed to publish event to external systems: {}", event.getEventType(), e);
            // 可以考虑重试机制或死信队列
        }
    }
    
    private void publishToMessageQueue(DomainEvent event) {
        // 发送到RabbitMQ的实现
        log.debug("Publishing event to message queue: {}", event.getEventType());
    }
    
    private void publishToWebhooks(DomainEvent event) {
        // 发送Webhook通知的实现
        log.debug("Publishing event to webhooks: {}", event.getEventType());
    }
    
    private void updateSearchIndex(DomainEvent event) {
        // 更新搜索索引的实现
        log.debug("Updating search index for event: {}", event.getEventType());
    }
    
    private DomainEvent deserializeEvent(StoredEvent storedEvent) {
        // 反序列化事件的实现
        // 这里需要根据事件类型动态反序列化
        return null; // 简化实现
    }
    
    private com.nnepoch.rendermanager.domain.shared.valueobject.EntityId createEntityId(String id) {
        // 创建EntityId的实现
        return null; // 简化实现
    }
    
    /**
     * 获取事件统计信息
     */
    public EventStatistics getEventStatistics() {
        return EventStatistics.builder()
            .totalEvents(getTotalEventCount())
            .eventsToday(getEventCountToday())
            .eventsByType(getEventCountByType())
            .averageProcessingTime(getAverageProcessingTime())
            .build();
    }
    
    private long getTotalEventCount() {
        // 获取总事件数的实现
        return 0;
    }
    
    private long getEventCountToday() {
        // 获取今日事件数的实现
        return 0;
    }
    
    private java.util.Map<String, Long> getEventCountByType() {
        // 获取按类型分组的事件数的实现
        return java.util.Map.of();
    }
    
    private double getAverageProcessingTime() {
        // 获取平均处理时间的实现
        return 0.0;
    }
}

/**
 * 领域事件处理器接口
 */
interface DomainEventHandler {
    boolean canHandle(DomainEvent event);
    void handle(DomainEvent event);
}

/**
 * 事件统计信息
 */
@lombok.Builder
@lombok.Data
class EventStatistics {
    private long totalEvents;
    private long eventsToday;
    private java.util.Map<String, Long> eventsByType;
    private double averageProcessingTime;
}
