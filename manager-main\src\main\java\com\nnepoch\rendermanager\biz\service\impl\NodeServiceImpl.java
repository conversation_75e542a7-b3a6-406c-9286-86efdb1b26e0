package com.nnepoch.rendermanager.biz.service.impl;

import cn.hutool.core.lang.id.NanoId;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nnepoch.rendermanager.api.dto.request.NodeQueryDTO;
import com.nnepoch.rendermanager.api.dto.request.NodeReqDTO;
import com.nnepoch.rendermanager.api.dto.response.NodeResDTO;
import com.nnepoch.rendermanager.api.dto.response.base.PageResDTO;
import com.nnepoch.rendermanager.api.enums.NodeStatusEnum;
import com.nnepoch.rendermanager.api.enums.UserRoleEnum;
import com.nnepoch.rendermanager.biz.dto.mq.NodeInfoMQDTO;
import com.nnepoch.rendermanager.biz.dto.mq.NodeListenerMQDTO;
import com.nnepoch.rendermanager.biz.entity.NodeEntity;
import com.nnepoch.rendermanager.biz.entity.UserEntity;
import com.nnepoch.rendermanager.biz.mapper.NodeMapper;
import com.nnepoch.rendermanager.biz.mq.RabbitMQSender;
import com.nnepoch.rendermanager.biz.service.NodeService;
import com.nnepoch.rendermanager.config.RabbitConfig;
import com.nnepoch.rendermanager.exception.BizException;
import com.nnepoch.rendermanager.utils.SqlOrderUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;

import java.util.HashMap;

/**
 * <AUTHOR> Che 2024/6/17
 */
@RequiredArgsConstructor
@Service
public class NodeServiceImpl extends ServiceImpl<NodeMapper, NodeEntity> implements NodeService {
    private final NodeMapper nodeMapper;
    private final RabbitMQSender rabbitMQSender;

    @Override
    public PageResDTO<NodeResDTO> getNodeList(NodeQueryDTO queryDTO) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        UserEntity user = (UserEntity) authentication.getPrincipal();

        Page<NodeEntity> pageObject = new Page<>(queryDTO.getPage(), queryDTO.getSize());

        LambdaQueryWrapper<NodeEntity> wrapper = Wrappers.<NodeEntity>lambdaQuery()
            .eq(CharSequenceUtil.isNotEmpty(queryDTO.getHashId()), NodeEntity::getHashId, queryDTO.getHashId())
            .eq(CharSequenceUtil.isNotEmpty(queryDTO.getOrganization()), NodeEntity::getOrganization, queryDTO.getOrganization())
            .eq(CharSequenceUtil.isNotEmpty(queryDTO.getHostname()), NodeEntity::getHostname, queryDTO.getHostname())
            .apply("n.deleted_at IS NULL");

        if (user.getRole() != UserRoleEnum.ADMIN) {
            wrapper.apply("n.owned_by = {0}", user.getId());
        }

        if (ObjectUtil.isNotNull(queryDTO.getStatus())) {
            wrapper.eq(NodeEntity::getStatus, queryDTO.getStatus().getCode());
        }

        if (ObjectUtil.isNotNull(queryDTO.getOwnedBy())) {
            wrapper.apply("n.owned_by = {0}", queryDTO.getOwnedBy());
        }

        if (CharSequenceUtil.isNotEmpty(queryDTO.getSort())) {
            pageObject.addOrder(SqlOrderUtil.parseOrderItem(queryDTO.getSort()));
        }

        IPage<NodeResDTO> page = nodeMapper.selectNodePage(pageObject, wrapper);

        return new PageResDTO<>(
            page.getRecords(),
            new PageResDTO.Pagination(page.getCurrent(), page.getSize(), page.getTotal())
        );
    }

    @Override
    public NodeResDTO getNodeInfo(Long id) {
        NodeResDTO node = nodeMapper.selectNodeById(id);
        if (node == null) {
            throw new BizException(HttpStatus.BAD_REQUEST.name(), "节点不存在");
        }
        return node;
    }

    @Override
    public NodeResDTO getNodeBySn(String sn) {
        NodeEntity nodeEntity = nodeMapper.selectOne(Wrappers.<NodeEntity>lambdaQuery().eq(NodeEntity::getSn, sn));
        if (nodeEntity == null) {
            return null;
        }
        return toDTO(nodeEntity);
    }

    @Override
    public NodeResDTO syncNode(NodeInfoMQDTO reqDTO) {
        // 根据sn来判断是新增还是更新
        NodeEntity nodeEntity = nodeMapper.selectOne(Wrappers.<NodeEntity>lambdaQuery().eq(NodeEntity::getSn, reqDTO.getSn()));
        if (nodeEntity == null) {
            nodeEntity = toEntity(reqDTO);
            nodeEntity.setHashId(NanoId.randomNanoId(16));
            nodeMapper.insert(nodeEntity);
        } else {
            NodeEntity toUpdate = toEntity(reqDTO);
            toUpdate.setId(nodeEntity.getId());
            nodeMapper.update(toUpdate, Wrappers.<NodeEntity>lambdaQuery().eq(NodeEntity::getId, nodeEntity.getId()));

            nodeEntity = toUpdate;
        }

        return toDTO(nodeEntity);
    }

    @Override
    public NodeResDTO updateNode(Long id, NodeReqDTO reqDTO) {
        NodeEntity node = nodeMapper.selectById(id);
        if (node == null) {
            throw new BizException("节点不存在");
        }

        NodeEntity toUpdate = toEntity(reqDTO);
        toUpdate.setId(id);
        nodeMapper.updateById(toUpdate);

        if (reqDTO.getMaxSceneInstanceCount() != null) {
            rabbitMQSender.send(
                RabbitConfig.EXCHANGE_NAME,
                RabbitConfig.getCloudRoutingKey(node.getSn()),
                new NodeListenerMQDTO(
                    NodeListenerMQDTO.NodeListenerRpcActionEnum.NODE_UPDATE,
                    new NodeListenerMQDTO.NodeListenerRpcPayload<>(new HashMap<>() {
                        {
                            put("maxSceneInstanceCount", reqDTO.getMaxSceneInstanceCount());
                        }
                    })
                ).toString(),
                0
            );
        }

        return toDTO(toUpdate);
    }

    @Override
    public NodeResDTO getBestNode() {
        IPage<NodeResDTO> page = nodeMapper.selectNodePage(new Page<>(1, 20), Wrappers.<NodeEntity>lambdaQuery().eq(NodeEntity::getStatus, NodeStatusEnum.ONLINE));
        if (page.getTotal() == 0) {
            return null;
        }

        return page.getRecords().getLast();
    }

    private NodeEntity toEntity(NodeInfoMQDTO reqDTO) {
        NodeEntity entity = new NodeEntity();
        entity.setHostname(reqDTO.getHostname());
        entity.setSn((reqDTO.getSn()));
        entity.setOs(reqDTO.getOs());
        entity.setOsVersion(reqDTO.getOsVersion());
        entity.setOrganization(reqDTO.getOrganization());
        entity.setMobile(reqDTO.getMobile());
        entity.setEmail(reqDTO.getEmail());
        entity.setMaxSceneInstanceCount(reqDTO.getMaxSceneInstanceCount());
        return entity;
    }

    private NodeEntity toEntity(NodeReqDTO reqDTO) {
        NodeEntity entity = new NodeEntity();
        entity.setStatus(reqDTO.getStatus());
        entity.setEnableLogCollection(reqDTO.getEnableLogCollection());
        entity.setOrganization(reqDTO.getOrganization());
        entity.setMobile(reqDTO.getMobile());
        entity.setEmail(reqDTO.getEmail());
        entity.setOwnedBy(reqDTO.getOwnedBy());
        entity.setMaxSceneInstanceCount(reqDTO.getMaxSceneInstanceCount());
        entity.setRemark(reqDTO.getRemark());
        return entity;
    }

    private NodeResDTO toDTO(NodeEntity entity) {
        NodeResDTO resDTO = new NodeResDTO();
        resDTO.setId(entity.getId());
        resDTO.setHashId(entity.getHashId());
        resDTO.setSn(entity.getSn());
        resDTO.setOs(entity.getOs());
        resDTO.setOsVersion(entity.getOsVersion());
        resDTO.setHostname(entity.getHostname());
        resDTO.setStatus(entity.getStatus());
        resDTO.setEnableLogCollection(entity.getEnableLogCollection());
        resDTO.setSceneCount(entity.getSceneCount());
        resDTO.setMaxSceneInstanceCount(entity.getMaxSceneInstanceCount());
        resDTO.setSceneRunningCount(entity.getSceneRunningCount());
        resDTO.setSceneSeatsTaken(entity.getSceneSeatsTaken());
        resDTO.setOrganization(entity.getOrganization());
        resDTO.setMobile(entity.getMobile());
        resDTO.setEmail(entity.getEmail());
        resDTO.setOwnedBy(entity.getOwnedBy());
        resDTO.setOwner(entity.getOwner());
        resDTO.setRemark(entity.getRemark());
        resDTO.setCreatedAt(entity.getCreatedAt());
        resDTO.setUpdatedAt(entity.getUpdatedAt());
        resDTO.setDeletedAt(entity.getDeletedAt());
        return resDTO;
    }
}
