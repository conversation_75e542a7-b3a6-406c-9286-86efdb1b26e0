server {
  listen 80;
  server_name localhost;
  resolver 127.0.0.11 valid=30s;

  access_log /var/log/nginx/access.log;
  error_log /var/log/nginx/error.log;

  # Allow special characters in headers
  ignore_invalid_headers off;
  # Allow any size file to be uploaded.
  # Set to a value such as 1000m; to restrict file size to a specific value
  client_max_body_size 0;
  # Disable buffering
  proxy_buffering off;
  proxy_request_buffering off;

  keepalive_timeout  65;
  proxy_connect_timeout 300;
  proxy_send_timeout 300;
  proxy_read_timeout 600;

  gzip  on;
  gzip_min_length 2k;
  gzip_comp_level 5;
  gzip_proxied expired no-cache no-store private auth;
  gzip_types text/plain application/javascript application/json application/x-javascript text/javascript text/xml text/css application/geo+json model/gltf-binary application/octet-stream;
  gzip_disable 'MSIE[1-6]\.'
  gzip_vary on;

  root /usr/share/nginx/html;

  location / {
    add_header Cache-Control "no-cache, no-store, must-revalidate";
    add_header Pragma "no-cache";
    add_header Expires "0";
    try_files $uri $uri/ /index.html;
  }

  location /api {
    proxy_pass http://rmp-api-svc:8080;

    proxy_set_header Host $http_host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header Client-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;


    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection "upgrade";
  }

  location ^~ /swagger {
    proxy_pass http://rmp-api-svc:8080;

    proxy_set_header Host $http_host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header Client-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
  }

  location ^~ /oss/ {
    proxy_pass http://minio-api-svc.middleware:9000/;

    proxy_set_header Host $http_host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
  }

  location /avatar/ {
    proxy_pass http://avatar-svc.middleware:3000/;

    proxy_set_header Host $http_host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
  }

  location ~* \.(css|js|jpg|png|gif|ico|svg)$ {
    expires 1y;
    add_header Cache-Control "public";
  }
}
