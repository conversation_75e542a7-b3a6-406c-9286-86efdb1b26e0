package com.nnepoch.rendermanager.domain.node.specification;

import com.nnepoch.rendermanager.api.enums.NodeStatusEnum;
import com.nnepoch.rendermanager.domain.node.aggregate.Node;
import com.nnepoch.rendermanager.domain.shared.specification.Specification;

/**
 * 节点规约集合
 * <AUTHOR> Migration
 */
public class NodeSpecifications {
    
    /**
     * 节点在线规约
     */
    public static Specification<Node> isOnline() {
        return node -> node.getStatus() == NodeStatusEnum.ONLINE;
    }
    
    /**
     * 节点有可用容量规约
     */
    public static Specification<Node> hasAvailableCapacity() {
        return Node::canStartNewScene;
    }
    
    /**
     * 节点健康规约
     */
    public static Specification<Node> isHealthy() {
        return Node::isHealthy;
    }
    
    /**
     * 节点负载低于阈值规约
     */
    public static Specification<Node> loadLessThan(double threshold) {
        return node -> {
            int maxCapacity = node.getSpec().getMaxSceneInstanceCount();
            if (maxCapacity == 0) return false;
            double load = (double) node.getSceneRunningCount() / maxCapacity;
            return load < threshold;
        };
    }
    
    /**
     * 节点启用日志收集规约
     */
    public static Specification<Node> hasLogCollectionEnabled() {
        return Node::isEnableLogCollection;
    }
    
    /**
     * 节点属于指定组织规约
     */
    public static Specification<Node> belongsToOrganization(String organization) {
        return node -> organization.equals(node.getSpec().getOrganization());
    }
    
    /**
     * 可分配节点规约 (组合规约示例)
     */
    public static Specification<Node> isAssignable() {
        return isOnline()
            .and(hasAvailableCapacity())
            .and(isHealthy());
    }
    
    /**
     * 高性能节点规约 (组合规约示例)
     */
    public static Specification<Node> isHighPerformance() {
        return isOnline()
            .and(loadLessThan(0.5))
            .and(node -> node.getSpec().getMaxSceneInstanceCount() >= 10);
    }
}
