package com.nnepoch.rendermanager.config.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

/**
 * <AUTHOR> 2024/6/20
 */
@Component
@Validated
@Data
@ConfigurationProperties(prefix = "app.dingtalk")
public class AppDingtalkProperties {
    private String clientId;
    private String clientSecret;
}
