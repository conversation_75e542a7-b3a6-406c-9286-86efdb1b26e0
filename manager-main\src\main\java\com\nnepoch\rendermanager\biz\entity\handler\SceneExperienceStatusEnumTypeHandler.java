package com.nnepoch.rendermanager.biz.entity.handler;

import com.nnepoch.rendermanager.api.enums.SceneExperienceStatusEnum;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * <AUTHOR> 2024/8/6
 */
public class SceneExperienceStatusEnumTypeHandler extends BaseTypeHandler<SceneExperienceStatusEnum> {
    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, SceneExperienceStatusEnum parameter, JdbcType jdbcType) throws SQLException {
        ps.setInt(i, parameter.getCode());
    }

    @Override
    public SceneExperienceStatusEnum getNullableResult(ResultSet rs, String columnName) throws SQLException {
        Integer code = rs.getInt(columnName);
        return SceneExperienceStatusEnum.fromCode(code);
    }

    @Override
    public SceneExperienceStatusEnum getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        Integer code = rs.getInt(columnIndex);
        return SceneExperienceStatusEnum.fromCode(code);
    }

    @Override
    public SceneExperienceStatusEnum getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        Integer code = cs.getInt(columnIndex);
        return SceneExperienceStatusEnum.fromCode(code);
    }
}
