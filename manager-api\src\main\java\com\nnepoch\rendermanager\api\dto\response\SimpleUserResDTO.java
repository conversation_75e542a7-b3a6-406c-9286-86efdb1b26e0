package com.nnepoch.rendermanager.api.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR> 2024/8/7
 */
@Data
@Schema(description = "用户简要信息")
public class SimpleUserResDTO {
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    private String username;

    private String email;

    private String mobile;

    private String avatar;
}
