package com.nnepoch.rendermanager.application.node.command;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;

/**
 * 注册节点命令
 * <AUTHOR> Migration
 */
@Data
public class RegisterNodeCommand {
    
    @NotBlank(message = "节点SN不能为空")
    private String sn;
    
    @NotBlank(message = "主机名不能为空")
    private String hostname;
    
    private String hashId;
    
    @NotBlank(message = "操作系统不能为空")
    private String os;
    
    private String osVersion;
    
    @NotNull(message = "最大场景实例数不能为空")
    @Positive(message = "最大场景实例数必须为正数")
    private Integer maxSceneInstanceCount;
    
    private String organization;
    private String mobile;
    private String email;
    
    @NotNull(message = "所有者ID不能为空")
    private Long ownedBy;
}
