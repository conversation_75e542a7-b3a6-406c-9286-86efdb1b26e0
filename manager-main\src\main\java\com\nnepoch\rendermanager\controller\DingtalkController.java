package com.nnepoch.rendermanager.controller;

import com.nnepoch.rendermanager.api.dto.response.SigninResDTO;
import com.nnepoch.rendermanager.biz.service.DingtalkService;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> 2024/8/21
 */
@RestController
@RequestMapping(value = "/api/v1/dingtalk", produces = MediaType.APPLICATION_JSON_VALUE)
@RequiredArgsConstructor
@Tag(name = "Dingtalk", description = "Dingtalk接口")
public class DingtalkController {
    private final DingtalkService dingtalkService;

    @GetMapping("auth")
    public SigninResDTO auth(@RequestParam() String authCode) throws Exception {
        return dingtalkService.auth(authCode);
    }
}
