package com.nnepoch.rendermanager.api.dto.request;

import com.nnepoch.rendermanager.api.dto.request.base.PageQueryDTO;
import com.nnepoch.rendermanager.api.enums.NodeStatusEnum;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> 2024/6/13
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(title = "节点查询条件")
public class NodeQueryDTO extends PageQueryDTO {
    @Parameter(description = "节点ID")
    private String hashId;

    @Parameter(description = "节点名称")
    private String hostname;

    @Parameter(description = "节点状态")
    private NodeStatusEnum status;

    @Parameter(description = "节点组织")
    private String organization;

    @Parameter(description = "节点管理员")
    private Long ownedBy;
}
