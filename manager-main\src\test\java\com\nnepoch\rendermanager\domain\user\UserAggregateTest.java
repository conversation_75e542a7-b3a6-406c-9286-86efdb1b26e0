package com.nnepoch.rendermanager.domain.user;

import com.nnepoch.rendermanager.api.enums.UserRoleEnum;
import com.nnepoch.rendermanager.domain.shared.valueobject.UserId;
import com.nnepoch.rendermanager.domain.user.aggregate.User;
import com.nnepoch.rendermanager.domain.user.event.UserCreatedEvent;
import com.nnepoch.rendermanager.domain.user.event.UserLoginEvent;
import com.nnepoch.rendermanager.domain.user.event.UserRoleChangedEvent;
import com.nnepoch.rendermanager.domain.user.valueobject.UserProfile;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

/**
 * 用户聚合测试
 * 验证DDD领域模型的正确性
 */
class UserAggregateTest {
    
    @Test
    void should_create_user_successfully() {
        // Given
        UserId userId = UserId.of(1L);
        UserProfile profile = new UserProfile("testuser", "<EMAIL>", "13800138000");
        String passwordHash = "hashedPassword";
        UserRoleEnum role = UserRoleEnum.MEMBER;
        
        // When
        User user = User.create(userId, profile, passwordHash, role);
        
        // Then
        assertThat(user.getId()).isEqualTo(userId);
        assertThat(user.getProfile()).isEqualTo(profile);
        assertThat(user.getPasswordHash()).isEqualTo(passwordHash);
        assertThat(user.getRole()).isEqualTo(role);
        assertThat(user.isBanned()).isFalse();
        assertThat(user.getDomainEvents()).hasSize(1);
        assertThat(user.getDomainEvents().get(0)).isInstanceOf(UserCreatedEvent.class);
    }
    
    @Test
    void should_login_successfully_when_not_banned() {
        // Given
        User user = createTestUser();
        
        // When
        user.login();
        
        // Then
        assertThat(user.getLoginAt()).isNotNull();
        assertThat(user.getDomainEvents()).hasSize(2); // UserCreated + UserLogin
        assertThat(user.getDomainEvents().get(1)).isInstanceOf(UserLoginEvent.class);
    }
    
    @Test
    void should_throw_exception_when_banned_user_tries_to_login() {
        // Given
        User user = createTestUser();
        user.ban();
        
        // When & Then
        assertThatThrownBy(user::login)
            .isInstanceOf(IllegalStateException.class)
            .hasMessage("User is banned and cannot login");
    }
    
    @Test
    void should_change_role_successfully() {
        // Given
        User user = createTestUser();
        UserRoleEnum newRole = UserRoleEnum.ADMIN;
        
        // When
        user.changeRole(newRole);
        
        // Then
        assertThat(user.getRole()).isEqualTo(newRole);
        assertThat(user.getDomainEvents()).hasSize(2); // UserCreated + UserRoleChanged
        assertThat(user.getDomainEvents().get(1)).isInstanceOf(UserRoleChangedEvent.class);
    }
    
    @Test
    void should_ban_user_successfully() {
        // Given
        User user = createTestUser();
        
        // When
        user.ban();
        
        // Then
        assertThat(user.isBanned()).isTrue();
        assertThat(user.canAccess()).isFalse();
    }
    
    @Test
    void should_unban_user_successfully() {
        // Given
        User user = createTestUser();
        user.ban();
        
        // When
        user.unban();
        
        // Then
        assertThat(user.isBanned()).isFalse();
        assertThat(user.canAccess()).isTrue();
    }
    
    @Test
    void should_check_admin_role_correctly() {
        // Given
        User adminUser = createTestUser();
        adminUser.changeRole(UserRoleEnum.ADMIN);
        
        User memberUser = createTestUser();
        
        // Then
        assertThat(adminUser.isAdmin()).isTrue();
        assertThat(memberUser.isAdmin()).isFalse();
    }
    
    @Test
    void should_validate_user_profile() {
        // Given
        UserId userId = UserId.of(1L);
        
        // When & Then - Invalid username
        assertThatThrownBy(() -> new UserProfile("", "<EMAIL>", "13800138000"))
            .isInstanceOf(IllegalArgumentException.class)
            .hasMessage("Username cannot be empty");
        
        // When & Then - Invalid email
        assertThatThrownBy(() -> new UserProfile("testuser", "invalid-email", "13800138000"))
            .isInstanceOf(IllegalArgumentException.class)
            .hasMessage("Invalid email format");
        
        // When & Then - Invalid mobile
        assertThatThrownBy(() -> new UserProfile("testuser", "<EMAIL>", "invalid-mobile"))
            .isInstanceOf(IllegalArgumentException.class)
            .hasMessage("Invalid mobile format");
    }
    
    private User createTestUser() {
        UserId userId = UserId.of(1L);
        UserProfile profile = new UserProfile("testuser", "<EMAIL>", "13800138000");
        return User.create(userId, profile, "hashedPassword", UserRoleEnum.MEMBER);
    }
}
