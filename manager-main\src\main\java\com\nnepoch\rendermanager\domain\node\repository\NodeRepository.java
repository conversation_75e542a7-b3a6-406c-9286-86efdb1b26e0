package com.nnepoch.rendermanager.domain.node.repository;

import com.nnepoch.rendermanager.api.enums.NodeStatusEnum;
import com.nnepoch.rendermanager.domain.node.aggregate.Node;
import com.nnepoch.rendermanager.domain.shared.repository.Repository;
import com.nnepoch.rendermanager.domain.shared.valueobject.NodeId;
import com.nnepoch.rendermanager.domain.shared.valueobject.UserId;

import java.util.List;
import java.util.Optional;

/**
 * 节点仓储接口
 * <AUTHOR> Migration
 */
public interface NodeRepository extends Repository<Node, NodeId> {
    
    /**
     * 根据SN查找节点
     */
    Optional<Node> findBySn(String sn);
    
    /**
     * 根据主机名查找节点
     */
    Optional<Node> findByHostname(String hostname);
    
    /**
     * 根据状态查找节点列表
     */
    List<Node> findByStatus(NodeStatusEnum status);
    
    /**
     * 查找在线节点列表
     */
    List<Node> findOnlineNodes();
    
    /**
     * 根据所有者查找节点列表
     */
    List<Node> findByOwner(UserId ownerId);
    
    /**
     * 检查SN是否存在
     */
    boolean existsBySn(String sn);
    
    /**
     * 检查主机名是否存在
     */
    boolean existsByHostname(String hostname);
    
    /**
     * 统计节点总数
     */
    long count();
    
    /**
     * 统计在线节点数
     */
    long countOnlineNodes();
    
    /**
     * 查找负载最低的节点
     */
    Optional<Node> findLeastLoadedNode();
}
