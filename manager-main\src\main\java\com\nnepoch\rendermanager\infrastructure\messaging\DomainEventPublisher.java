package com.nnepoch.rendermanager.infrastructure.messaging;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.nnepoch.rendermanager.domain.shared.event.DomainEvent;
import com.nnepoch.rendermanager.infrastructure.monitoring.PerformanceMetrics;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.core.MessageProperties;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.Instant;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

/**
 * 领域事件消息发布器
 * 负责将领域事件发布到消息队列，支持重试、死信队列等企业级特性
 * <AUTHOR> Architect
 */
@Component("messagingEventPublisher")
@RequiredArgsConstructor
@Slf4j
public class DomainEventPublisher {

    private final RabbitTemplate rabbitTemplate;
    private final ObjectMapper objectMapper;
    private final PerformanceMetrics performanceMetrics;
    
    // 交换机和路由键配置
    private static final String DOMAIN_EVENTS_EXCHANGE = "domain.events";
    
    /**
     * 发布领域事件
     */
    @Retryable(value = {Exception.class}, maxAttempts = 3, backoff = @Backoff(delay = 1000))
    public void publish(DomainEvent event) {
        Instant start = Instant.now();
        
        try {
            String routingKey = buildRoutingKey(event);
            Message message = createMessage(event);
            
            rabbitTemplate.send(DOMAIN_EVENTS_EXCHANGE, routingKey, message);
            
            Duration duration = Duration.between(start, Instant.now());
            performanceMetrics.recordEventProcessing(event.getEventType(), "publisher", duration, true);
            
            log.debug("Published event: {} with routing key: {}", event.getEventType(), routingKey);
            
        } catch (Exception e) {
            Duration duration = Duration.between(start, Instant.now());
            performanceMetrics.recordEventProcessing(event.getEventType(), "publisher", duration, false);
            
            log.error("Failed to publish event: {}", event.getEventType(), e);
            throw new EventPublishException("Failed to publish event: " + event.getEventType(), e);
        }
    }
    
    /**
     * 异步发布事件
     */
    public CompletableFuture<Void> publishAsync(DomainEvent event) {
        return CompletableFuture.runAsync(() -> publish(event));
    }
    
    /**
     * 批量发布事件
     */
    public void publishBatch(java.util.List<DomainEvent> events) {
        events.forEach(this::publish);
    }
    
    /**
     * 延迟发布事件
     * 注意：此实现使用自定义头部信息标记延迟时间，需要消费者端处理延迟逻辑
     */
    public void publishDelayed(DomainEvent event, Duration delay) {
        try {
            String routingKey = buildRoutingKey(event);
            Message message = createMessage(event);

            // 使用自定义头部信息设置延迟时间（毫秒）
            message.getMessageProperties().getHeaders().put("x-delay", delay.toMillis());

            rabbitTemplate.send(DOMAIN_EVENTS_EXCHANGE, routingKey, message);

            log.debug("Published delayed event: {} with delay: {}", event.getEventType(), delay);

        } catch (Exception e) {
            log.error("Failed to publish delayed event: {}", event.getEventType(), e);
            throw new EventPublishException("Failed to publish delayed event: " + event.getEventType(), e);
        }
    }
    
    /**
     * 发布到特定队列
     */
    public void publishToQueue(DomainEvent event, String queueName) {
        try {
            Message message = createMessage(event);
            rabbitTemplate.send(queueName, message);
            
            log.debug("Published event: {} to queue: {}", event.getEventType(), queueName);
            
        } catch (Exception e) {
            log.error("Failed to publish event to queue: {}", queueName, e);
            throw new EventPublishException("Failed to publish event to queue: " + queueName, e);
        }
    }
    
    /**
     * 发布高优先级事件
     */
    public void publishHighPriority(DomainEvent event) {
        try {
            String routingKey = buildRoutingKey(event);
            Message message = createMessage(event);
            
            // 设置高优先级
            message.getMessageProperties().setPriority(10);
            
            rabbitTemplate.send(DOMAIN_EVENTS_EXCHANGE, routingKey, message);
            
            log.debug("Published high priority event: {}", event.getEventType());
            
        } catch (Exception e) {
            log.error("Failed to publish high priority event: {}", event.getEventType(), e);
            throw new EventPublishException("Failed to publish high priority event: " + event.getEventType(), e);
        }
    }
    
    /**
     * 发布事务性事件（确保消息投递）
     */
    public void publishTransactional(DomainEvent event) {
        try {
            String routingKey = buildRoutingKey(event);
            Message message = createMessage(event);

            // 使用RabbitTemplate的事务支持
            rabbitTemplate.convertAndSend(DOMAIN_EVENTS_EXCHANGE, routingKey, message);

            log.debug("Published transactional event: {}", event.getEventType());

        } catch (Exception e) {
            log.error("Failed to publish transactional event: {}", event.getEventType(), e);
            throw new EventPublishException("Failed to publish transactional event: " + event.getEventType(), e);
        }
    }
    
    /**
     * 创建消息
     */
    private Message createMessage(DomainEvent event) {
        try {
            byte[] body = objectMapper.writeValueAsBytes(event);
            
            MessageProperties properties = new MessageProperties();
            properties.setContentType("application/json");
            properties.setMessageId(UUID.randomUUID().toString());
            properties.setTimestamp(java.util.Date.from(event.getOccurredOn().atZone(java.time.ZoneId.systemDefault()).toInstant()));
            
            // 设置消息头
            Map<String, Object> headers = new HashMap<>();
            headers.put("eventType", event.getEventType());
            headers.put("eventId", event.getEventId());
            headers.put("occurredOn", event.getOccurredOn().toString());
            headers.put("version", "1.0");
            
            properties.setHeaders(headers);
            
            // 设置TTL（消息存活时间）
            properties.setExpiration("3600000"); // 1小时
            
            return new Message(body, properties);
            
        } catch (Exception e) {
            throw new EventPublishException("Failed to create message for event: " + event.getEventType(), e);
        }
    }
    
    /**
     * 构建路由键
     */
    private String buildRoutingKey(DomainEvent event) {
        String eventType = event.getEventType();
        
        // 根据事件类型构建路由键
        if (eventType.contains("User")) {
            return "user." + eventType.toLowerCase();
        } else if (eventType.contains("Node")) {
            return "node." + eventType.toLowerCase();
        } else if (eventType.contains("Scene")) {
            return "scene." + eventType.toLowerCase();
        } else {
            return "general." + eventType.toLowerCase();
        }
    }
    
    /**
     * 发布确认回调
     */
    public void setupPublisherConfirms() {
        rabbitTemplate.setConfirmCallback((correlationData, ack, cause) -> {
            if (ack) {
                log.debug("Message confirmed: {}", correlationData);
                performanceMetrics.recordBusinessMetric("message.confirmed");
            } else {
                log.error("Message not confirmed: {}, cause: {}", correlationData, cause);
                performanceMetrics.recordBusinessMetric("message.not_confirmed");
            }
        });
        
        rabbitTemplate.setReturnsCallback(returned -> {
            log.error("Message returned: {}, reply code: {}, reply text: {}", 
                returned.getMessage(), returned.getReplyCode(), returned.getReplyText());
            performanceMetrics.recordBusinessMetric("message.returned");
        });
    }
    
    /**
     * 获取发布统计信息
     */
    public PublishStatistics getPublishStatistics() {
        return PublishStatistics.builder()
            .totalPublished(getTotalPublished())
            .totalConfirmed(getTotalConfirmed())
            .totalReturned(getTotalReturned())
            .averagePublishTime(getAveragePublishTime())
            .build();
    }
    
    private long getTotalPublished() {
        // 从指标系统获取
        return 0; // 简化实现
    }
    
    private long getTotalConfirmed() {
        // 从指标系统获取
        return 0; // 简化实现
    }
    
    private long getTotalReturned() {
        // 从指标系统获取
        return 0; // 简化实现
    }
    
    private double getAveragePublishTime() {
        // 从指标系统获取
        return 0.0; // 简化实现
    }
}

/**
 * 事件发布异常
 */
class EventPublishException extends RuntimeException {
    public EventPublishException(String message, Throwable cause) {
        super(message, cause);
    }
}

/**
 * 发布统计信息
 */
@lombok.Builder
@lombok.Data
class PublishStatistics {
    private long totalPublished;
    private long totalConfirmed;
    private long totalReturned;
    private double averagePublishTime;
}
