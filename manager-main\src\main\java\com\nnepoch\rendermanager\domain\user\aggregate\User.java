package com.nnepoch.rendermanager.domain.user.aggregate;

import com.nnepoch.rendermanager.api.enums.UserRoleEnum;
import com.nnepoch.rendermanager.domain.shared.aggregate.AggregateRoot;
import com.nnepoch.rendermanager.domain.shared.valueobject.UserId;
import com.nnepoch.rendermanager.domain.user.event.UserCreatedEvent;
import com.nnepoch.rendermanager.domain.user.event.UserLoginEvent;
import com.nnepoch.rendermanager.domain.user.event.UserRoleChangedEvent;
import com.nnepoch.rendermanager.domain.user.valueobject.UserProfile;
import lombok.Getter;

import java.time.LocalDateTime;

/**
 * 用户聚合根
 * <AUTHOR> Migration
 */
@Getter
public class User extends AggregateRoot<UserId> {
    private UserProfile profile;
    private String passwordHash;
    private UserRoleEnum role;
    private boolean isBanned;
    private LocalDateTime loginAt;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    
    // 私有构造函数，强制使用工厂方法
    private User(UserId id, UserProfile profile, String passwordHash, UserRoleEnum role) {
        super(id);
        this.profile = profile;
        this.passwordHash = passwordHash;
        this.role = role;
        this.isBanned = false;
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * 创建新用户
     */
    public static User create(UserId id, UserProfile profile, String passwordHash, UserRoleEnum role) {
        User user = new User(id, profile, passwordHash, role);
        user.addDomainEvent(new UserCreatedEvent(id, profile.getUsername(), role));
        return user;
    }
    
    /**
     * 用户登录
     */
    public void login() {
        if (isBanned) {
            throw new IllegalStateException("User is banned and cannot login");
        }
        this.loginAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
        addDomainEvent(new UserLoginEvent(this.id, profile.getUsername()));
    }
    
    /**
     * 更改用户角色
     */
    public void changeRole(UserRoleEnum newRole) {
        if (newRole == null) {
            throw new IllegalArgumentException("Role cannot be null");
        }
        UserRoleEnum oldRole = this.role;
        this.role = newRole;
        this.updatedAt = LocalDateTime.now();
        addDomainEvent(new UserRoleChangedEvent(this.id, oldRole, newRole));
    }
    
    /**
     * 封禁用户
     */
    public void ban() {
        this.isBanned = true;
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * 解封用户
     */
    public void unban() {
        this.isBanned = false;
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * 更新用户档案
     */
    public void updateProfile(UserProfile newProfile) {
        this.profile = newProfile;
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * 检查是否有管理员权限
     */
    public boolean isAdmin() {
        return UserRoleEnum.ADMIN.equals(this.role);
    }
    
    /**
     * 检查是否可以访问
     */
    public boolean canAccess() {
        return !isBanned;
    }
}
