package com.nnepoch.rendermanager.api.feign;

import com.nnepoch.rendermanager.api.feign.fallback.WebFeignFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;

/**
 * <AUTHOR> <PERSON> 2024/6/13
 */
@FeignClient(value = "render-manager", fallbackFactory = WebFeignFallbackFactory.class, path = "/render-manager")
public interface WebFeignService {
    @GetMapping("/info")
    String getInfo();
}
