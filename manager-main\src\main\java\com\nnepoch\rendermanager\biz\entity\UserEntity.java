package com.nnepoch.rendermanager.biz.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nnepoch.rendermanager.api.enums.UserRoleEnum;
import com.nnepoch.rendermanager.biz.entity.enums.UserPermissionEnum;
import com.nnepoch.rendermanager.biz.entity.handler.UserRoleEnumTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

import java.util.*;

/**
 * <AUTHOR> <PERSON>e 2024/6/13
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value = "\"user\"", autoResultMap = true)
@Data
public class UserEntity extends BaseEntity implements UserDetails {
    private String username;

    @TableField(select = false)
    private String password;

    @TableField(exist = false)
    private String rawPassword;

    private Boolean isBanned;

    private UserRoleEnum role;

    private String email;

    private String mobile;

    private Date loginAt;

    @TableField(exist = false)
    private String avatar;

    @TableField(exist = false)
    private String permissions;

    public String getAvatar() {
        return "/avatar/9.x/big-smile/svg?seed=" + this.getId();
    }

    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        List<GrantedAuthority> authorities = new ArrayList<>();
        authorities.add(new SimpleGrantedAuthority(this.role.name()));

        Set<UserPermissionEnum> permissionSet = RoleEntity.getPermissionSet(this.permissions);

        for (UserPermissionEnum permission : permissionSet) {
            authorities.add(new SimpleGrantedAuthority(permission.name()));
        }

        return authorities;
    }

    @Override
    public boolean isEnabled() {
        return !this.isBanned;
    }
}
