package com.nnepoch.rendermanager.domain.node.service;

import com.nnepoch.rendermanager.domain.node.aggregate.Node;
import com.nnepoch.rendermanager.domain.node.repository.NodeRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.Optional;

/**
 * 节点领域服务
 * <AUTHOR> Migration
 */
@Service
@RequiredArgsConstructor
public class NodeDomainService {
    
    private final NodeRepository nodeRepository;
    
    /**
     * 验证节点注册的业务规则
     */
    public void validateNodeRegistration(String sn, String hostname) {
        if (nodeRepository.existsBySn(sn)) {
            throw new IllegalArgumentException("节点SN已存在: " + sn);
        }
        
        if (nodeRepository.existsByHostname(hostname)) {
            throw new IllegalArgumentException("主机名已存在: " + hostname);
        }
    }
    
    /**
     * 查找最佳可用节点
     * 策略: 选择负载最低的在线节点
     */
    public Optional<Node> findBestAvailableNode() {
        return nodeRepository.findOnlineNodes().stream()
            .filter(Node::canStartNewScene)
            .min(Comparator.comparing(this::calculateNodeLoad));
    }
    
    /**
     * 计算节点负载
     * 负载 = 当前运行场景数 / 最大容量
     */
    private double calculateNodeLoad(Node node) {
        int maxCapacity = node.getSpec().getMaxSceneInstanceCount();
        if (maxCapacity == 0) {
            return Double.MAX_VALUE; // 避免除零
        }
        return (double) node.getSceneRunningCount() / maxCapacity;
    }
    
    /**
     * 检查节点是否可以分配给指定用户
     */
    public boolean canAssignToUser(Node node, Long userId) {
        // 业务规则: 节点必须在线且有剩余容量
        return node.isHealthy() && node.canStartNewScene();
    }
    
    /**
     * 计算集群总容量
     */
    public int calculateClusterCapacity() {
        return nodeRepository.findOnlineNodes().stream()
            .mapToInt(node -> node.getSpec().getMaxSceneInstanceCount())
            .sum();
    }
    
    /**
     * 计算集群已使用容量
     */
    public int calculateClusterUsedCapacity() {
        return nodeRepository.findOnlineNodes().stream()
            .mapToInt(Node::getSceneRunningCount)
            .sum();
    }
    
    /**
     * 检查集群是否健康
     */
    public boolean isClusterHealthy() {
        long onlineNodes = nodeRepository.findOnlineNodes().size();
        long totalNodes = nodeRepository.count();
        
        // 至少50%的节点在线才认为集群健康
        return totalNodes > 0 && (double) onlineNodes / totalNodes >= 0.5;
    }
}
