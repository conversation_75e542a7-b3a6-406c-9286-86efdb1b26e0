package com.nnepoch.rendermanager.config;

import org.springframework.amqp.core.*;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> 2024/6/23
 */
@Configuration
public class RabbitConfig {
    public static final String EXCHANGE_NAME = "amq.topic";
    public static final String NODE_ROUTING_KEY = "agent.#";
    public static final String DELAY_EXCHANGE_NAME = "delay.exchange";
    public static final String DELAY_QUEUE_NAME = "delay.queue";
    public static final String DELAY_ROUTING_KEY = "delay.#";

    public static String getCloudRoutingKey(String nodeSn) {
        return "cloud." + nodeSn;
    }

    @Bean(name = "renderExchange")
    public Exchange exchange() {
        return ExchangeBuilder.topicExchange(EXCHANGE_NAME).durable(true).build();
    }

    @Bean(name = "delayExchange")
    public CustomExchange delayExchange() {
        Map<String, Object> args = new HashMap<>(1);
        args.put("x-delayed-type", "direct");
        return new CustomExchange(DELAY_EXCHANGE_NAME, "x-delayed-message", true, false, args);
    }

    @Bean(name = "delayQueue")
    public Queue delayQueue() {
        return QueueBuilder.durable(DELAY_QUEUE_NAME).build();
    }

    @Bean
    public Binding getDelayBinding(@Qualifier("delayQueue") Queue queue, @Qualifier("delayExchange") CustomExchange exchange) {
        return BindingBuilder.bind(queue).to(exchange).with(DELAY_ROUTING_KEY).noargs();
    }
}
