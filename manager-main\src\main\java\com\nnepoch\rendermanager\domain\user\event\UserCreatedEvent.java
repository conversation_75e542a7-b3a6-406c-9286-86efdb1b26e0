package com.nnepoch.rendermanager.domain.user.event;

import com.nnepoch.rendermanager.api.enums.UserRoleEnum;
import com.nnepoch.rendermanager.domain.shared.event.DomainEvent;
import com.nnepoch.rendermanager.domain.shared.valueobject.UserId;
import lombok.Getter;

/**
 * 用户创建事件
 * <AUTHOR> Migration
 */
@Getter
public class UserCreatedEvent extends DomainEvent {
    private final UserId userId;
    private final String username;
    private final UserRoleEnum role;
    
    public UserCreatedEvent(UserId userId, String username, UserRoleEnum role) {
        super();
        this.userId = userId;
        this.username = username;
        this.role = role;
    }
    
    @Override
    public String getEventType() {
        return "UserCreated";
    }
}
