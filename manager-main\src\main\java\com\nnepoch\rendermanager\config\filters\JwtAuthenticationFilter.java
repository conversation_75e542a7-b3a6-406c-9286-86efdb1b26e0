package com.nnepoch.rendermanager.config.filters;

import cn.hutool.core.text.CharSequenceUtil;
import com.nnepoch.rendermanager.biz.entity.UserEntity;
import com.nnepoch.rendermanager.biz.service.JwtService;
import com.nnepoch.rendermanager.biz.service.UserService;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.lang.NonNull;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;

/**
 * <AUTHOR> <PERSON> 2024/6/20
 */
@RequiredArgsConstructor
@Component
public class JwtAuthenticationFilter extends OncePerRequestFilter {
    private final JwtService jwtService;
    private final UserService userService;

    @Override
    protected void doFilterInternal(
        @NonNull HttpServletRequest request,
        @NonNull HttpServletResponse response,
        @NonNull FilterChain filterChain) throws ServletException, IOException, RuntimeException {

        final String authHeader = request.getHeader(HttpHeaders.AUTHORIZATION);
        final String token;
        final String mobile;

        if (CharSequenceUtil.isEmpty(authHeader) || !authHeader.startsWith("Bearer ")) {
            filterChain.doFilter(request, response);
            return;
        }

        token = authHeader.substring(7);

        if (jwtService.isTokenInValid(token)) {
            filterChain.doFilter(request, response);
            return;
        }

        // 系统使用 mobile 作为 JWT 的唯一标识
        mobile = (String) jwtService.parseJWT(token).getClaim("mobile");

        if (CharSequenceUtil.isNotEmpty(mobile) && SecurityContextHolder.getContext().getAuthentication() == null) {
            UserEntity user = userService.getUserByMobileWithPassword(mobile);

            if (jwtService.validateJWT(token, user)) {
                var authToken = new UsernamePasswordAuthenticationToken(user, mobile, user.getAuthorities());
                authToken.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
                SecurityContextHolder.getContext().setAuthentication(authToken);
            }
        }

        filterChain.doFilter(request, response);
    }
}
