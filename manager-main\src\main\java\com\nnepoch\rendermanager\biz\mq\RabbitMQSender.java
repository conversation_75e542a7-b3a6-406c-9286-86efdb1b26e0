package com.nnepoch.rendermanager.biz.mq;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.core.MessageProperties;
import org.springframework.amqp.core.MessagePropertiesBuilder;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class RabbitMQSender {
    private final RabbitTemplate rabbitTemplate;

    public void send(String exchange, String routingKey, String payload, int priority) {
        MessageProperties messageProperties = MessagePropertiesBuilder.newInstance()
                .setPriority(priority)
                .build();

        Message message = new Message(payload.getBytes(), messageProperties);

        rabbitTemplate.send(exchange, routingKey, message);

        log.info("Sent message to exchange: {}, routing key: {}, priority: {}, payload: {}", exchange, routingKey, priority, payload);
    }
}
