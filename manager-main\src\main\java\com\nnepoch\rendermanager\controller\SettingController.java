package com.nnepoch.rendermanager.controller;

import com.nnepoch.rendermanager.api.dto.response.PublicSettingResDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> 2024/6/24
 */
@RestController
@RequestMapping(path = "/api/v1/settings", produces = "application/json")
@RequiredArgsConstructor
@Tag(name = "Setting", description = "平台设置相关接口")
public class SettingController {
    private final PublicSettingResDTO publicSettingResDTO;

    @GetMapping("public")
    @Operation(summary = "获取公开配置信息")
    public PublicSettingResDTO getPublicSetting() {
        return publicSettingResDTO;
    }
}
