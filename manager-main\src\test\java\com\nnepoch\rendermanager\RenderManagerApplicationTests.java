package com.nnepoch.rendermanager;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.nnepoch.rendermanager.biz.entity.NodeEntity;
import com.nnepoch.rendermanager.biz.mapper.NodeMapper;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.ArrayList;
import java.util.List;

@SpringBootTest
@ActiveProfiles("dev")
class RenderManagerApplicationTests {
    @Resource
    private NodeMapper nodeMapper;

    @Test
    void test() {
        QueryWrapper<NodeEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("sn", "03560274-043C-0565-8E06-110700080009");
        NodeEntity node = nodeMapper.selectOne(wrapper);
        System.out.println(node);
        assert node != null;
    }

    @Test
    void array() {
        List<String> privileges = new ArrayList<>();
        privileges.add("admin");
        privileges.add("user");
        privileges.add("guest");
        privileges.add("guest");
        System.out.println("privileges: " + privileges);
    }
}
