package com.nnepoch.rendermanager.application.scene.command;

import com.nnepoch.rendermanager.api.enums.SceneIndustryEnum;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * 创建场景命令
 * <AUTHOR> Architect
 */
@Data
public class CreateSceneCommand {
    
    @NotBlank(message = "场景名称不能为空")
    private String name;
    
    @NotBlank(message = "场景版本不能为空")
    private String version;
    
    private String description;
    
    @NotNull(message = "行业类型不能为空")
    private SceneIndustryEnum industry;
    
    private String link;
    
    private Boolean isOfficial = false;
    
    private String intro;
    
    private String thumb;
}
