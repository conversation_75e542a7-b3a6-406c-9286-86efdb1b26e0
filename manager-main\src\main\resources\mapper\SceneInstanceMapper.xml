<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nnepoch.rendermanager.biz.mapper.SceneInstanceMapper">
    <resultMap id="BaseResultMap" type="com.nnepoch.rendermanager.api.dto.response.SceneInstanceResDTO">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="nodeId" column="node_id" jdbcType="BIGINT"/>
        <result property="nodeSn" column="node_sn" jdbcType="VARCHAR"/>
        <result property="sessionId" column="session_id" jdbcType="VARCHAR"/>
        <result property="processId" column="process_id" jdbcType="INTEGER"/>
        <result property="sceneExperienceId" column="scene_experience_id" jdbcType="BIGINT"/>
        <result property="resolution" column="resolution" jdbcType="VARCHAR"/>
        <result property="fps" column="fps" jdbcType="INTEGER"/>
        <result property="bitrate" column="bitrate" jdbcType="INTEGER"/>
        <result property="memory" column="memory" jdbcType="INTEGER"/>
        <result property="cpuUsage" column="cpu_usage" jdbcType="DOUBLE"/>
        <result property="startTime" column="start_time" jdbcType="TIMESTAMP"/>
        <result property="runTime" column="run_time" jdbcType="INTEGER"/>
        <association property="owner" javaType="com.nnepoch.rendermanager.api.dto.response.SimpleUserResDTO">
            <id property="id" column="owner_id" jdbcType="BIGINT"/>
            <result property="username" column="owner_username" jdbcType="VARCHAR"/>
            <result property="email" column="owner_email" jdbcType="VARCHAR"/>
            <result property="mobile" column="owner_mobile" jdbcType="VARCHAR"/>
            <result property="avatar" column="owner_avatar" jdbcType="VARCHAR"/>
        </association>
        <association property="sceneExperience"
                     javaType="com.nnepoch.rendermanager.api.dto.response.SimpleUserResDTO">
            <id property="id" column="scene_experience_id" jdbcType="BIGINT"/>
            <result property="username" column="scene_experience_username" jdbcType="VARCHAR"/>
            <result property="email" column="scene_experience_email" jdbcType="VARCHAR"/>
            <result property="mobile" column="scene_experience_mobile" jdbcType="VARCHAR"/>
            <result property="avatar" column="scene_experience_avatar" jdbcType="VARCHAR"/>
        </association>
    </resultMap>

    <select id="selectSceneInstanceList" resultMap="BaseResultMap">
        SELECT si.id,
               si.node_id,
               n.sn              node_sn,
               si.session_id,
               si.process_id,
               si.scene_experience_id,
               si.resolution,
               si.fps,
               si.bitrate,
               si.memory,
               si.cpu_usage,
               si.start_time,
               si.run_time,
               u.id              owner_id,
               u.username        owner_username,
               u.email           owner_email,
               u.mobile          owner_mobile,
               CASE
                   WHEN u.id IS NOT NULL THEN CONCAT('/avatar/9.x/big-smile/svg?seed=', u.id)
                   ELSE NULL END owner_avatar,
               se.id AS          scene_experience_id,
               scene_experience_id,
               se.username       scene_experience_username,
               se.email          scene_experience_email,
               se.mobile         scene_experience_mobile,
               CASE
                   WHEN se.id IS NOT NULL THEN CONCAT('/avatar/9.x/big-smile/svg?seed=', se.id)
                   ELSE NULL
                   END           scene_experience_avatar
        FROM public.scene_instance si
                 INNER JOIN public.node n ON si.node_id = n.id
                 LEFT JOIN public.user u ON si.owned_by = u.id
                 LEFT JOIN public.scene_experience se ON si.scene_experience_id = se.id
            ${ew.customSqlSegment}
    </select>

</mapper>
