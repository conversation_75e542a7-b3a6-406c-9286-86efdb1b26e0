package com.nnepoch.rendermanager.biz.service.impl;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.lang.id.NanoId;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.RSA;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.nnepoch.rendermanager.api.dto.request.SigninReqDTO;
import com.nnepoch.rendermanager.api.dto.response.AuthPublicKeyResDTO;
import com.nnepoch.rendermanager.api.dto.response.SigninResDTO;
import com.nnepoch.rendermanager.biz.entity.UserEntity;
import com.nnepoch.rendermanager.biz.service.AuthService;
import com.nnepoch.rendermanager.biz.service.JwtService;
import com.nnepoch.rendermanager.biz.service.RedisService;
import com.nnepoch.rendermanager.exception.BizException;
import lombok.RequiredArgsConstructor;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> Che 2024/6/20
 */
@Service
@RequiredArgsConstructor
public class AuthServiceImpl implements AuthService {
    private final UserServiceImpl userService;
    private final JwtService jwtService;
    private final AuthenticationManager authenticationManager;
    private final RedisService redisService;

    private static final String AUTH_KEY_PREFIX = "auth:key:";

    @Override
    public AuthPublicKeyResDTO getPublicKey() {
        RSA rsa = SecureUtil.rsa();
        String publicKey = rsa.getPublicKeyBase64();
        String privateKey = rsa.getPrivateKeyBase64();
        String keyId = NanoId.randomNanoId();

        redisService.set(AUTH_KEY_PREFIX + keyId, privateKey, (long) 60 * 60);

        AuthPublicKeyResDTO resDTO = new AuthPublicKeyResDTO();
        resDTO.setPublicKey(publicKey);
        resDTO.setKeyId(keyId);

        return resDTO;
    }

    @Override
    public SigninResDTO signin(SigninReqDTO reqDTO) {
        String decryptedPassword = decryptPassword(reqDTO.getKeyId(), reqDTO.getPassword());
        authenticationManager.authenticate(new UsernamePasswordAuthenticationToken(reqDTO.getMobile(), decryptedPassword));

        UserEntity user = userService.getOne(Wrappers.<UserEntity>lambdaQuery().eq(UserEntity::getMobile, reqDTO.getMobile()));

        if (user == null) {
            throw new IllegalArgumentException("用户不存在");
        }

        userService.doAfterSignin(user);

        var accessToken = jwtService.generateJWT(user);
        SigninResDTO resDTO = toDTO(user);
        resDTO.setAccessToken(accessToken);

        return resDTO;
    }

    @Override
    public String decryptPassword(String keyId, String encryptedPassword) {
        String privateKey = (String) redisService.get(AUTH_KEY_PREFIX + keyId);
        if (privateKey == null) {
            throw new BizException("keyId 无效");
        }

        // 使用 Base64 解码私钥和加密数据
        byte[] privateKeyBytes = Base64.decode(privateKey);
        byte[] encryptedBytes = Base64.decode(encryptedPassword);

        RSA rsa = new RSA(privateKeyBytes, null);
        try {
            byte[] decryptedBytes = rsa.decrypt(encryptedBytes, KeyType.PrivateKey);
            return new String(decryptedBytes);
        } catch (Exception e) {
            throw new BizException("用户名或密码错误");
        }
    }

    @Override
    public String encryptPassword(String keyId, String publicKey, String plainText) {
        String privateKey = (String) redisService.get(AUTH_KEY_PREFIX + keyId);
        if (privateKey == null) {
            throw new BizException("参数错误");
        }

        // 使用 Base64 解码密钥
        byte[] publicKeyBytes = Base64.decode(publicKey);
        byte[] privateKeyBytes = Base64.decode(privateKey);

        RSA rsa = new RSA(privateKeyBytes, publicKeyBytes);
        byte[] encryptedBytes = rsa.encrypt(plainText.getBytes(), KeyType.PublicKey);
        return Base64.encode(encryptedBytes);
    }

    private SigninResDTO toDTO(UserEntity user) {
        if (user == null) {
            return null;
        }
        SigninResDTO signinResDTO = new SigninResDTO();
        signinResDTO.setUsername(user.getUsername());
        signinResDTO.setRawPassword(user.getRawPassword());
        signinResDTO.setIsBanned(user.getIsBanned());
        signinResDTO.setRole(user.getRole());
        signinResDTO.setEmail(user.getEmail());
        signinResDTO.setMobile(user.getMobile());
        signinResDTO.setLoginAt(user.getLoginAt());
        signinResDTO.setId(user.getId());
        signinResDTO.setRemark(user.getRemark());
        signinResDTO.setCreatedAt(user.getCreatedAt());
        signinResDTO.setUpdatedAt(user.getUpdatedAt());
        signinResDTO.setDeletedAt(user.getDeletedAt());
        return signinResDTO;
    }
}
