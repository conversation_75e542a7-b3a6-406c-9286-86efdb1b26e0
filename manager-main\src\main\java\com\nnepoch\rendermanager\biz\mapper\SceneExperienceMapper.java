package com.nnepoch.rendermanager.biz.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nnepoch.rendermanager.api.dto.response.SceneExperienceResDTO;
import com.nnepoch.rendermanager.biz.entity.SceneExperienceEntity;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR> <PERSON> 2024/6/13
 */
public interface SceneExperienceMapper extends BaseMapper<SceneExperienceEntity> {
    SceneExperienceResDTO selectById(@Param("id") Long id);

    IPage<SceneExperienceResDTO> selectByPage(Page<SceneExperienceEntity> page, @Param(Constants.WRAPPER) Wrapper<SceneExperienceEntity> wrapper);
}
