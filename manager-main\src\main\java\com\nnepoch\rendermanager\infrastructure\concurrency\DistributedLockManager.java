package com.nnepoch.rendermanager.infrastructure.concurrency;

import com.nnepoch.rendermanager.infrastructure.cache.DomainCacheManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

/**
 * 分布式锁管理器
 * 基于Redis实现的分布式锁，支持聚合级别的并发控制
 * <AUTHOR> Architect
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class DistributedLockManager {
    
    private final DomainCacheManager cacheManager;
    
    private static final Duration DEFAULT_LOCK_TIMEOUT = Duration.ofSeconds(30);
    private static final Duration DEFAULT_WAIT_TIMEOUT = Duration.ofSeconds(5);
    
    /**
     * 执行带锁的操作
     */
    public <T> T executeWithLock(String lockKey, Supplier<T> operation) {
        return executeWithLock(lockKey, DEFAULT_LOCK_TIMEOUT, DEFAULT_WAIT_TIMEOUT, operation);
    }
    
    /**
     * 执行带锁的操作（自定义超时时间）
     */
    public <T> T executeWithLock(String lockKey, Duration lockTimeout, Duration waitTimeout, Supplier<T> operation) {
        String lockValue = UUID.randomUUID().toString();
        
        if (acquireLock(lockKey, lockValue, lockTimeout, waitTimeout)) {
            try {
                log.debug("Acquired lock: {} with value: {}", lockKey, lockValue);
                return operation.get();
                
            } finally {
                releaseLock(lockKey, lockValue);
                log.debug("Released lock: {} with value: {}", lockKey, lockValue);
            }
        } else {
            throw new LockAcquisitionException("Failed to acquire lock: " + lockKey);
        }
    }
    
    /**
     * 异步执行带锁的操作
     */
    public <T> CompletableFuture<T> executeWithLockAsync(String lockKey, Supplier<T> operation) {
        return CompletableFuture.supplyAsync(() -> executeWithLock(lockKey, operation));
    }
    
    /**
     * 尝试执行带锁的操作（不等待）
     */
    public <T> java.util.Optional<T> tryExecuteWithLock(String lockKey, Supplier<T> operation) {
        String lockValue = UUID.randomUUID().toString();
        
        if (cacheManager.acquireLock(lockKey, lockValue, DEFAULT_LOCK_TIMEOUT)) {
            try {
                return java.util.Optional.of(operation.get());
            } finally {
                cacheManager.releaseLock(lockKey, lockValue);
            }
        }
        
        return java.util.Optional.empty();
    }
    
    /**
     * 聚合级别的锁
     */
    public <T> T executeWithAggregateLock(String aggregateType, String aggregateId, Supplier<T> operation) {
        String lockKey = buildAggregateLockKey(aggregateType, aggregateId);
        return executeWithLock(lockKey, operation);
    }
    
    /**
     * 业务流程锁
     */
    public <T> T executeWithProcessLock(String processType, String processId, Supplier<T> operation) {
        String lockKey = buildProcessLockKey(processType, processId);
        return executeWithLock(lockKey, Duration.ofMinutes(5), DEFAULT_WAIT_TIMEOUT, operation);
    }
    
    /**
     * 资源锁（如节点分配）
     */
    public <T> T executeWithResourceLock(String resourceType, String resourceId, Supplier<T> operation) {
        String lockKey = buildResourceLockKey(resourceType, resourceId);
        return executeWithLock(lockKey, operation);
    }
    
    /**
     * 批量锁操作
     */
    public <T> T executeWithMultipleLocks(java.util.List<String> lockKeys, Supplier<T> operation) {
        // 按字典序排序，避免死锁
        java.util.List<String> sortedKeys = lockKeys.stream().sorted().toList();
        java.util.List<String> lockValues = new java.util.ArrayList<>();
        java.util.List<String> acquiredLocks = new java.util.ArrayList<>();
        
        try {
            // 逐个获取锁
            for (String lockKey : sortedKeys) {
                String lockValue = UUID.randomUUID().toString();
                lockValues.add(lockValue);
                
                if (cacheManager.acquireLock(lockKey, lockValue, DEFAULT_LOCK_TIMEOUT)) {
                    acquiredLocks.add(lockKey);
                } else {
                    throw new LockAcquisitionException("Failed to acquire lock: " + lockKey);
                }
            }
            
            // 执行操作
            return operation.get();
            
        } finally {
            // 释放所有已获取的锁（逆序释放）
            for (int i = acquiredLocks.size() - 1; i >= 0; i--) {
                String lockKey = acquiredLocks.get(i);
                String lockValue = lockValues.get(sortedKeys.indexOf(lockKey));
                cacheManager.releaseLock(lockKey, lockValue);
            }
        }
    }
    
    /**
     * 可重入锁支持
     */
    public <T> T executeWithReentrantLock(String lockKey, Supplier<T> operation) {
        String threadId = Thread.currentThread().getName();
        String lockValue = threadId + ":" + UUID.randomUUID().toString();
        
        return executeWithLock(lockKey + ":" + threadId, DEFAULT_LOCK_TIMEOUT, DEFAULT_WAIT_TIMEOUT, operation);
    }
    
    /**
     * 读写锁支持
     */
    public <T> T executeWithReadLock(String lockKey, Supplier<T> operation) {
        String readLockKey = lockKey + ":read:" + UUID.randomUUID().toString();
        return executeWithLock(readLockKey, operation);
    }
    
    public <T> T executeWithWriteLock(String lockKey, Supplier<T> operation) {
        String writeLockKey = lockKey + ":write";
        return executeWithLock(writeLockKey, Duration.ofMinutes(1), DEFAULT_WAIT_TIMEOUT, operation);
    }
    
    /**
     * 获取锁（带重试）
     */
    private boolean acquireLock(String lockKey, String lockValue, Duration lockTimeout, Duration waitTimeout) {
        long waitUntil = System.currentTimeMillis() + waitTimeout.toMillis();
        
        while (System.currentTimeMillis() < waitUntil) {
            if (cacheManager.acquireLock(lockKey, lockValue, lockTimeout)) {
                return true;
            }
            
            try {
                Thread.sleep(100); // 100ms重试间隔
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                return false;
            }
        }
        
        return false;
    }
    
    /**
     * 释放锁
     */
    private void releaseLock(String lockKey, String lockValue) {
        try {
            cacheManager.releaseLock(lockKey, lockValue);
        } catch (Exception e) {
            log.error("Failed to release lock: {} with value: {}", lockKey, lockValue, e);
        }
    }
    
    /**
     * 构建聚合锁键
     */
    private String buildAggregateLockKey(String aggregateType, String aggregateId) {
        return String.format("lock:aggregate:%s:%s", aggregateType, aggregateId);
    }
    
    /**
     * 构建流程锁键
     */
    private String buildProcessLockKey(String processType, String processId) {
        return String.format("lock:process:%s:%s", processType, processId);
    }
    
    /**
     * 构建资源锁键
     */
    private String buildResourceLockKey(String resourceType, String resourceId) {
        return String.format("lock:resource:%s:%s", resourceType, resourceId);
    }
    
    /**
     * 检查锁状态
     */
    public boolean isLocked(String lockKey) {
        try {
            return cacheManager.getProjection("locks", lockKey, String.class).isPresent();
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 获取锁信息
     */
    public LockInfo getLockInfo(String lockKey) {
        try {
            java.util.Optional<String> lockValue = cacheManager.getProjection("locks", lockKey, String.class);
            if (lockValue.isPresent()) {
                return LockInfo.builder()
                    .lockKey(lockKey)
                    .lockValue(lockValue.get())
                    .locked(true)
                    .build();
            }
        } catch (Exception e) {
            log.error("Failed to get lock info: {}", lockKey, e);
        }
        
        return LockInfo.builder()
            .lockKey(lockKey)
            .locked(false)
            .build();
    }
    
    /**
     * 强制释放锁（管理员操作）
     */
    public boolean forceReleaseLock(String lockKey) {
        try {
            // 直接删除锁键
            cacheManager.evictProjection("locks", lockKey);
            log.warn("Force released lock: {}", lockKey);
            return true;
        } catch (Exception e) {
            log.error("Failed to force release lock: {}", lockKey, e);
            return false;
        }
    }
}

/**
 * 锁获取异常
 */
class LockAcquisitionException extends RuntimeException {
    public LockAcquisitionException(String message) {
        super(message);
    }
}

/**
 * 锁信息
 */
@lombok.Builder
@lombok.Data
class LockInfo {
    private String lockKey;
    private String lockValue;
    private boolean locked;
    private java.time.LocalDateTime acquiredAt;
    private java.time.LocalDateTime expiresAt;
}
