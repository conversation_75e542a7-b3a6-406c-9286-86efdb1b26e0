package com.nnepoch.rendermanager.api.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "场景信息,开放接口")
public class OpenapiSceneResDTO extends SceneResDTO {
    @Schema(description = "WebSocket 连接配置")
    private WebSocketConfigDTO webSocketConfig;

    @Data
    public static class WebSocketConfigDTO {
        @Schema(description = "WebSocket 访问令牌, 用于第三方ws连接授权")
        private String accessToken;
    }
}
