package com.nnepoch.rendermanager.interfaces.rest.user;

import com.nnepoch.rendermanager.application.user.command.CreateUserCommand;
import com.nnepoch.rendermanager.application.user.dto.UserDTO;
import com.nnepoch.rendermanager.application.user.service.UserApplicationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Optional;

/**
 * 用户控制器 (DDD版本)
 * <AUTHOR> Migration
 */
@RestController
@RequestMapping(path = "/api/v2/users", produces = "application/json")
@RequiredArgsConstructor
@Tag(name = "User V2", description = "用户管理接口 (DDD版本)")
@SecurityRequirement(name = "bearerAuth")
public class UserController {
    
    private final UserApplicationService userApplicationService;
    
    @PostMapping
    @Operation(summary = "创建用户", description = "创建新用户")
    @ApiResponse(responseCode = "200", description = "用户创建成功")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<UserDTO> createUser(@Valid @RequestBody CreateUserCommand command) {
        UserDTO user = userApplicationService.createUser(command);
        return ResponseEntity.ok(user);
    }
    
    @GetMapping("/{id}")
    @Operation(summary = "获取用户详情", description = "根据ID获取用户详情")
    @ApiResponse(responseCode = "200", description = "用户详情")
    public ResponseEntity<UserDTO> getUserById(@PathVariable Long id) {
        Optional<UserDTO> user = userApplicationService.getUserById(id);
        return user.map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }
    
    @PutMapping("/{id}/ban")
    @Operation(summary = "封禁用户", description = "封禁指定用户")
    @ApiResponse(responseCode = "200", description = "用户封禁成功")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Void> banUser(@PathVariable Long id) {
        userApplicationService.banUser(id);
        return ResponseEntity.ok().build();
    }
    
    @PutMapping("/{id}/unban")
    @Operation(summary = "解封用户", description = "解封指定用户")
    @ApiResponse(responseCode = "200", description = "用户解封成功")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Void> unbanUser(@PathVariable Long id) {
        userApplicationService.unbanUser(id);
        return ResponseEntity.ok().build();
    }
}
