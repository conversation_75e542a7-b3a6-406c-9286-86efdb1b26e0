package com.nnepoch.rendermanager.biz.service.impl;

import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nnepoch.rendermanager.api.dto.request.SceneScheduleQueryDTO;
import com.nnepoch.rendermanager.api.dto.response.SceneScheduleResDTO;
import com.nnepoch.rendermanager.api.dto.response.base.PageResDTO;
import com.nnepoch.rendermanager.biz.entity.SceneScheduleEntity;
import com.nnepoch.rendermanager.biz.mapper.SceneScheduleMapper;
import com.nnepoch.rendermanager.biz.service.SceneScheduleService;
import com.nnepoch.rendermanager.utils.SqlOrderUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> Che 2024/6/17
 */
@RequiredArgsConstructor
@Service
public class SceneScheduleServiceImpl extends ServiceImpl<SceneScheduleMapper, SceneScheduleEntity> implements SceneScheduleService {
    private final SceneScheduleMapper sceneScheduleMapper;

    @Override
    public PageResDTO<SceneScheduleResDTO> getSceneScheduleList(SceneScheduleQueryDTO queryDTO) {
        Page<SceneScheduleEntity> pageObject = new Page<>(queryDTO.getPage(), queryDTO.getSize());

        LambdaQueryWrapper<SceneScheduleEntity> wrapper = Wrappers.lambdaQuery(SceneScheduleEntity.class)
            .eq(CharSequenceUtil.isNotEmpty(queryDTO.getSceneVersion()), SceneScheduleEntity::getSceneVersion, queryDTO.getSceneVersion());

        if (CharSequenceUtil.isNotEmpty(queryDTO.getSort())) {
            pageObject.addOrder(SqlOrderUtil.parseOrderItem(queryDTO.getSort()));
        }

        IPage<SceneScheduleEntity> page = sceneScheduleMapper.selectPage(pageObject, wrapper);

        return new PageResDTO<>(
            page.getRecords().stream().map(this::toDTO).toList(),
            new PageResDTO.Pagination(page.getCurrent(), page.getSize(), page.getTotal())
        );
    }

    @Override
    public SceneScheduleResDTO getSceneScheduleInfo(Long id) {
        return null;
    }

    private SceneScheduleResDTO toDTO(SceneScheduleEntity entity) {
        SceneScheduleResDTO resDTO = new SceneScheduleResDTO();
        resDTO.setId(entity.getId());
        resDTO.setSceneVersion(entity.getSceneVersion());
        resDTO.setCreatedAt(entity.getCreatedAt());
        resDTO.setUpdatedAt(entity.getUpdatedAt());
        resDTO.setDeletedAt(entity.getDeletedAt());
        resDTO.setRemark(entity.getRemark());
        return resDTO;
    }
}
