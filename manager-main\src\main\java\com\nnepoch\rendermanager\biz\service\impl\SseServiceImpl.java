package com.nnepoch.rendermanager.biz.service.impl;

import com.nnepoch.rendermanager.biz.dto.mq.NodeMetricMQDTO;
import com.nnepoch.rendermanager.biz.service.SseService;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.util.Map;
import java.util.concurrent.*;

/**
 * <AUTHOR> <PERSON> 2024/8/20
 */
@Service
public class SseServiceImpl implements SseService {
    private final Map<String, SseEmitter> emitters = new ConcurrentHashMap<>();

    @Override
    public SseEmitter createEmitter(String nodeSn, String frequency) {
        String key = getKey(nodeSn, frequency);
        SseEmitter emitter = new SseEmitter(Long.MAX_VALUE);
        emitters.put(key, emitter);
        emitter.onCompletion(() -> emitters.remove(key));
        emitter.onTimeout(() -> emitters.remove(key));
        return emitter;
    }

    @Override
    public void sendNodeMetric(String nodeSn, NodeMetricMQDTO nodeMetric, String frequency) {
        String key = getKey(nodeSn, frequency);
        send(key, nodeMetric);
    }

    public void send(String key, NodeMetricMQDTO metric) {
        SseEmitter emitter = emitters.get(key);
        if (emitter != null) {
            try {
                emitter.send(SseEmitter.event().data(metric).id(String.valueOf(System.currentTimeMillis())));
            } catch (IOException e) {
                emitters.remove(key);
                emitter.completeWithError(e);
            }
        }
    }

    private String getKey(String nodeSn, String frequency) {
        return nodeSn + ":" + frequency;
    }
}
