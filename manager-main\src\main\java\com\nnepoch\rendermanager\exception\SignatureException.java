package com.nnepoch.rendermanager.exception;

import lombok.Getter;
import lombok.Setter;
import org.springframework.http.HttpStatus;

import java.util.Map;

@Setter
@Getter
public class SignatureException extends RuntimeException {
    private HttpStatus httpStatus = HttpStatus.BAD_REQUEST;

    private final String code;

    private final String message;

    private Map<String, ?> data;

    public SignatureException(String message) {
        this.code = HttpStatus.BAD_REQUEST.toString();
        this.message = message;
    }

    public SignatureException(String message, Map<String, ?> data) {
        this.data = data;
        this.code = HttpStatus.BAD_REQUEST.toString();
        this.message = message;
    }
}
