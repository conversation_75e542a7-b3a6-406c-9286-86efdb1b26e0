package com.nnepoch.rendermanager.domain.scene.service;

import com.nnepoch.rendermanager.domain.scene.repository.SceneRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 场景领域服务
 * <AUTHOR> Architect
 */
@Service
@RequiredArgsConstructor
public class SceneDomainService {
    
    private final SceneRepository sceneRepository;
    
    /**
     * 验证场景创建的业务规则
     */
    public void validateSceneCreation(String name) {
        if (sceneRepository.existsByName(name)) {
            throw new IllegalArgumentException("场景名称已存在: " + name);
        }
    }
    
    /**
     * 验证场景版本创建的业务规则
     */
    public void validateSceneVersionCreation(String name, String version) {
        if (sceneRepository.existsByNameAndVersion(name, version)) {
            throw new IllegalArgumentException("场景版本已存在: " + name + "@" + version);
        }
    }
    
    /**
     * 检查场景是否可以启动
     */
    public boolean canStartScene(String sceneName) {
        return sceneRepository.findByName(sceneName)
            .map(scene -> scene.isAvailable() && !scene.isBanned())
            .orElse(false);
    }
}
