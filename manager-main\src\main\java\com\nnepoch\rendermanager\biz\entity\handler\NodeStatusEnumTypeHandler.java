package com.nnepoch.rendermanager.biz.entity.handler;

import com.nnepoch.rendermanager.api.enums.NodeStatusEnum;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * <AUTHOR> <PERSON> 2024/7/29
 */
public class NodeStatusEnumTypeHandler extends BaseTypeHandler<NodeStatusEnum> {
    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, NodeStatusEnum parameter, JdbcType jdbcType) throws SQLException {
        ps.setInt(i, parameter.getCode());
    }

    @Override
    public NodeStatusEnum getNullableResult(ResultSet rs, String columnName) throws SQLException {
        Integer code = rs.getInt(columnName);
        return NodeStatusEnum.fromCode(code);
    }

    @Override
    public NodeStatusEnum getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        Integer code = rs.getInt(columnIndex);
        return NodeStatusEnum.fromCode(code);
    }

    @Override
    public NodeStatusEnum getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        Integer code = cs.getInt(columnIndex);
        return NodeStatusEnum.fromCode(code);
    }
}
