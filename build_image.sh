#!/bin/bash

default_name=rmp-api
# 获取当前日期作为默认标签
default_tag=$(date +%Y%m%d%H)

# 显示脚本的帮助信息
show_help() {
    echo "使用方法: ./build_image.sh [选项]"
    echo "选项:"
    echo "  -n, --name <名称>     设置Docker镜像的名称"
    echo "  -t, --tag <标签>      设置Docker镜像的标签"
    echo "  -h, --help           显示帮助信息"
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    key="$1"
    case $key in
        -t|--tag)
        if [ -z "$2" ]; then
            echo "标签参数不能为空"
            show_help
            exit 1
        fi
        # 使用参数中指定的标签
        tag="$2"
        shift
        shift
        ;;
        -n|--name)
        if [ -z "$2" ]; then
            echo "名称参数不能为空"
            show_help
            exit 1
        fi
        # 使用参数中指定的名称
        name="$2"
        shift
        shift
        ;;
        -h|--help)
        # 显示帮助信息
        show_help
        exit 0
        ;;
        *)
        # 未知参数
        echo "未知参数: $1"
        exit 1
        ;;
    esac
done

# 如果没有指定名称，则使用默认名称
if [ -z "$name" ]; then
    name=$default_name
fi

# 如果没有指定标签，则使用默认标签
if [ -z "$tag" ]; then
    tag=$default_tag
fi

# 编译项目
echo ">> 开始编译项目..."
mvn clean package -DskipTests

# 构建Docker镜像
echo ">> 开始构建Docker镜像..."
docker build -t docker.dtc.io/dtc/$name:$tag .
if [ $? -eq 0 ]; then
  echo "Docker镜像构建完成。镜像标签为: $tag"
else
  echo "Docker镜像构建失败"
  exit 1
fi

# 推送Docker镜像到ECR
docker push docker.dtc.io/dtc/$name:$tag

if [ $? -eq 0 ]; then
  echo ">> Docker镜像推送完成。镜像: docker.dtc.io/dtc/$name:$tag"
else
  echo "Docker镜像推送失败"
  exit 1
fi