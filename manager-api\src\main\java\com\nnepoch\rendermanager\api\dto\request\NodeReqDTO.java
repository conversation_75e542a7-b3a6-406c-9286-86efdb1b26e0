package com.nnepoch.rendermanager.api.dto.request;

import com.nnepoch.rendermanager.api.enums.NodeStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR> 2024/6/18
 */
@Data
@Schema(description = "节点请求DTO")
public class NodeReqDTO {
    private NodeStatusEnum status;

    @Schema(description = "是否开启日志采集")
    private Boolean enableLogCollection;
    private String organization;
    private String mobile;
    private String email;
    private Long ownedBy;
    private Integer maxSceneInstanceCount;
    private String remark;
}
