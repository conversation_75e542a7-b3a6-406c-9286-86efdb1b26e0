package com.nnepoch.rendermanager.application.node.service;

import com.nnepoch.rendermanager.application.node.command.RegisterNodeCommand;
import com.nnepoch.rendermanager.application.node.dto.NodeDTO;
import com.nnepoch.rendermanager.domain.node.aggregate.Node;
import com.nnepoch.rendermanager.domain.node.repository.NodeRepository;
import com.nnepoch.rendermanager.domain.node.service.NodeDomainService;
import com.nnepoch.rendermanager.domain.node.valueobject.NodeAddress;
import com.nnepoch.rendermanager.domain.node.valueobject.NodeSpec;
import com.nnepoch.rendermanager.domain.shared.valueobject.NodeId;
import com.nnepoch.rendermanager.domain.shared.valueobject.UserId;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 节点应用服务
 * <AUTHOR> Migration
 */
@Service
@RequiredArgsConstructor
@Transactional
public class NodeApplicationService {
    
    private final NodeRepository nodeRepository;
    private final NodeDomainService nodeDomainService;
    
    /**
     * 注册节点
     */
    public NodeDTO registerNode(RegisterNodeCommand command) {
        // 1. 验证业务规则
        nodeDomainService.validateNodeRegistration(command.getSn(), command.getHostname());
        
        // 2. 创建值对象
        NodeAddress address = new NodeAddress(command.getSn(), command.getHostname(), command.getHashId());
        NodeSpec spec = new NodeSpec(
            command.getOs(), 
            command.getOsVersion(), 
            command.getMaxSceneInstanceCount(),
            command.getOrganization(),
            command.getMobile(),
            command.getEmail()
        );
        
        // 3. 创建节点聚合
        NodeId nodeId = NodeId.of(generateNodeId());
        UserId ownerId = UserId.of(command.getOwnedBy());
        Node node = Node.register(nodeId, address, spec, ownerId);
        
        // 4. 保存节点
        nodeRepository.save(node);
        
        return NodeDTO.fromDomain(node);
    }
    
    /**
     * 节点上线
     */
    public void bringNodeOnline(String sn) {
        Node node = nodeRepository.findBySn(sn)
            .orElseThrow(() -> new IllegalArgumentException("节点不存在: " + sn));
        
        node.goOnline();
        nodeRepository.save(node);
    }
    
    /**
     * 节点下线
     */
    public void bringNodeOffline(String sn) {
        Node node = nodeRepository.findBySn(sn)
            .orElseThrow(() -> new IllegalArgumentException("节点不存在: " + sn));
        
        node.goOffline();
        nodeRepository.save(node);
    }
    
    /**
     * 获取最佳节点 (负载最低的在线节点)
     */
    @Transactional(readOnly = true)
    public Optional<NodeDTO> getBestAvailableNode() {
        return nodeDomainService.findBestAvailableNode()
            .map(NodeDTO::fromDomain);
    }
    
    /**
     * 根据ID获取节点
     */
    @Transactional(readOnly = true)
    public Optional<NodeDTO> getNodeById(Long id) {
        return nodeRepository.findById(NodeId.of(id))
            .map(NodeDTO::fromDomain);
    }
    
    /**
     * 根据SN获取节点
     */
    @Transactional(readOnly = true)
    public Optional<NodeDTO> getNodeBySn(String sn) {
        return nodeRepository.findBySn(sn)
            .map(NodeDTO::fromDomain);
    }
    
    /**
     * 获取在线节点列表
     */
    @Transactional(readOnly = true)
    public List<NodeDTO> getOnlineNodes() {
        return nodeRepository.findOnlineNodes().stream()
            .map(NodeDTO::fromDomain)
            .collect(Collectors.toList());
    }
    
    /**
     * 启用节点日志收集
     */
    public void enableLogCollection(Long nodeId) {
        Node node = nodeRepository.findById(NodeId.of(nodeId))
            .orElseThrow(() -> new IllegalArgumentException("节点不存在"));
        
        node.enableLogCollection();
        nodeRepository.save(node);
    }
    
    /**
     * 禁用节点日志收集
     */
    public void disableLogCollection(Long nodeId) {
        Node node = nodeRepository.findById(NodeId.of(nodeId))
            .orElseThrow(() -> new IllegalArgumentException("节点不存在"));
        
        node.disableLogCollection();
        nodeRepository.save(node);
    }
    
    /**
     * 节点启动场景实例
     */
    public void startSceneInstance(String sn) {
        Node node = nodeRepository.findBySn(sn)
            .orElseThrow(() -> new IllegalArgumentException("节点不存在: " + sn));
        
        node.startSceneInstance();
        nodeRepository.save(node);
    }
    
    /**
     * 节点停止场景实例
     */
    public void stopSceneInstance(String sn) {
        Node node = nodeRepository.findBySn(sn)
            .orElseThrow(() -> new IllegalArgumentException("节点不存在: " + sn));
        
        node.stopSceneInstance();
        nodeRepository.save(node);
    }
    
    private Long generateNodeId() {
        return System.currentTimeMillis(); // 简化实现
    }
}
