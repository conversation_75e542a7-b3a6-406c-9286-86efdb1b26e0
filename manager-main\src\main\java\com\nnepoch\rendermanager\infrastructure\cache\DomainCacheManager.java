package com.nnepoch.rendermanager.infrastructure.cache;

import com.nnepoch.rendermanager.domain.shared.valueobject.EntityId;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.List;
import java.util.Optional;
import java.util.Set;

/**
 * 领域驱动的缓存管理器
 * 提供聚合级别的缓存策略和失效机制
 * <AUTHOR> Architect
 */
@Component
@Slf4j
public class DomainCacheManager {

    private final RedisTemplate<String, Object> redisTemplate;

    public DomainCacheManager(RedisTemplate<String, Object> redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    // 缓存键前缀
    private static final String AGGREGATE_PREFIX = "aggregate:";
    private static final String PROJECTION_PREFIX = "projection:";
    private static final String QUERY_PREFIX = "query:";
    
    // 默认过期时间
    private static final Duration DEFAULT_TTL = Duration.ofHours(1);
    private static final Duration PROJECTION_TTL = Duration.ofMinutes(30);
    private static final Duration QUERY_TTL = Duration.ofMinutes(15);
    
    /**
     * 缓存聚合根
     */
    public <T> void cacheAggregate(EntityId aggregateId, String aggregateType, T aggregate) {
        String key = buildAggregateKey(aggregateType, aggregateId.getValue().toString());
        
        try {
            redisTemplate.opsForValue().set(key, aggregate, DEFAULT_TTL);
            
            // 添加到聚合类型集合中，便于批量失效
            String typeSetKey = buildAggregateTypeSetKey(aggregateType);
            redisTemplate.opsForSet().add(typeSetKey, aggregateId.getValue().toString());
            redisTemplate.expire(typeSetKey, Duration.ofDays(1));
            
            log.debug("Cached aggregate: {} with key: {}", aggregateType, key);
            
        } catch (Exception e) {
            log.error("Failed to cache aggregate: {}", key, e);
        }
    }
    
    /**
     * 获取缓存的聚合根
     */
    @SuppressWarnings("unchecked")
    public <T> Optional<T> getAggregate(EntityId aggregateId, String aggregateType, Class<T> clazz) {
        String key = buildAggregateKey(aggregateType, aggregateId.getValue().toString());
        
        try {
            Object cached = redisTemplate.opsForValue().get(key);
            if (cached != null) {
                log.debug("Cache hit for aggregate: {}", key);
                return Optional.of((T) cached);
            }
            
            log.debug("Cache miss for aggregate: {}", key);
            return Optional.empty();
            
        } catch (Exception e) {
            log.error("Failed to get cached aggregate: {}", key, e);
            return Optional.empty();
        }
    }
    
    /**
     * 失效聚合缓存
     */
    public void evictAggregate(EntityId aggregateId, String aggregateType) {
        String key = buildAggregateKey(aggregateType, aggregateId.getValue().toString());
        
        try {
            redisTemplate.delete(key);
            
            // 从类型集合中移除
            String typeSetKey = buildAggregateTypeSetKey(aggregateType);
            redisTemplate.opsForSet().remove(typeSetKey, aggregateId.getValue().toString());
            
            log.debug("Evicted aggregate cache: {}", key);
            
        } catch (Exception e) {
            log.error("Failed to evict aggregate cache: {}", key, e);
        }
    }
    
    /**
     * 批量失效某类型的所有聚合缓存
     */
    public void evictAggregatesByType(String aggregateType) {
        String typeSetKey = buildAggregateTypeSetKey(aggregateType);
        
        try {
            Set<Object> aggregateIds = redisTemplate.opsForSet().members(typeSetKey);
            if (aggregateIds != null && !aggregateIds.isEmpty()) {
                List<String> keys = aggregateIds.stream()
                    .map(id -> buildAggregateKey(aggregateType, id.toString()))
                    .toList();
                
                redisTemplate.delete(keys);
                redisTemplate.delete(typeSetKey);
                
                log.info("Evicted {} aggregate caches of type: {}", keys.size(), aggregateType);
            }
            
        } catch (Exception e) {
            log.error("Failed to evict aggregates by type: {}", aggregateType, e);
        }
    }
    
    /**
     * 缓存投影数据
     */
    public <T> void cacheProjection(String projectionName, String key, T data) {
        String cacheKey = buildProjectionKey(projectionName, key);
        
        try {
            redisTemplate.opsForValue().set(cacheKey, data, PROJECTION_TTL);
            log.debug("Cached projection: {} with key: {}", projectionName, cacheKey);
            
        } catch (Exception e) {
            log.error("Failed to cache projection: {}", cacheKey, e);
        }
    }
    
    /**
     * 获取缓存的投影数据
     */
    @SuppressWarnings("unchecked")
    public <T> Optional<T> getProjection(String projectionName, String key, Class<T> clazz) {
        String cacheKey = buildProjectionKey(projectionName, key);
        
        try {
            Object cached = redisTemplate.opsForValue().get(cacheKey);
            if (cached != null) {
                return Optional.of((T) cached);
            }
            return Optional.empty();
            
        } catch (Exception e) {
            log.error("Failed to get cached projection: {}", cacheKey, e);
            return Optional.empty();
        }
    }
    
    /**
     * 失效投影缓存
     */
    public void evictProjection(String projectionName, String key) {
        String cacheKey = buildProjectionKey(projectionName, key);
        redisTemplate.delete(cacheKey);
        log.debug("Evicted projection cache: {}", cacheKey);
    }
    
    /**
     * 批量失效投影缓存
     */
    public void evictProjectionsByPattern(String projectionName, String pattern) {
        String searchPattern = buildProjectionKey(projectionName, pattern);
        
        try {
            Set<String> keys = redisTemplate.keys(searchPattern);
            if (keys != null && !keys.isEmpty()) {
                redisTemplate.delete(keys);
                log.info("Evicted {} projection caches with pattern: {}", keys.size(), searchPattern);
            }
            
        } catch (Exception e) {
            log.error("Failed to evict projections by pattern: {}", searchPattern, e);
        }
    }
    
    /**
     * 缓存查询结果
     */
    public <T> void cacheQuery(String queryKey, T result) {
        String cacheKey = buildQueryKey(queryKey);
        
        try {
            redisTemplate.opsForValue().set(cacheKey, result, QUERY_TTL);
            log.debug("Cached query result: {}", cacheKey);
            
        } catch (Exception e) {
            log.error("Failed to cache query result: {}", cacheKey, e);
        }
    }
    
    /**
     * 获取缓存的查询结果
     */
    @SuppressWarnings("unchecked")
    public <T> Optional<T> getQueryResult(String queryKey, Class<T> clazz) {
        String cacheKey = buildQueryKey(queryKey);
        
        try {
            Object cached = redisTemplate.opsForValue().get(cacheKey);
            if (cached != null) {
                return Optional.of((T) cached);
            }
            return Optional.empty();
            
        } catch (Exception e) {
            log.error("Failed to get cached query result: {}", cacheKey, e);
            return Optional.empty();
        }
    }
    
    /**
     * 分布式锁
     */
    public boolean acquireLock(String lockKey, String lockValue, Duration expiration) {
        try {
            Boolean acquired = redisTemplate.opsForValue()
                .setIfAbsent(buildLockKey(lockKey), lockValue, expiration);
            return Boolean.TRUE.equals(acquired);
            
        } catch (Exception e) {
            log.error("Failed to acquire lock: {}", lockKey, e);
            return false;
        }
    }
    
    /**
     * 释放分布式锁
     */
    public boolean releaseLock(String lockKey, String lockValue) {
        String script = """
            if redis.call('get', KEYS[1]) == ARGV[1] then
                return redis.call('del', KEYS[1])
            else
                return 0
            end
            """;
        
        try {
            Long result = redisTemplate.execute((RedisCallback<Long>) connection -> {
                byte[] scriptBytes = script.getBytes();
                byte[] keyBytes = buildLockKey(lockKey).getBytes();
                byte[] valueBytes = lockValue.getBytes();

                return connection.eval(scriptBytes, org.springframework.data.redis.connection.ReturnType.INTEGER, 1, keyBytes, valueBytes);
            });

            return Long.valueOf(1).equals(result);

        } catch (Exception e) {
            log.error("Failed to release lock: {}", lockKey, e);
            return false;
        }
    }
    
    /**
     * 获取缓存统计信息
     */
    public CacheStatistics getCacheStatistics() {
        try {
            return CacheStatistics.builder()
                .totalKeys(getTotalKeys())
                .memoryUsage(0L) // 简化实现
                .hitRate(calculateHitRate())
                .build();

        } catch (Exception e) {
            log.error("Failed to get cache statistics", e);
            return CacheStatistics.builder().build();
        }
    }
    
    private String buildAggregateKey(String type, String id) {
        return AGGREGATE_PREFIX + type + ":" + id;
    }
    
    private String buildAggregateTypeSetKey(String type) {
        return AGGREGATE_PREFIX + "types:" + type;
    }
    
    private String buildProjectionKey(String projection, String key) {
        return PROJECTION_PREFIX + projection + ":" + key;
    }
    
    private String buildQueryKey(String key) {
        return QUERY_PREFIX + key;
    }
    
    private String buildLockKey(String key) {
        return "lock:" + key;
    }
    
    private long getTotalKeys() {
        try {
            Long result = redisTemplate.execute((RedisCallback<Long>) connection -> connection.dbSize());
            return result != null ? result : 0L;
        } catch (Exception e) {
            return 0;
        }
    }

    private double calculateHitRate() {
        // 计算缓存命中率
        return 0.0; // 简化实现
    }
}

/**
 * 缓存统计信息
 */
@lombok.Builder
@lombok.Data
class CacheStatistics {
    private long totalKeys;
    private long memoryUsage;
    private double hitRate;
}
