package com.nnepoch.rendermanager.infrastructure.persistence.user;

import com.nnepoch.rendermanager.api.enums.UserRoleEnum;
import com.nnepoch.rendermanager.domain.shared.valueobject.UserId;
import com.nnepoch.rendermanager.domain.user.aggregate.User;
import com.nnepoch.rendermanager.domain.user.repository.UserRepository;
import com.nnepoch.rendermanager.infrastructure.cache.DomainCacheManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 缓存感知的用户仓储实现
 * 在原有仓储基础上添加缓存层
 * <AUTHOR> Architect
 */
@Repository
@Primary
@RequiredArgsConstructor
@Slf4j
public class CachedUserRepositoryImpl implements UserRepository {
    
    private final UserRepositoryImpl delegate;
    private final DomainCacheManager cacheManager;
    
    private static final String AGGREGATE_TYPE = "User";
    private static final String USERNAME_INDEX_PREFIX = "username:";
    private static final String EMAIL_INDEX_PREFIX = "email:";
    private static final String MOBILE_INDEX_PREFIX = "mobile:";
    
    @Override
    public Optional<User> findById(UserId id) {
        // 1. 尝试从缓存获取
        Optional<User> cached = cacheManager.getAggregate(id, AGGREGATE_TYPE, User.class);
        if (cached.isPresent()) {
            log.debug("Cache hit for user: {}", id);
            return cached;
        }
        
        // 2. 从数据库加载
        Optional<User> user = delegate.findById(id);
        
        // 3. 缓存结果
        user.ifPresent(u -> {
            cacheManager.cacheAggregate(id, AGGREGATE_TYPE, u);
            cacheSecondaryIndexes(u);
        });
        
        return user;
    }
    
    @Override
    public void save(User user) {
        // 1. 保存到数据库
        delegate.save(user);
        
        // 2. 更新缓存
        cacheManager.cacheAggregate(user.getId(), AGGREGATE_TYPE, user);
        cacheSecondaryIndexes(user);
        
        log.debug("Saved and cached user: {}", user.getId());
    }
    
    @Override
    public void delete(User user) {
        // 1. 从数据库删除
        delegate.delete(user);
        
        // 2. 清除缓存
        evictUserCache(user);
        
        log.debug("Deleted and evicted user: {}", user.getId());
    }
    
    @Override
    public void deleteById(UserId id) {
        // 先获取用户信息用于清除二级索引缓存
        Optional<User> user = findById(id);
        
        // 删除数据库记录
        delegate.deleteById(id);
        
        // 清除缓存
        cacheManager.evictAggregate(id, AGGREGATE_TYPE);
        user.ifPresent(this::evictSecondaryIndexes);
    }
    
    @Override
    public boolean existsById(UserId id) {
        // 先检查缓存
        Optional<User> cached = cacheManager.getAggregate(id, AGGREGATE_TYPE, User.class);
        if (cached.isPresent()) {
            return true;
        }
        
        // 回退到数据库查询
        return delegate.existsById(id);
    }
    
    @Override
    public Optional<User> findByUsername(String username) {
        // 1. 检查二级索引缓存
        String indexKey = USERNAME_INDEX_PREFIX + username;
        Optional<UserId> cachedId = cacheManager.getProjection("user_index", indexKey, UserId.class);
        
        if (cachedId.isPresent()) {
            return findById(cachedId.get());
        }
        
        // 2. 从数据库查询
        Optional<User> user = delegate.findByUsername(username);
        
        // 3. 缓存结果
        user.ifPresent(u -> {
            cacheManager.cacheAggregate(u.getId(), AGGREGATE_TYPE, u);
            cacheSecondaryIndexes(u);
        });
        
        return user;
    }
    
    @Override
    public Optional<User> findByMobile(String mobile) {
        String indexKey = MOBILE_INDEX_PREFIX + mobile;
        Optional<UserId> cachedId = cacheManager.getProjection("user_index", indexKey, UserId.class);
        
        if (cachedId.isPresent()) {
            return findById(cachedId.get());
        }
        
        Optional<User> user = delegate.findByMobile(mobile);
        user.ifPresent(u -> {
            cacheManager.cacheAggregate(u.getId(), AGGREGATE_TYPE, u);
            cacheSecondaryIndexes(u);
        });
        
        return user;
    }
    
    @Override
    public Optional<User> findByEmail(String email) {
        String indexKey = EMAIL_INDEX_PREFIX + email;
        Optional<UserId> cachedId = cacheManager.getProjection("user_index", indexKey, UserId.class);
        
        if (cachedId.isPresent()) {
            return findById(cachedId.get());
        }
        
        Optional<User> user = delegate.findByEmail(email);
        user.ifPresent(u -> {
            cacheManager.cacheAggregate(u.getId(), AGGREGATE_TYPE, u);
            cacheSecondaryIndexes(u);
        });
        
        return user;
    }
    
    @Override
    public List<User> findByRole(UserRoleEnum role) {
        // 角色查询通常返回多个结果，缓存策略需要特殊处理
        String queryKey = "users_by_role:" + role.name();
        
        Optional<List<User>> cached = cacheManager.getQueryResult(queryKey, List.class);
        if (cached.isPresent()) {
            return cached.get();
        }
        
        List<User> users = delegate.findByRole(role);
        
        // 缓存查询结果（较短的TTL）
        cacheManager.cacheQuery(queryKey, users);
        
        // 同时缓存个别用户
        users.forEach(user -> {
            cacheManager.cacheAggregate(user.getId(), AGGREGATE_TYPE, user);
            cacheSecondaryIndexes(user);
        });
        
        return users;
    }
    
    @Override
    public boolean existsByUsername(String username) {
        // 利用二级索引缓存快速检查
        String indexKey = USERNAME_INDEX_PREFIX + username;
        Optional<UserId> cachedId = cacheManager.getProjection("user_index", indexKey, UserId.class);
        
        if (cachedId.isPresent()) {
            return true;
        }
        
        return delegate.existsByUsername(username);
    }
    
    @Override
    public boolean existsByMobile(String mobile) {
        String indexKey = MOBILE_INDEX_PREFIX + mobile;
        Optional<UserId> cachedId = cacheManager.getProjection("user_index", indexKey, UserId.class);
        
        if (cachedId.isPresent()) {
            return true;
        }
        
        return delegate.existsByMobile(mobile);
    }
    
    @Override
    public boolean existsByEmail(String email) {
        String indexKey = EMAIL_INDEX_PREFIX + email;
        Optional<UserId> cachedId = cacheManager.getProjection("user_index", indexKey, UserId.class);
        
        if (cachedId.isPresent()) {
            return true;
        }
        
        return delegate.existsByEmail(email);
    }
    
    /**
     * 缓存二级索引
     */
    private void cacheSecondaryIndexes(User user) {
        String usernameKey = USERNAME_INDEX_PREFIX + user.getProfile().getUsername();
        String emailKey = EMAIL_INDEX_PREFIX + user.getProfile().getEmail();
        String mobileKey = MOBILE_INDEX_PREFIX + user.getProfile().getMobile();
        
        cacheManager.cacheProjection("user_index", usernameKey, user.getId());
        cacheManager.cacheProjection("user_index", emailKey, user.getId());
        cacheManager.cacheProjection("user_index", mobileKey, user.getId());
    }
    
    /**
     * 清除二级索引缓存
     */
    private void evictSecondaryIndexes(User user) {
        String usernameKey = USERNAME_INDEX_PREFIX + user.getProfile().getUsername();
        String emailKey = EMAIL_INDEX_PREFIX + user.getProfile().getEmail();
        String mobileKey = MOBILE_INDEX_PREFIX + user.getProfile().getMobile();
        
        cacheManager.evictProjection("user_index", usernameKey);
        cacheManager.evictProjection("user_index", emailKey);
        cacheManager.evictProjection("user_index", mobileKey);
    }
    
    /**
     * 清除用户相关的所有缓存
     */
    private void evictUserCache(User user) {
        // 清除聚合缓存
        cacheManager.evictAggregate(user.getId(), AGGREGATE_TYPE);
        
        // 清除二级索引缓存
        evictSecondaryIndexes(user);
        
        // 清除相关查询缓存
        cacheManager.evictProjectionsByPattern("query", "users_by_role:" + user.getRole().name());
    }
    
    /**
     * 预热缓存
     */
    public void warmupCache() {
        log.info("Starting user cache warmup...");
        
        // 预热活跃用户
        List<User> activeUsers = delegate.findByRole(UserRoleEnum.ADMIN);
        activeUsers.addAll(delegate.findByRole(UserRoleEnum.OPERATOR));
        
        activeUsers.forEach(user -> {
            cacheManager.cacheAggregate(user.getId(), AGGREGATE_TYPE, user);
            cacheSecondaryIndexes(user);
        });
        
        log.info("User cache warmup completed, cached {} users", activeUsers.size());
    }
    
    /**
     * 清除所有用户缓存
     */
    public void evictAllCache() {
        log.info("Evicting all user caches...");
        
        cacheManager.evictAggregatesByType(AGGREGATE_TYPE);
        cacheManager.evictProjectionsByPattern("user_index", "*");
        cacheManager.evictProjectionsByPattern("query", "users_by_role:*");
        
        log.info("All user caches evicted");
    }
}
