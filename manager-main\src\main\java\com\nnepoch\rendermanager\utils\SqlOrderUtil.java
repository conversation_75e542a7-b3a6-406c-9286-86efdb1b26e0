package com.nnepoch.rendermanager.utils;

import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.core.metadata.OrderItem;

import java.util.Objects;

/**
 * <AUTHOR> 2024/6/21
 */
public class SqlOrderUtil {
    private SqlOrderUtil() {
        throw new IllegalStateException("Utility class");
    }

    public static OrderItem parseOrderItem(String sort) {
        String[] sortArr = sort.split(",");

        if (sortArr.length != 2) {
            throw new IllegalArgumentException("排序参数格式错误，请按照 'column,asc' 格式排序");
        }

        String column = CharSequenceUtil.toUnderlineCase(sortArr[0]);
        boolean isAsc = Objects.equals(sortArr[1], "asc");

        return new OrderItem().setColumn(column).setAsc(isAsc);
    }
}
