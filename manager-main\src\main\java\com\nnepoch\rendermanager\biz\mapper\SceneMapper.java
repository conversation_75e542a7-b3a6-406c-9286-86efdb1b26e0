package com.nnepoch.rendermanager.biz.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nnepoch.rendermanager.api.dto.response.SceneResDTO;
import com.nnepoch.rendermanager.biz.entity.SceneEntity;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR> <PERSON> 2024/6/13
 */
public interface SceneMapper extends BaseMapper<SceneEntity> {
    IPage<SceneResDTO> selectScenePage(Page<SceneEntity> page, @Param(Constants.WRAPPER) Wrapper<SceneEntity> wrapper, @Param("minioURL") String minioURL);

    SceneResDTO selectSceneById(@Param("id") Long id, @Param("minioURL") String minioURL);
}
