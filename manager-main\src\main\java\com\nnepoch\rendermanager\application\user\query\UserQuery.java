package com.nnepoch.rendermanager.application.user.query;

import com.nnepoch.rendermanager.api.enums.UserRoleEnum;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户查询对象
 * <AUTHOR> Migration
 */
@Data
@Builder
public class UserQuery {
    
    // 基本查询条件
    private String username;
    private String email;
    private String mobile;
    private UserRoleEnum role;
    private Boolean isBanned;
    
    // 时间范围查询
    private LocalDateTime createdAfter;
    private LocalDateTime createdBefore;
    private LocalDateTime loginAfter;
    private LocalDateTime loginBefore;
    
    // 模糊查询
    private String usernameContains;
    private String emailContains;
    
    // 排序
    private String sortBy;
    private SortDirection sortDirection;
    
    // 分页
    private Integer page;
    private Integer size;
    
    // 包含关联数据
    private boolean includePermissions;
    private boolean includeLoginHistory;
    
    public enum SortDirection {
        ASC, DESC
    }
    
    /**
     * 创建简单查询
     */
    public static UserQuery simple() {
        return UserQuery.builder()
            .page(1)
            .size(20)
            .sortBy("createdAt")
            .sortDirection(SortDirection.DESC)
            .build();
    }
    
    /**
     * 按角色查询
     */
    public static UserQuery byRole(UserRoleEnum role) {
        return UserQuery.builder()
            .role(role)
            .page(1)
            .size(20)
            .sortBy("createdAt")
            .sortDirection(SortDirection.DESC)
            .build();
    }
    
    /**
     * 按用户名模糊查询
     */
    public static UserQuery byUsernameContains(String username) {
        return UserQuery.builder()
            .usernameContains(username)
            .page(1)
            .size(20)
            .sortBy("username")
            .sortDirection(SortDirection.ASC)
            .build();
    }
    
    /**
     * 查询活跃用户 (最近30天有登录)
     */
    public static UserQuery activeUsers() {
        return UserQuery.builder()
            .loginAfter(LocalDateTime.now().minusDays(30))
            .isBanned(false)
            .page(1)
            .size(50)
            .sortBy("loginAt")
            .sortDirection(SortDirection.DESC)
            .build();
    }
    
    /**
     * 查询新注册用户 (最近7天)
     */
    public static UserQuery newUsers() {
        return UserQuery.builder()
            .createdAfter(LocalDateTime.now().minusDays(7))
            .page(1)
            .size(20)
            .sortBy("createdAt")
            .sortDirection(SortDirection.DESC)
            .build();
    }
    
    /**
     * 验证查询参数
     */
    public void validate() {
        if (page != null && page < 1) {
            throw new IllegalArgumentException("页码必须大于0");
        }
        if (size != null && (size < 1 || size > 100)) {
            throw new IllegalArgumentException("页面大小必须在1-100之间");
        }
        if (createdAfter != null && createdBefore != null && createdAfter.isAfter(createdBefore)) {
            throw new IllegalArgumentException("创建时间范围无效");
        }
        if (loginAfter != null && loginBefore != null && loginAfter.isAfter(loginBefore)) {
            throw new IllegalArgumentException("登录时间范围无效");
        }
    }
    
    /**
     * 设置默认值
     */
    public UserQuery withDefaults() {
        if (page == null) page = 1;
        if (size == null) size = 20;
        if (sortBy == null) sortBy = "createdAt";
        if (sortDirection == null) sortDirection = SortDirection.DESC;
        return this;
    }
}
