package com.nnepoch.rendermanager.api.dto.request;

import com.nnepoch.rendermanager.api.dto.request.base.PageQueryDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> 2024/6/17
 */
@Schema(title = "场景节点调度记录查询DTO")
//@Schema(description = "场景节点调度记录查询DTO")
@EqualsAndHashCode(callSuper = true)
@Data
public class SceneScheduleQueryDTO extends PageQueryDTO {
    private String sceneVersion;
    private String nodeStatus;
    private String nodeName;
    private String nodeHashId;
    private String nodeOrganization;
}
