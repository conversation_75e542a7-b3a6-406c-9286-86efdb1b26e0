package com.nnepoch.rendermanager.config.interceptors;

import com.nnepoch.rendermanager.biz.entity.UserEntity;
import com.nnepoch.rendermanager.biz.service.JwtService;
import com.nnepoch.rendermanager.biz.service.UserService;
import com.nnepoch.rendermanager.config.Constants;
import com.nnepoch.rendermanager.config.properties.AppOpenApiProperties;
import com.nnepoch.rendermanager.exception.SignatureException;
import com.nnepoch.rendermanager.utils.SignatureUtil;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
@RequiredArgsConstructor
@Order(Ordered.HIGHEST_PRECEDENCE)
@Slf4j
public class SignatureInterceptor implements HandlerInterceptor {
    private final AppOpenApiProperties appOpenApiProperties;
    private final UserService userService;
    private final JwtService jwtService;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        log.info("Processing request: {} {}", request.getMethod(), request.getRequestURI());
        log.info("Query string: {}", request.getQueryString());
        log.info("Handler: {}", handler);

        String clientId = appOpenApiProperties.getClientId();
        String clientSecret = appOpenApiProperties.getClientSecret();

        Map<String, String> params = new HashMap<>();

        // 指定要获取的参数键
        List<String> requiredKeys = Arrays.asList(
            SignatureUtil.CLIENT_ID_PARAM,
            SignatureUtil.TIMESTAMP_PARAM,
            SignatureUtil.NONCE_PARAM,
            SignatureUtil.SIGN_PARAM
        );

        request.getParameterMap().forEach((key, values) -> {
            if (requiredKeys.contains(key) && values != null && values.length > 0) {
                params.put(key, values[0]);
            }
        });

        if (!params.containsKey(SignatureUtil.CLIENT_ID_PARAM) || !params.get(SignatureUtil.CLIENT_ID_PARAM).equals(clientId)) {
            throw new SignatureException("无效的client_id");
        }

        SignatureUtil.verifySignature(params, clientSecret);

        UserEntity user = userService.getUserByMobileWithPassword(Constants.OpenAPI_MOBILE);
        var authToken = new UsernamePasswordAuthenticationToken(user, user.getMobile(), user.getAuthorities());
        authToken.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
        SecurityContextHolder.getContext().setAuthentication(authToken);

        String accessToken = jwtService.generateJWT(user);
        request.setAttribute("accessToken", accessToken);

        return true;
    }
}
