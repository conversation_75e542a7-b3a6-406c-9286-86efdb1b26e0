package com.nnepoch.rendermanager.biz.dto.mq;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.SneakyThrows;

import java.util.HashMap;
import java.util.Map;


/**
 * <AUTHOR> <PERSON> 2024/6/24
 */
@Data
@AllArgsConstructor
public class SceneListenerMQDTO {
    private SceneListenerRpcActionEnum trigger;
    private SceneListenerRpcPayload data;

    /**
     * 转换为json字符串，用于消息队列的传输
     */
    @SneakyThrows
    @Override
    public String toString() {
        ObjectMapper objectMapper = new ObjectMapper();

        Map<String, Object> payloadMap = new HashMap<>();
        payloadMap.put("trigger", trigger.getAction());
        payloadMap.put("data", data);
        return objectMapper.writeValueAsString(payloadMap);
    }

    @AllArgsConstructor
    @Getter
    public enum SceneListenerRpcActionEnum {
        START_SCENE("scene.start", "打开场景"),
        STOP_SCENE("scene.stop", "关闭场景"),
        RESTART_SCENE("scene.restart", "重启场景");

        private final String action;
        private final String desc;
    }

    public record SceneListenerRpcPayload<T>(String name, T options) {
        public SceneListenerRpcPayload(String name) {
            this(name, null);
        }
    }
}
