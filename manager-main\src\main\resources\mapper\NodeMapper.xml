<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nnepoch.rendermanager.biz.mapper.NodeMapper">
    <resultMap id="BaseResultMap" type="com.nnepoch.rendermanager.api.dto.response.NodeResDTO">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="hashId" column="hash_id" jdbcType="VARCHAR"/>
        <result property="sn" column="sn" jdbcType="VARCHAR"/>
        <result property="hostname" column="hostname" jdbcType="VARCHAR"/>
        <result property="os" column="os" jdbcType="VARCHAR"/>
        <result property="osVersion" column="os_version" jdbcType="VARCHAR"/>
        <result property="status" column="status"
                typeHandler="com.nnepoch.rendermanager.biz.entity.handler.NodeStatusEnumTypeHandler"/>
        <result property="enableLogCollection" column="enable_log_collection" jdbcType="BOOLEAN"/>
        <result property="sceneCount" column="scene_count" jdbcType="INTEGER"/>
        <result property="maxSceneInstanceCount" column="max_scene_instance_count" jdbcType="INTEGER"/>
        <result property="sceneRunningCount" column="scene_running_count" jdbcType="INTEGER"/>
        <result property="sceneSeatsTaken" column="scene_seats_taken" jdbcType="INTEGER"/>
        <result property="organization" column="organization" jdbcType="VARCHAR"/>
        <result property="mobile" column="mobile" jdbcType="VARCHAR"/>
        <result property="email" column="email" jdbcType="VARCHAR"/>
        <result property="createdAt" column="created_at" jdbcType="TIMESTAMP"/>
        <result property="updatedAt" column="updated_at" jdbcType="TIMESTAMP"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="deletedAt" column="deleted_at" jdbcType="TIMESTAMP"/>
        <result property="ownedBy" column="owned_by" jdbcType="BIGINT"/>
        <association property="owner" javaType="com.nnepoch.rendermanager.api.dto.response.SimpleUserResDTO">
            <id property="id" column="owner_id" jdbcType="BIGINT"/>
            <result property="username" column="owner_username" jdbcType="VARCHAR"/>
            <result property="email" column="owner_email" jdbcType="VARCHAR"/>
            <result property="mobile" column="owner_mobile" jdbcType="VARCHAR"/>
            <result property="avatar" column="owner_avatar" jdbcType="VARCHAR"/>
        </association>
    </resultMap>

    <sql id="Base_Column_List">
        n.id,
        n.hash_id,
        n.sn,
        n.hostname,
        n.os,
        n.os_version,
        n.status,
        n.enable_log_collection,
        COUNT(DISTINCT sn.id) AS scene_count,
        n.max_scene_instance_count,
        COUNT(DISTINCT si.id) AS scene_running_count,
        COUNT(DISTINCT CASE WHEN si.session_id IS NOT NULL THEN si.id END) AS scene_seats_taken,
        n.organization,
        n.mobile,
        n.email,
        n.owned_by,
        n.created_at,
        n.updated_at,
        n.remark,
        n.deleted_at,
        u.id AS owner_id,
        u.username AS owner_username,
        u.email AS owner_email,
        u.mobile AS owner_mobile,
        CONCAT('/avatar/9.x/big-smile/svg?seed=', u.id) AS owner_avatar
    </sql>

    <select id="selectNodeById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM public.node n
        LEFT JOIN public.user u ON n.owned_by = u.id
        LEFT JOIN public.scene_node sn ON n.id = sn.node_id
        LEFT JOIN public.scene_instance si ON n.id = si.node_id
        WHERE n.id = #{id}
        GROUP BY n.id, u.id
    </select>

    <select id="selectNodePage" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM public.node n
        LEFT JOIN public.user u ON n.owned_by = u.id
        LEFT JOIN public.scene_node sn ON n.id = sn.node_id
        LEFT JOIN public.scene_instance si ON n.id = si.node_id
        ${ew.customSqlSegment}
        GROUP BY n.id, u.id
    </select>
</mapper>
