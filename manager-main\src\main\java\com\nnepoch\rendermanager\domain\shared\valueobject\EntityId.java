package com.nnepoch.rendermanager.domain.shared.valueobject;

import lombok.EqualsAndHashCode;
import lombok.Getter;

import java.io.Serializable;

/**
 * 实体ID值对象基类
 * <AUTHOR> Migration
 */
@Getter
@EqualsAndHashCode
public abstract class EntityId implements Serializable {
    protected final Long value;

    protected EntityId(Long value) {
        if (value == null || value <= 0) {
            throw new IllegalArgumentException("Entity ID cannot be null or negative");
        }
        this.value = value;
    }

    @Override
    public String toString() {
        return String.valueOf(value);
    }
}
