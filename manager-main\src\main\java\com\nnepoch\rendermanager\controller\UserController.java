package com.nnepoch.rendermanager.controller;

import com.nnepoch.rendermanager.api.dto.request.ModifyPasswordReqDTO;
import com.nnepoch.rendermanager.api.dto.request.SimpleUserQueryDTO;
import com.nnepoch.rendermanager.api.dto.request.UserQueryDTO;
import com.nnepoch.rendermanager.api.dto.request.UserReqDTO;
import com.nnepoch.rendermanager.api.dto.response.UserResDTO;
import com.nnepoch.rendermanager.api.dto.response.base.ActionResDTO;
import com.nnepoch.rendermanager.api.dto.response.base.PageResDTO;
import com.nnepoch.rendermanager.biz.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> Che 2024/6/13
 */
@RestController
@RequestMapping(path = "/api/v1/users", produces = "application/json")
@RequiredArgsConstructor
@Tag(name = "User", description = "用户管理接口")
@SecurityRequirement(name = "bearerAuth")
public class UserController {
    private final UserService userService;

    @GetMapping("simple")
    @Operation(summary = "获取简单用户列表")
    @ApiResponse(responseCode = "200", description = "用户列表")
    public List<Map<String, Object>> getSimpleUserList(@ParameterObject SimpleUserQueryDTO queryDTO) {
        return userService.getSimpleUserList(queryDTO);
    }

    @PreAuthorize("hasRole('ADMIN')")
    @GetMapping
    @Operation(summary = "获取用户列表")
    @ApiResponse(responseCode = "200", description = "用户列表")
    public PageResDTO<UserResDTO> getUserList(@ParameterObject UserQueryDTO queryDTO) {
        return userService.getUserList(queryDTO);
    }

    @GetMapping(value = "/profile")
    @Operation(summary = "获取当前用户信息")
    @ApiResponse(responseCode = "200", description = "当前用户信息")
    public UserResDTO getCurUser() {
        return userService.getCurUser();
    }

    @PreAuthorize("hasRole('ADMIN')")
    @PostMapping
    @Operation(summary = "新增用户")
    @ApiResponse(responseCode = "200", description = "新增用户成功")
    public UserResDTO addUser(@Valid @RequestBody UserReqDTO reqDTO) {
        return userService.addUser(reqDTO);
    }

    @PreAuthorize("hasRole('ADMIN')")
    @PutMapping(value = "/{id}")
    @Operation(summary = "更新用户信息")
    @ApiResponse(responseCode = "200", description = "更新用户信息成功")
    public UserResDTO updateUser(@PathVariable("id") Long id, @Valid @RequestBody UserReqDTO reqDTO) {
        return userService.updateUser(id, reqDTO);
    }

    @PreAuthorize("hasRole('ADMIN')")
    @PatchMapping(value = "/{id}/reset-password")
    @Operation(summary = "重置密码")
    @ApiResponse(responseCode = "200", description = "重置密码成功")
    public ActionResDTO<Map<String, String>> resetPassword(@PathVariable("id") Long id) {
        return userService.resetPassword(id);
    }

    @PatchMapping(value = "/{id}/modify-password")
    @Operation(summary = "更新密码")
    @ApiResponse(responseCode = "200", description = "更新密码成功")
    public ActionResDTO<Void> modifyPassword(@PathVariable("id") Long id, @Valid @RequestBody ModifyPasswordReqDTO reqDTO) {
        return userService.modifyPassword(id, reqDTO);
    }

    @PreAuthorize("hasRole('ADMIN')")
    @PostMapping(value = "/{id}/banned")
    @Operation(summary = "禁用用户")
    @ApiResponse(responseCode = "200", description = "禁用用户成功")
    public ActionResDTO<Void> banUser(@PathVariable("id") Long id) {
        return userService.banUser(id);
    }

    @PreAuthorize("hasRole('ADMIN')")
    @PostMapping(value = "/{id}/permitted")
    @Operation(summary = "启用用户")
    @ApiResponse(responseCode = "200", description = "启用用户成功")
    public ActionResDTO<Void> permitUser(@PathVariable("id") Long id) {
        return userService.permitUser(id);
    }

    @PreAuthorize("hasRole('ADMIN')")
    @DeleteMapping(value = "/{id}")
    @Operation(summary = "删除用户")
    @ApiResponse(responseCode = "200", description = "删除用户成功")
    public ActionResDTO<Void> deleteUser(@PathVariable("id") Long id) {
        return userService.deleteUser(id);
    }
}
