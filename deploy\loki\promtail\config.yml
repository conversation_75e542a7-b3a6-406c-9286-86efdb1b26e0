server:
  http_listen_port: 9080
  grpc_listen_port: 0

positions:
  filename: /tmp/positions.yaml

clients:
  - url: http://loki:3100/loki/api/v1/push

scrape_configs:
  - job_name: docker-logs
    pipeline_stages:
      - docker: {}  # 自动添加 Docker 元数据，如容器名、标签等
    static_configs:
      - targets:
          - localhost
        labels:
          job: "docker-logs"
          __path__: /var/lib/docker/containers/*/*.log  # Docker 容器日志的默认存放位置