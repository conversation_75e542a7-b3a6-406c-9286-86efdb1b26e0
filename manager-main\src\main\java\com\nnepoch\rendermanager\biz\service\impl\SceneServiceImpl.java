package com.nnepoch.rendermanager.biz.service.impl;

import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.nnepoch.rendermanager.api.dto.request.*;
import com.nnepoch.rendermanager.api.dto.response.NodeResDTO;
import com.nnepoch.rendermanager.api.dto.response.SceneInstanceResDTO;
import com.nnepoch.rendermanager.api.dto.response.SceneResDTO;
import com.nnepoch.rendermanager.api.dto.response.base.ActionResDTO;
import com.nnepoch.rendermanager.api.dto.response.base.PageResDTO;
import com.nnepoch.rendermanager.biz.dto.mq.SceneInfoMQDTO;
import com.nnepoch.rendermanager.biz.dto.mq.SceneListenerMQDTO;
import com.nnepoch.rendermanager.biz.entity.*;
import com.nnepoch.rendermanager.biz.mapper.NodeMapper;
import com.nnepoch.rendermanager.biz.mapper.SceneInstanceMapper;
import com.nnepoch.rendermanager.biz.mapper.SceneMapper;
import com.nnepoch.rendermanager.biz.mapper.SceneNodeMapper;
import com.nnepoch.rendermanager.biz.mq.DelayProducer;
import com.nnepoch.rendermanager.biz.mq.RabbitMQSender;
import com.nnepoch.rendermanager.biz.service.NodeService;
import com.nnepoch.rendermanager.biz.service.SceneInstanceService;
import com.nnepoch.rendermanager.biz.service.SceneService;
import com.nnepoch.rendermanager.config.RabbitConfig;
import com.nnepoch.rendermanager.config.properties.AppMinioProperties;
import com.nnepoch.rendermanager.exception.BizException;
import com.nnepoch.rendermanager.utils.SqlOrderUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> Che 2024/6/17
 */
@RequiredArgsConstructor
@Slf4j
@Service
public class SceneServiceImpl extends ServiceImpl<SceneMapper, SceneEntity> implements SceneService {
    private final SceneMapper sceneMapper;
    private final SceneNodeMapper sceneNodeMapper;
    private final SceneInstanceMapper sceneInstanceMapper;
    private final RabbitMQSender rabbitMQSender;
    private final AppMinioProperties appMinioProperties;
    private final SceneInstanceService sceneInstanceService;
    private final NodeMapper nodeMapper;
    private final NodeService nodeService;
    private final DelayProducer delayProducer;

    @Override
    public PageResDTO<SceneResDTO> getSceneList(SceneQueryDTO queryDTO) {
        Page<SceneEntity> pageObject = new Page<>(queryDTO.getPage(), queryDTO.getSize());

        LambdaQueryWrapper<SceneEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(CharSequenceUtil.isNotBlank(queryDTO.getName()), SceneEntity::getName, queryDTO.getName());
        wrapper.apply("s.deleted_at IS NULL");

        if (queryDTO.getIsOfficial() != null) {
            wrapper.eq(SceneEntity::getIsOfficial, queryDTO.getIsOfficial());
        }
        if (queryDTO.getIndustry() != null) {
            wrapper.eq(SceneEntity::getIndustry, queryDTO.getIndustry().getCode());
        }
        if (queryDTO.getNodeId() != null) {
            wrapper.apply("sn.node_id = {0}", queryDTO.getNodeId());
        }

        if (CharSequenceUtil.isNotBlank(queryDTO.getSort())) {
            pageObject.addOrder(SqlOrderUtil.parseOrderItem(queryDTO.getSort()));
        } else {
            pageObject.addOrder(SqlOrderUtil.parseOrderItem("s.id,desc"));
        }

        IPage<SceneResDTO> page = sceneMapper.selectScenePage(pageObject, wrapper, appMinioProperties.getUrl());

        return new PageResDTO<>(
            page.getRecords(),
            new PageResDTO.Pagination(page.getCurrent(), page.getSize(), page.getTotal())
        );
    }

    @Override
    public SceneEntity syncScene(SceneInfoMQDTO scene, NodeResDTO node) {
        SceneEntity sceneEntity = getSceneInfoByName(scene.getDisplayName());

        if (sceneEntity == null) {
            sceneEntity = new SceneEntity();
        }

        sceneEntity.setVersion(scene.getVersion());
        sceneEntity.setName(scene.getDisplayName());
        sceneEntity.setChangelog(scene.getChangelog());

        sceneMapper.insertOrUpdate(sceneEntity);

        syncSceneNodeRef(sceneEntity, node, scene);
        return sceneEntity;
    }

    public SceneResDTO getSceneInfo(Long id) {
        SceneResDTO scene = sceneMapper.selectSceneById(id, appMinioProperties.getUrl());
        if (scene == null) {
            throw new BizException(HttpStatus.NOT_FOUND, "场景不存在");
        }
        return scene;
    }

    @Override
    public SceneEntity getSceneInfoByName(String name) {
        return sceneMapper.selectOne(Wrappers.lambdaQuery(SceneEntity.class).eq(SceneEntity::getName, name));
    }

    @Override
    public SceneResDTO addScene(SceneCreateDTO reqDTO) {
        SceneEntity entity = toEntity(reqDTO);
        sceneMapper.insert(entity);
        return toDTO(entity);
    }

    @Override
    public SceneResDTO updateScene(Long id, SceneUpdateDTO reqDTO) {
        SceneEntity entity = toEntity(reqDTO);
        entity.setId(id);
        sceneMapper.updateById(entity);
        return toDTO(entity);
    }

    @Override
    public ActionResDTO<NodeEntity> startScene(Long id, SceneStartOptionDTO optionDTO) {
        SceneResDTO scene = getSceneInfo(id);

        Integer maxSeatCount = scene.getMaxSeatCount() == null ? 0 : scene.getMaxSeatCount();
        if (scene.getSeatsTaken() >= maxSeatCount) {
            throw new BizException("场景坐席已满");
        }

        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null) {
            // websocket 触发时，user context由 ws 自行管理
            UserEntity currentUser = (UserEntity) authentication.getPrincipal();
            optionDTO.setOwnedBy(currentUser.getId());
        }

        NodeResDTO node = nodeService.getBestNode();

        rabbitMQSender.send(
            RabbitConfig.EXCHANGE_NAME,
            RabbitConfig.getCloudRoutingKey(node.getSn()),
            new SceneListenerMQDTO(
                SceneListenerMQDTO.SceneListenerRpcActionEnum.START_SCENE,
                new SceneListenerMQDTO.SceneListenerRpcPayload<>(scene.getName(), optionDTO)
            ).toString(),
            0
        );

        // FIXME: 返回执行的节点（节点按择优算法选取）
        SceneNodeEntity sceneNode = sceneNodeMapper.selectOne(
            Wrappers.lambdaQuery(SceneNodeEntity.class)
                .eq(SceneNodeEntity::getSceneId, id)
        );
        NodeEntity nodeSelected = nodeMapper.selectById(sceneNode.getNodeId());

        return ActionResDTO.success(nodeSelected);
    }

    @Override
    public ActionResDTO<Void> stopScene(Long id, SceneStopOptionDTO optionDTO) {
        SceneEntity sceneEntity = sceneMapper.selectById(id);
        if (sceneEntity == null) {
            throw new BizException(HttpStatus.NOT_FOUND, "No scene found with id", "场景不存在");
        }

        List<SceneInstanceEntity> sceneInstanceEntities = sceneInstanceMapper.selectList(
            Wrappers.lambdaQuery(SceneInstanceEntity.class)
                .eq(SceneInstanceEntity::getSceneId, id)
                .eq(CharSequenceUtil.isNotBlank(optionDTO.getSessionId()), SceneInstanceEntity::getSessionId, optionDTO.getSessionId())
        );

        if (sceneInstanceEntities.isEmpty() && CharSequenceUtil.isNotBlank(optionDTO.getSessionId())) {
            // 由于异步，场景实例可能还未持久化，需手动追加当前 session 到 mq 执行异步处理
            SceneInstanceEntity instanceFuture = new SceneInstanceEntity();
            instanceFuture.setSessionId(optionDTO.getSessionId());
            instanceFuture.setProcessId(optionDTO.getProcessId());
            sceneInstanceEntities.add(instanceFuture);
        }

        NodeResDTO node = nodeService.getBestNode();

        for (SceneInstanceEntity sceneInstanceEntity : sceneInstanceEntities) {
            optionDTO.setProcessId(sceneInstanceEntity.getProcessId());
            optionDTO.setSessionId(sceneInstanceEntity.getSessionId());

            rabbitMQSender.send(
                RabbitConfig.EXCHANGE_NAME,
                RabbitConfig.getCloudRoutingKey(node.getSn()),
                new SceneListenerMQDTO(
                    SceneListenerMQDTO.SceneListenerRpcActionEnum.STOP_SCENE,
                    new SceneListenerMQDTO.SceneListenerRpcPayload<>(sceneEntity.getName(), optionDTO)
                ).toString(),
                0
            );
        }

        return ActionResDTO.success();
    }

    @Override
    public ActionResDTO<Void> restartScene(Long id) {
        SceneEntity entity = sceneMapper.selectById(id);
        if (entity == null) {
            throw new BizException(HttpStatus.NOT_FOUND, "No scene found with id", "场景不存在");
        }

        List<SceneInstanceEntity> sceneInstanceEntities = sceneInstanceMapper.selectList(
            Wrappers.lambdaQuery(SceneInstanceEntity.class).eq(SceneInstanceEntity::getSceneId, id)
        );

        NodeResDTO node = nodeService.getBestNode();

        sceneInstanceEntities.forEach(sceneInstanceEntity ->
            rabbitMQSender.send(
                RabbitConfig.EXCHANGE_NAME,
                RabbitConfig.getCloudRoutingKey(node.getSn()),
                new SceneListenerMQDTO(
                    SceneListenerMQDTO.SceneListenerRpcActionEnum.RESTART_SCENE,
                    new SceneListenerMQDTO.SceneListenerRpcPayload<>(entity.getName())
                ).toString(),
                0
            )
        );

        return ActionResDTO.success();
    }

    @Override
    public ActionResDTO<Void> banScene(Long id, Integer bannedTime) throws JsonProcessingException {
        SceneEntity entity = new SceneEntity();
        entity.setId(id);
        entity.setIsBanned(true);
        entity.setBannedTime(bannedTime);
        sceneMapper.updateById(entity);

        if (bannedTime > 0) {
            // 延迟任务，解封场景
            Map<String, Object> message = new HashMap<>();
            message.put("type", "scene.permit");
            message.put("sceneId", id);
            message.put("bannedTime", bannedTime);

            ObjectMapper om = new ObjectMapper();
            String messageJson = om.writeValueAsString(message);

            delayProducer.send(messageJson, bannedTime * 60);
        }

        stopScene(id, new SceneStopOptionDTO());

        return ActionResDTO.success();
    }

    @Override
    public ActionResDTO<Void> permitScene(Long id) {
        SceneEntity entity = new SceneEntity();
        entity.setId(id);
        entity.setIsBanned(false);
        entity.setBannedTime(null);
        sceneMapper.updateById(entity);
        return ActionResDTO.success();
    }

    @Override
    public ActionResDTO<Void> freeSceneInstance(Long id) {
        SceneInstanceQueryDTO queryDTO = new SceneInstanceQueryDTO();
        queryDTO.setSceneId(id);
        queryDTO.setSize(1);
        queryDTO.setSort("runTime,desc");

        PageResDTO<SceneInstanceResDTO> instanceList = sceneInstanceService.getList(queryDTO);


        if (instanceList.getData().isEmpty()) {
            throw new BizException(HttpStatus.NOT_FOUND, "无可释放的坐席");
        }

        SceneInstanceResDTO instance = instanceList.getData().getFirst();

        return sceneInstanceService.freeInstance(instance.getId());
    }

    private void syncSceneNodeRef(SceneEntity sceneEntity, NodeResDTO node, SceneInfoMQDTO sceneInfo) {
        SceneNodeEntity sceneNodeEntity = sceneNodeMapper.selectOne(
            Wrappers.lambdaQuery(SceneNodeEntity.class)
                .eq(SceneNodeEntity::getSceneId, sceneEntity.getId())
                .eq(SceneNodeEntity::getNodeId, node.getId())
//                .eq(SceneNodeEntity::getVersion, scene.getVersion())
        );
        if (sceneNodeEntity == null) {
            sceneNodeEntity = new SceneNodeEntity();
            sceneNodeEntity.setSceneId(sceneEntity.getId());
            sceneNodeEntity.setNodeId(node.getId());
            sceneNodeEntity.setVersion(sceneInfo.getVersion());
            sceneNodeEntity.setMaxSeatCount(sceneInfo.getMaxSeatCount());

            sceneNodeMapper.insert(sceneNodeEntity);
        } else {
            sceneNodeEntity.setVersion(sceneInfo.getVersion());
            sceneNodeEntity.setMaxSeatCount(sceneInfo.getMaxSeatCount());

            sceneNodeMapper.updateById(sceneNodeEntity);
        }
    }

    private SceneEntity toEntity(SceneCreateDTO reqDTO) {
        SceneEntity sceneEntity = new SceneEntity();
        sceneEntity.setName(reqDTO.getName());
        if (reqDTO.getThumb() != null) {
            String thumb = reqDTO.getThumb().replace(appMinioProperties.getUrl(), "");
            sceneEntity.setThumb(thumb);
        }
        sceneEntity.setIndustry(reqDTO.getIndustry());
        sceneEntity.setLink(reqDTO.getLink());
        sceneEntity.setIsOfficial(reqDTO.getIsOfficial());
        sceneEntity.setIntro(reqDTO.getIntro());
        sceneEntity.setViewedAt(reqDTO.getViewedAt());
        return sceneEntity;
    }

    private SceneEntity toEntity(SceneUpdateDTO reqDTO) {
        SceneEntity sceneEntity = new SceneEntity();
        sceneEntity.setName(reqDTO.getName());
        if (reqDTO.getThumb() != null) {
            String thumb = reqDTO.getThumb().replace(appMinioProperties.getUrl(), "");
            sceneEntity.setThumb(thumb);
        }
        sceneEntity.setIndustry(reqDTO.getIndustry());
        sceneEntity.setLink(reqDTO.getLink());
        sceneEntity.setIntro(reqDTO.getIntro());
        return sceneEntity;
    }

    private SceneResDTO toDTO(SceneEntity entity) {
        if (entity == null) {
            return null;
        }
        SceneResDTO sceneResDTO = new SceneResDTO();
        sceneResDTO.setName(entity.getName());

        if (CharSequenceUtil.isNotBlank(entity.getThumb())) {
            sceneResDTO.setThumb(appMinioProperties.getUrl() + entity.getThumb());
        }

        sceneResDTO.setVersion(entity.getVersion());
        sceneResDTO.setIndustry(entity.getIndustry());
        sceneResDTO.setLink(entity.getLink());
        sceneResDTO.setSeatsTaken(entity.getSeatsTaken());
        sceneResDTO.setSeatsTotal(entity.getSeatsTotal());
        sceneResDTO.setIsOfficial(entity.getIsOfficial());
        sceneResDTO.setIntro(entity.getIntro());
        sceneResDTO.setChangelog(entity.getChangelog());
        sceneResDTO.setViewedAt(entity.getViewedAt());
        sceneResDTO.setId(entity.getId());
        sceneResDTO.setRemark(entity.getRemark());
        sceneResDTO.setCreatedAt(entity.getCreatedAt());
        sceneResDTO.setUpdatedAt(entity.getUpdatedAt());
        sceneResDTO.setDeletedAt(entity.getDeletedAt());
        return sceneResDTO;
    }
}
