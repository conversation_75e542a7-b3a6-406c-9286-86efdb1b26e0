package com.nnepoch.rendermanager.api.dto.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> 2024/7/2
 */
@Data
@Schema(description = "场景启动选项")
@NoArgsConstructor
public class SceneStartOptionDTO {
    // Agent 约定的启动选项
    private String type = "Start";

    @Schema(description = "websocket session id")
    private String sessionId;

    @Schema(description = "连接模式: public / private")
    private ModeEnum mode;

    @Schema(description = "是否使用硬件编码, 需重启场景生效")
    private Boolean hardwareEncoder;

    @Schema(description = "关联操作人")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long ownedBy;

    @Schema(description = "关联场景体验")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long sceneExperienceId;

    @Schema(description = "场景名称")
    private String resolution;

    @Schema(description = "帧率")
    private Short fps;

    @Schema(description = "比特率")
    private Integer bitrate;

    @Schema(description = "持续时间")
    private Integer duration;

    @Schema(description = "是否重启")
    private Boolean isRestart = false;

    public enum ModeEnum {
        PUBLIC, PRIVATE;
    }
}
