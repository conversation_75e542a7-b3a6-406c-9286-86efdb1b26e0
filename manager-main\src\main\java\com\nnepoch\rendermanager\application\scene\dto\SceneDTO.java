package com.nnepoch.rendermanager.application.scene.dto;

import com.nnepoch.rendermanager.api.enums.SceneIndustryEnum;
import com.nnepoch.rendermanager.domain.scene.aggregate.Scene;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 场景数据传输对象
 * <AUTHOR> Architect
 */
@Data
public class SceneDTO {
    private Long id;
    private String name;
    private String version;
    private String description;
    private SceneIndustryEnum industry;
    private String link;
    private Boolean isOfficial;
    private String intro;
    private String thumb;
    private String changelog;
    private Boolean isBanned;
    private Integer bannedTime;
    private LocalDateTime viewedAt;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    
    // 运行时信息
    private Integer seatsTaken;
    private Integer seatsTotal;
    private Integer availableSeats;
    
    /**
     * 从领域对象转换为DTO
     */
    public static SceneDTO fromDomain(Scene scene) {
        SceneDTO dto = new SceneDTO();
        dto.setId(scene.getId().getValue());
        dto.setName(scene.getConfig().getName());
        dto.setVersion(scene.getConfig().getVersion());
        dto.setDescription(scene.getConfig().getDescription());
        dto.setIndustry(scene.getConfig().getIndustry());
        dto.setLink(scene.getConfig().getLink());
        dto.setIsOfficial(scene.getConfig().isOfficial());
        dto.setIntro(scene.getConfig().getIntro());
        dto.setThumb(scene.getThumb());
        dto.setChangelog(scene.getChangelog());
        dto.setIsBanned(scene.isBanned());
        dto.setBannedTime(scene.getBannedTime());
        dto.setViewedAt(scene.getViewedAt());
        dto.setCreatedAt(scene.getCreatedAt());
        dto.setUpdatedAt(scene.getUpdatedAt());
        dto.setSeatsTaken(scene.getSeatsTaken());
        dto.setSeatsTotal(scene.getSeatsTotal());
        dto.setAvailableSeats(scene.getAvailableSeats());
        return dto;
    }
}
