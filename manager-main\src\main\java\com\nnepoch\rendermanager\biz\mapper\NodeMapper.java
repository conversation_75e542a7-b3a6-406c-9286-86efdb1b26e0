package com.nnepoch.rendermanager.biz.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nnepoch.rendermanager.api.dto.response.NodeResDTO;
import com.nnepoch.rendermanager.biz.entity.NodeEntity;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR> <PERSON> 2024/6/13
 */
public interface NodeMapper extends BaseMapper<NodeEntity> {
    IPage<NodeResDTO> selectNodePage(Page<NodeEntity> page, @Param(Constants.WRAPPER) Wrapper<NodeEntity> wrapper);

    NodeResDTO selectNodeById(@Param("id") Long id);
}
