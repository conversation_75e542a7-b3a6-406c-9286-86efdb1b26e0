# DDD应用验证脚本
Write-Host "🚀 开始验证DDD应用..." -ForegroundColor Green

# 测试健康检查端点
Write-Host "📊 测试健康检查端点..." -ForegroundColor Yellow
try {
    $healthResponse = Invoke-RestMethod -Uri "http://localhost:8080/rest/actuator/health" -Method GET
    Write-Host "✅ 健康检查通过: $($healthResponse.status)" -ForegroundColor Green
} catch {
    Write-Host "❌ 健康检查失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 测试应用信息端点
Write-Host "📋 测试应用信息端点..." -ForegroundColor Yellow
try {
    $infoResponse = Invoke-RestMethod -Uri "http://localhost:8080/rest/actuator/info" -Method GET
    Write-Host "✅ 应用信息获取成功" -ForegroundColor Green
} catch {
    Write-Host "❌ 应用信息获取失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 测试性能指标端点
Write-Host "📈 测试性能指标端点..." -ForegroundColor Yellow
try {
    $metricsResponse = Invoke-RestMethod -Uri "http://localhost:8080/rest/actuator/metrics" -Method GET
    Write-Host "✅ 性能指标获取成功，共 $($metricsResponse.names.Count) 个指标" -ForegroundColor Green
} catch {
    Write-Host "❌ 性能指标获取失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 测试Swagger文档
Write-Host "📚 测试Swagger文档..." -ForegroundColor Yellow
try {
    $swaggerResponse = Invoke-WebRequest -Uri "http://localhost:8080/swagger/api-docs" -Method GET
    if ($swaggerResponse.StatusCode -eq 200) {
        Write-Host "✅ Swagger文档可访问" -ForegroundColor Green
    }
} catch {
    Write-Host "❌ Swagger文档访问失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 测试数据库连接（通过数据源指标）
Write-Host "🗄️ 测试数据库连接..." -ForegroundColor Yellow
try {
    $dbMetrics = Invoke-RestMethod -Uri "http://localhost:8080/rest/actuator/metrics/hikaricp.connections.active" -Method GET
    Write-Host "✅ 数据库连接正常，活跃连接数: $($dbMetrics.measurements[0].value)" -ForegroundColor Green
} catch {
    Write-Host "❌ 数据库连接检查失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 测试Redis连接（如果有相关指标）
Write-Host "🔴 测试Redis连接..." -ForegroundColor Yellow
try {
    $redisMetrics = Invoke-RestMethod -Uri "http://localhost:8080/rest/actuator/metrics/lettuce.command.completion" -Method GET
    Write-Host "✅ Redis连接正常" -ForegroundColor Green
} catch {
    Write-Host "⚠️ Redis指标不可用（可能正常）" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "🎉 DDD应用验证完成！" -ForegroundColor Green
Write-Host "📖 访问 http://localhost:8080/swagger/index.html 查看API文档" -ForegroundColor Cyan
Write-Host "📊 访问 http://localhost:8080/rest/actuator 查看监控端点" -ForegroundColor Cyan
