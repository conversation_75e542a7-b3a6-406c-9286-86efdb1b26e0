package com.nnepoch.rendermanager.controller;

import com.nnepoch.rendermanager.api.dto.request.SceneExperienceQueryDTO;
import com.nnepoch.rendermanager.api.dto.request.SceneExperienceReqDTO;
import com.nnepoch.rendermanager.api.dto.response.SceneExperienceResDTO;
import com.nnepoch.rendermanager.api.dto.response.base.ActionResDTO;
import com.nnepoch.rendermanager.api.dto.response.base.PageResDTO;
import com.nnepoch.rendermanager.biz.service.SceneExperienceService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR> <PERSON> 2024/6/17
 */
@RestController
@RequestMapping(path = "/api/v1/scene-experience", produces = "application/json")
@RequiredArgsConstructor
@Tag(name = "Scene Experience", description = "场景体验相关接口")
@SecurityRequirement(name = "bearerAuth")
public class SceneExperienceController {
    private final SceneExperienceService sceneExperienceService;

    @GetMapping
    @Operation(summary = "获取场景体验列表", description = "根据查询条件获取场景体验列表")
    @ApiResponse(responseCode = "200", description = "场景体验列表")
    public PageResDTO<SceneExperienceResDTO> getSceneExperiences(@Valid @ParameterObject SceneExperienceQueryDTO queryDTO) {
        return sceneExperienceService.getSceneExperiences(queryDTO);
    }

    @GetMapping("/{id}")
    @Operation(summary = "获取场景体验详情", description = "根据场景体验ID获取场景体验详情")
    @ApiResponse(responseCode = "200", description = "场景体验详情")
    public SceneExperienceResDTO getSceneExperience(@PathVariable Long id) {
        return sceneExperienceService.getSceneExperience(id);
    }

    @PostMapping("")
    @Operation(summary = "创建场景体验", description = "创建场景体验")
    @ApiResponse(responseCode = "200", description = "场景体验详情")
    public SceneExperienceResDTO addSceneExperience(@Valid @RequestBody SceneExperienceReqDTO reqDTO) {
        return sceneExperienceService.addSceneExperience(reqDTO);
    }

    @PatchMapping("/{id}")
    @Operation(summary = "更新场景体验", description = "根据场景体验ID更新场景体验")
    @ApiResponse(responseCode = "200", description = "场景体验详情")
    public SceneExperienceResDTO updateSceneExperience(@PathVariable Long id, @Valid @RequestBody SceneExperienceReqDTO reqDTO) {
        return sceneExperienceService.updateSceneExperience(id, reqDTO);
    }

    @PostMapping("/{id}/banned")
    @Operation(summary = "禁用场景体验", description = "根据场景体验ID禁用场景体验")
    @ApiResponse(responseCode = "200", description = "操作结果")
    public ActionResDTO<Void> banSceneExperience(@PathVariable Long id) {
        return sceneExperienceService.banSceneExperience(id);
    }

    @PostMapping("/{id}/permitted")
    @Operation(summary = "启用场景体验", description = "根据场景体验ID启用场景体验")
    @ApiResponse(responseCode = "200", description = "操作结果")
    public ActionResDTO<Void> permitSceneExperience(@PathVariable Long id) {
        return sceneExperienceService.permitSceneExperience(id);
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除场景体验", description = "根据场景体验ID删除场景体验")
    @ApiResponse(responseCode = "200", description = "操作结果")
    public ActionResDTO<Void> deleteSceneExperience(@PathVariable Long id) {
        sceneExperienceService.removeById(id);
        return ActionResDTO.success();
    }
}
