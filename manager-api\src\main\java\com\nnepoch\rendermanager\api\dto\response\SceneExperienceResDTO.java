package com.nnepoch.rendermanager.api.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.nnepoch.rendermanager.api.dto.response.base.BaseResDTO;
import com.nnepoch.rendermanager.api.enums.SceneExperienceStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <AUTHOR> Che 2024/6/17
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "场景体验信息")
public class SceneExperienceResDTO extends BaseResDTO {
    @Schema(description = "hashId")
    private String hashId;

    @Schema(description = "场景ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long sceneId;

    @Schema(description = "场景名称")
    private String sceneName;

    @Schema(description = "场景链接")
    private String sceneLink;

    @Schema(description = "体验链接状态")
    private SceneExperienceStatusEnum status;

    @Schema(description = "是否已禁用")
    private Boolean isBanned;

    @Schema(description = "体验用户名")
    private String username;

    @Schema(description = "体验用户单位")
    private String organization;

    @Schema(description = "体验用户手机号")
    private String mobile;

    @Schema(description = "体验用户邮箱")
    private String email;

    @Schema(description = "体验用户头像")
    private String avatar;

    @Schema(description = "单次体验时长, 单位：分钟")
    private Integer duration;

    @Schema(description = "体验持续时长, 单位：分钟")
    private Integer keepDuration;

    @Schema(description = "体验最大并发数")
    private Integer maxConcurrent;

    @Schema(description = "体验有效期截止日期")
    private Date expiredAt;

    @Schema(description = "最近场景体验开始日期")
    private Date viewedAt;

    @Schema(description = "关联创建者ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long createdBy;

    @Schema(description = "关联创建者信息")
    private SimpleUserResDTO creator;
}
