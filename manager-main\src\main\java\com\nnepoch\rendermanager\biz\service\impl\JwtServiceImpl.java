package com.nnepoch.rendermanager.biz.service.impl;

import com.nimbusds.jose.JOSEException;
import com.nimbusds.jose.JWSHeader;
import com.nimbusds.jose.crypto.MACSigner;
import com.nimbusds.jwt.JWTClaimsSet;
import com.nimbusds.jwt.JWTParser;
import com.nimbusds.jwt.SignedJWT;
import com.nnepoch.rendermanager.biz.entity.UserEntity;
import com.nnepoch.rendermanager.biz.service.JwtService;
import com.nnepoch.rendermanager.config.properties.AppJwtProperties;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.time.Instant;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> Che 2024/6/19
 */
@RequiredArgsConstructor
@Service
public class JwtServiceImpl implements JwtService {
    private final AppJwtProperties appJwtProperties;

    @Override
    public String generateJWT(UserEntity user) {
        var key = appJwtProperties.getKey();
        var algorithm = appJwtProperties.getAlgorithm();
        var header = new JWSHeader(algorithm);
        Map<String, Object> claimsMap = new HashMap<>();
        claimsMap.put("id", user.getId());
        claimsMap.put("mobile", user.getMobile());
        claimsMap.put("role", user.getRole());
        var claimsSet = buildClaimsSet(claimsMap);

        var jwt = new SignedJWT(header, claimsSet);

        try {
            var signer = new MACSigner(key);
            jwt.sign(signer);
        } catch (JOSEException e) {
            throw new RuntimeException("Unable to generate JWT", e);
        }

        return jwt.serialize();
    }

    @Override
    public JWTClaimsSet parseJWT(String token) {
        try {
            return JWTParser.parse(token).getJWTClaimsSet();
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public boolean validateJWT(String token, UserEntity user) {
        var claims = parseJWT(token);
        var issuer = claims.getIssuer();
        var issuedAt = claims.getIssueTime();
        var expirationTime = claims.getExpirationTime();
        var mobile = claims.getClaim("mobile");

        if (issuer.equals(appJwtProperties.getIssuer()) &&
            issuedAt != null && expirationTime != null &&
            mobile.equals(user.getMobile())) {
            var now = Date.from(Instant.now());
            return now.before(expirationTime);
        }


        return false;
    }

    /**
     * 验证token是否符合 jwt 格式规范
     */
    @Override
    public boolean isTokenInValid(String token) {
        // 验证是否符合 header.payload.signature 格式
        return token.split("\\.").length != 3;
    }

    private JWTClaimsSet buildClaimsSet(Map<String, Object> claims) {
        var issuer = appJwtProperties.getIssuer();
        var issuedAt = Instant.now();
        var expirationTime = issuedAt.plus(appJwtProperties.getExpiresIn());

        var builder = new JWTClaimsSet.Builder()
            .issuer(issuer)
            .issueTime(Date.from(issuedAt))
            .expirationTime(Date.from(expirationTime));

        claims.forEach(builder::claim);

        return builder.build();
    }
}
