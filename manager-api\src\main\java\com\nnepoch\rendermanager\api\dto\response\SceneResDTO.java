package com.nnepoch.rendermanager.api.dto.response;

import com.nnepoch.rendermanager.api.dto.response.base.BaseResDTO;
import com.nnepoch.rendermanager.api.enums.SceneIndustryEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <AUTHOR> Che 2024/6/17
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "场景信息")
public class SceneResDTO extends BaseResDTO {
    private String name;
    private String thumb;
    private String version;

    @Schema(description = "行业类型")
    private SceneIndustryEnum industry;

    private String link;

    @Schema(description = "坐席在线数")
    private Integer seatsTaken;

    @Schema(description = "坐席总数")
    private Integer seatsTotal;

    @Schema(description = "最大坐席数")
    private Integer maxSeatCount;

    @Schema(description = "是否官方场景")
    private Boolean isOfficial;

    @Schema(description = "是否禁用")
    private Boolean isBanned;

    @Schema(description = "禁用时间, 单位：分钟 (-1表示永久禁用)")
    private Integer bannedTime;

    @Schema(description = "场景简介")
    private String intro;

    @Schema(description = "更新日志")
    private String changelog;

    @Schema(description = "场景最近浏览时间")
    private Date viewedAt;
}
