package com.nnepoch.rendermanager.domain.scheduling.service;

import com.nnepoch.rendermanager.domain.node.aggregate.Node;
import com.nnepoch.rendermanager.domain.node.repository.NodeRepository;
import com.nnepoch.rendermanager.domain.node.specification.NodeSpecifications;
import com.nnepoch.rendermanager.domain.scene.aggregate.Scene;
import com.nnepoch.rendermanager.domain.shared.specification.Specification;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 资源分配领域服务
 * 实现复杂的资源分配策略和负载均衡算法
 * <AUTHOR> Architect
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ResourceAllocationService {
    
    private final NodeRepository nodeRepository;
    
    /**
     * 为场景分配最优节点
     */
    public Optional<Node> allocateOptimalNode(Scene scene, AllocationStrategy strategy) {
        List<Node> candidateNodes = findCandidateNodes(scene);
        
        if (candidateNodes.isEmpty()) {
            log.warn("No candidate nodes available for scene: {}", scene.getId());
            return Optional.empty();
        }
        
        return switch (strategy) {
            case LEAST_LOADED -> allocateByLeastLoaded(candidateNodes);
            case ROUND_ROBIN -> allocateByRoundRobin(candidateNodes);
            case AFFINITY_BASED -> allocateByAffinity(candidateNodes, scene);
            case PERFORMANCE_OPTIMIZED -> allocateByPerformance(candidateNodes);
            case COST_OPTIMIZED -> allocateByCost(candidateNodes);
        };
    }
    
    /**
     * 查找候选节点
     */
    private List<Node> findCandidateNodes(Scene scene) {
        Specification<Node> baseSpec = NodeSpecifications.isAssignable();
        
        // 根据场景特性添加额外约束
        if (scene.isOfficial()) {
            baseSpec = baseSpec.and(NodeSpecifications.isHighPerformance());
        }
        
        return nodeRepository.findOnlineNodes().stream()
            .filter(baseSpec::isSatisfiedBy)
            .collect(Collectors.toList());
    }
    
    /**
     * 最少负载策略
     */
    private Optional<Node> allocateByLeastLoaded(List<Node> nodes) {
        return nodes.stream()
            .min(Comparator.comparing(this::calculateLoadScore));
    }
    
    /**
     * 轮询策略
     */
    private Optional<Node> allocateByRoundRobin(List<Node> nodes) {
        // 简化实现：基于节点ID取模
        long currentTime = System.currentTimeMillis();
        int index = (int) (currentTime % nodes.size());
        return Optional.of(nodes.get(index));
    }
    
    /**
     * 亲和性策略
     */
    private Optional<Node> allocateByAffinity(List<Node> nodes, Scene scene) {
        // 优先选择已经运行相同场景的节点
        Optional<Node> affinityNode = nodes.stream()
            .filter(node -> hasSceneAffinity(node, scene))
            .min(Comparator.comparing(this::calculateLoadScore));
        
        return affinityNode.or(() -> allocateByLeastLoaded(nodes));
    }
    
    /**
     * 性能优化策略
     */
    private Optional<Node> allocateByPerformance(List<Node> nodes) {
        return nodes.stream()
            .max(Comparator.comparing(this::calculatePerformanceScore));
    }
    
    /**
     * 成本优化策略
     */
    private Optional<Node> allocateByCost(List<Node> nodes) {
        return nodes.stream()
            .min(Comparator.comparing(this::calculateCostScore));
    }
    
    /**
     * 计算负载分数 (越低越好)
     */
    private double calculateLoadScore(Node node) {
        int maxCapacity = node.getSpec().getMaxSceneInstanceCount();
        if (maxCapacity == 0) return Double.MAX_VALUE;
        
        double cpuLoad = (double) node.getSceneRunningCount() / maxCapacity;
        double memoryLoad = calculateMemoryLoad(node);
        double networkLoad = calculateNetworkLoad(node);
        
        // 加权平均
        return cpuLoad * 0.5 + memoryLoad * 0.3 + networkLoad * 0.2;
    }
    
    /**
     * 计算性能分数 (越高越好)
     */
    private double calculatePerformanceScore(Node node) {
        double capacityScore = node.getSpec().getMaxSceneInstanceCount() / 10.0;
        double availabilityScore = node.getRemainingCapacity() / (double) node.getSpec().getMaxSceneInstanceCount();
        double healthScore = node.isHealthy() ? 1.0 : 0.0;
        
        return capacityScore * 0.4 + availabilityScore * 0.4 + healthScore * 0.2;
    }
    
    /**
     * 计算成本分数 (越低越好)
     */
    private double calculateCostScore(Node node) {
        // 基于节点规格和使用率计算成本
        double baseCost = node.getSpec().getMaxSceneInstanceCount() * 0.1;
        double utilizationMultiplier = 1.0 + (node.getSceneRunningCount() * 0.1);
        
        return baseCost * utilizationMultiplier;
    }
    
    /**
     * 检查场景亲和性
     */
    private boolean hasSceneAffinity(Node node, Scene scene) {
        // 检查节点是否已经运行相同类型的场景
        // 这里需要查询场景实例信息
        return false; // 简化实现
    }
    
    /**
     * 计算内存负载
     */
    private double calculateMemoryLoad(Node node) {
        // 从监控系统获取实际内存使用率
        return 0.5; // 简化实现
    }
    
    /**
     * 计算网络负载
     */
    private double calculateNetworkLoad(Node node) {
        // 从监控系统获取网络使用率
        return 0.3; // 简化实现
    }
    
    /**
     * 预测资源需求
     */
    public ResourcePrediction predictResourceRequirement(Scene scene, int expectedUsers) {
        double cpuRequirement = calculateCpuRequirement(scene, expectedUsers);
        double memoryRequirement = calculateMemoryRequirement(scene, expectedUsers);
        double networkRequirement = calculateNetworkRequirement(scene, expectedUsers);
        
        return ResourcePrediction.builder()
            .cpuCores(cpuRequirement)
            .memoryGB(memoryRequirement)
            .networkMbps(networkRequirement)
            .estimatedNodes(calculateRequiredNodes(cpuRequirement, memoryRequirement))
            .build();
    }
    
    private double calculateCpuRequirement(Scene scene, int users) {
        double baseCpu = scene.isOfficial() ? 2.0 : 1.0;
        return baseCpu + (users * 0.1);
    }
    
    private double calculateMemoryRequirement(Scene scene, int users) {
        double baseMemory = scene.isOfficial() ? 4.0 : 2.0;
        return baseMemory + (users * 0.2);
    }
    
    private double calculateNetworkRequirement(Scene scene, int users) {
        return users * 5.0; // 每用户5Mbps
    }
    
    private int calculateRequiredNodes(double cpuRequirement, double memoryRequirement) {
        // 基于平均节点容量计算所需节点数
        double avgNodeCapacity = 8.0; // 平均每节点8核
        return (int) Math.ceil(cpuRequirement / avgNodeCapacity);
    }
    
    /**
     * 分配策略枚举
     */
    public enum AllocationStrategy {
        LEAST_LOADED,        // 最少负载
        ROUND_ROBIN,         // 轮询
        AFFINITY_BASED,      // 亲和性
        PERFORMANCE_OPTIMIZED, // 性能优化
        COST_OPTIMIZED       // 成本优化
    }
}

/**
 * 资源预测结果
 */
@lombok.Builder
@lombok.Data
class ResourcePrediction {
    private double cpuCores;
    private double memoryGB;
    private double networkMbps;
    private int estimatedNodes;
}
