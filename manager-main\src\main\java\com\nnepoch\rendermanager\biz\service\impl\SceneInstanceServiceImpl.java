package com.nnepoch.rendermanager.biz.service.impl;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nnepoch.rendermanager.api.dto.request.SceneInstanceQueryDTO;
import com.nnepoch.rendermanager.api.dto.request.SceneStartOptionDTO;
import com.nnepoch.rendermanager.api.dto.request.SceneStopOptionDTO;
import com.nnepoch.rendermanager.api.dto.response.NodeResDTO;
import com.nnepoch.rendermanager.api.dto.response.SceneInstanceResDTO;
import com.nnepoch.rendermanager.api.dto.response.base.ActionResDTO;
import com.nnepoch.rendermanager.api.dto.response.base.PageResDTO;
import com.nnepoch.rendermanager.api.enums.SceneExperienceStatusEnum;
import com.nnepoch.rendermanager.biz.dto.mq.SceneInfoMQDTO;
import com.nnepoch.rendermanager.biz.dto.mq.SceneListenerMQDTO;
import com.nnepoch.rendermanager.biz.dto.mq.SceneProcessMQDTO;
import com.nnepoch.rendermanager.biz.entity.NodeEntity;
import com.nnepoch.rendermanager.biz.entity.SceneEntity;
import com.nnepoch.rendermanager.biz.entity.SceneExperienceEntity;
import com.nnepoch.rendermanager.biz.entity.SceneInstanceEntity;
import com.nnepoch.rendermanager.biz.event.SceneControlEvent;
import com.nnepoch.rendermanager.biz.mapper.NodeMapper;
import com.nnepoch.rendermanager.biz.mapper.SceneInstanceMapper;
import com.nnepoch.rendermanager.biz.mapper.SceneMapper;
import com.nnepoch.rendermanager.biz.mq.RabbitMQSender;
import com.nnepoch.rendermanager.biz.service.SceneExperienceService;
import com.nnepoch.rendermanager.biz.service.SceneInstanceService;
import com.nnepoch.rendermanager.config.RabbitConfig;
import com.nnepoch.rendermanager.exception.BizException;
import com.nnepoch.rendermanager.utils.SqlOrderUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

import javax.annotation.Nullable;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static com.nnepoch.rendermanager.biz.mq.NodeListener.TRIGGER;

/**
 * <AUTHOR> Che 2024/8/8
 */
@Service
@RequiredArgsConstructor
public class SceneInstanceServiceImpl extends ServiceImpl<SceneInstanceMapper, SceneInstanceEntity> implements SceneInstanceService {
    private final SceneInstanceMapper sceneInstanceMapper;
    private final RabbitMQSender rabbitMQSender;
    private final SceneMapper sceneMapper;
    private final NodeMapper nodeMapper;
    private final SceneExperienceService sceneExperienceService;
    private final ApplicationEventPublisher eventPublisher;

    @Override
    public PageResDTO<SceneInstanceResDTO> getList(SceneInstanceQueryDTO queryDTO) {
        Page<SceneInstanceEntity> pageObject = new Page<>(queryDTO.getPage(), queryDTO.getSize());

        QueryWrapper<SceneInstanceEntity> wrapper = new QueryWrapper<>();
        wrapper.eq(queryDTO.getSceneId() != null, "si.scene_id", queryDTO.getSceneId());
        wrapper.eq(queryDTO.getNodeId() != null, "si.node_id", queryDTO.getNodeId());

        if (CharSequenceUtil.isNotBlank(queryDTO.getSort())) {
            pageObject.addOrder(SqlOrderUtil.parseOrderItem(queryDTO.getSort()));
        } else {
            pageObject.addOrder(SqlOrderUtil.parseOrderItem("si.id,desc"));
        }

        IPage<SceneInstanceResDTO> page = sceneInstanceMapper.selectSceneInstanceList(pageObject, wrapper);

        return new PageResDTO<>(page.getRecords(), new PageResDTO.Pagination(page.getCurrent(), page.getSize(), page.getTotal()));
    }

    @Override
    public ActionResDTO<Void> freeInstance(Long id) {
        SceneInstanceEntity instance = sceneInstanceMapper.selectById(id);
        if (instance == null) {
            return ActionResDTO.fail();
        }
        SceneEntity scene = sceneMapper.selectById(instance.getSceneId());

        SceneStopOptionDTO options = new SceneStopOptionDTO();
        options.setProcessId(instance.getProcessId());
        options.setSessionId(instance.getSessionId());

        NodeEntity node = nodeMapper.selectById(instance.getNodeId());

        rabbitMQSender.send(
            RabbitConfig.EXCHANGE_NAME,
            RabbitConfig.getCloudRoutingKey(node.getSn()),
            new SceneListenerMQDTO(
                SceneListenerMQDTO.SceneListenerRpcActionEnum.STOP_SCENE,
                new SceneListenerMQDTO.SceneListenerRpcPayload<>(scene.getName(), options)
            ).toString(),
            0
        );

        return ActionResDTO.success();
    }

    @Override
    public ActionResDTO<Void> restartInstance(String sessionId, SceneStartOptionDTO options) {
        SceneInstanceEntity instance = getBySessionId(sessionId);
        if (instance == null) {
            throw new BizException("scene instance not found");
        }

        NodeEntity node = nodeMapper.selectById(instance.getNodeId());
        SceneEntity scene = sceneMapper.selectById(instance.getSceneId());

        options.setSessionId(sessionId);
        options.setOwnedBy(instance.getOwnedBy());
        options.setSceneExperienceId(instance.getSceneExperienceId());
        options.setMode(SceneStartOptionDTO.ModeEnum.PRIVATE);

        rabbitMQSender.send(
            RabbitConfig.EXCHANGE_NAME,
            RabbitConfig.getCloudRoutingKey(node.getSn()),
            new SceneListenerMQDTO(
                SceneListenerMQDTO.SceneListenerRpcActionEnum.RESTART_SCENE,
                new SceneListenerMQDTO.SceneListenerRpcPayload<>(scene.getName(), options)
            ).toString(),
            0
        );

        return ActionResDTO.success();
    }

    @Override
    public SceneInstanceEntity getBySessionId(String sessionId) {
        return sceneInstanceMapper.selectOne(Wrappers.<SceneInstanceEntity>lambdaQuery().eq(SceneInstanceEntity::getSessionId, sessionId));
    }

    @Override
    public void syncSceneInstance(SceneEntity sceneEntity, NodeResDTO node, SceneInfoMQDTO sceneInfo, List<SceneProcessMQDTO> sceneProcesses, @Nullable SceneStartOptionDTO startOptions) throws IOException {
        if (ObjUtil.isEmpty(sceneProcesses)) {
            sceneInstanceMapper.delete(
                Wrappers.lambdaQuery(SceneInstanceEntity.class)
                   .eq(SceneInstanceEntity::getSceneId, sceneEntity.getId())
                   .eq(SceneInstanceEntity::getNodeId, node.getId())
            );

            return;
        }

        List<SceneInstanceEntity> sceneInstances = sceneInstanceMapper.selectList(
            Wrappers.lambdaQuery(SceneInstanceEntity.class)
                .eq(SceneInstanceEntity::getSceneId, sceneEntity.getId())
                .eq(SceneInstanceEntity::getNodeId, node.getId())
                .isNull(SceneInstanceEntity::getSessionId)
        );

        Set<Integer> processIds = sceneProcesses.stream().map(SceneProcessMQDTO::getPid).collect(Collectors.toSet());
        for (SceneInstanceEntity sceneInstance : sceneInstances) {
            if (!processIds.contains(sceneInstance.getProcessId())) {
                sceneInstanceMapper.deleteById(sceneInstance.getId());
            }
        }

        for (SceneProcessMQDTO sceneProcess : sceneProcesses) {
            SceneInstanceEntity instanceEntity = sceneInstanceMapper.selectOne(
                Wrappers.lambdaQuery(SceneInstanceEntity.class)
                    .eq(SceneInstanceEntity::getNodeId, node.getId())
                    .eq(SceneInstanceEntity::getProcessId, sceneProcess.getPid())
            );

            SceneInstanceEntity instance = new SceneInstanceEntity();
            instance.setSceneId(sceneEntity.getId());
            instance.setNodeId(node.getId());
            instance.setProcessId(sceneProcess.getPid());
            instance.setDisplayName(sceneInfo.getDisplayName());
            instance.setProgramName(sceneInfo.getProgramName());
            instance.setExeName(sceneInfo.getExeName());
            instance.setVersion(sceneInfo.getVersion());
            instance.setSessionId(startOptions != null ? startOptions.getSessionId() : null);
            instance.setResolution(startOptions != null ? startOptions.getResolution() : sceneInfo.getResolution());
            instance.setFps(startOptions != null ? startOptions.getFps() : sceneInfo.getFps());
            instance.setBitrate(startOptions != null ? startOptions.getBitrate() : sceneInfo.getBitrate());
            instance.setSceneExperienceId(startOptions != null ? startOptions.getSceneExperienceId() : null);
            instance.setOwnedBy(startOptions != null ? startOptions.getOwnedBy() : null);
            instance.setAppDir(sceneInfo.getAppDir());
            instance.setExe(sceneProcess.getExe());
            instance.setStartTime(sceneProcess.getStartTime());
            instance.setRunTime(sceneProcess.getRunTime());
            instance.setMemory(sceneProcess.getMemory());
            instance.setCpuUsage(sceneProcess.getCpuUsage());

            if (instanceEntity == null) {
                sceneInstanceMapper.insert(instance);
            } else {
                instance.setId(instanceEntity.getId());
                sceneInstanceMapper.updateById(instance);

                if (instanceEntity.getSessionId() != null) {
                    Map<String, Object> data = new HashMap<>();
                    data.put(TRIGGER, "scene.metric");
                    data.put("success", true);
                    data.put("data", Map.of(
                        "cpuUsage", instance.getCpuUsage(),
                        "memory", instance.getMemory(),
                        "runTime", instance.getRunTime()
                    ));

                    eventPublisher.publishEvent(new SceneControlEvent("callback", instanceEntity.getSessionId(), data));
                }
            }

            // 设置场景最近使用时间
            sceneEntity.setViewedAt(sceneProcess.getStartTime());
            sceneMapper.updateById(sceneEntity);

            if (startOptions != null && startOptions.getSceneExperienceId() != null) {
                SceneExperienceEntity sceneExperience = new SceneExperienceEntity();
                sceneExperience.setId(startOptions.getSceneExperienceId());
                sceneExperience.setViewedAt(sceneProcess.getStartTime());
                sceneExperience.setStatus(SceneExperienceStatusEnum.VISITING);
                sceneExperienceService.updateById(sceneExperience);
            }
        }

    }

    @Override
    public void removeSceneInstance(NodeResDTO node, SceneStopOptionDTO options) {
        SceneInstanceEntity instance = sceneInstanceMapper.selectOne(
            Wrappers.lambdaQuery(SceneInstanceEntity.class)
                .eq(SceneInstanceEntity::getNodeId, node.getId())
                .eq(SceneInstanceEntity::getProcessId, options.getProcessId())
        );
        if (instance == null) return;

        sceneInstanceMapper.delete(
            Wrappers.lambdaQuery(SceneInstanceEntity.class)
                .eq(SceneInstanceEntity::getNodeId, node.getId())
                .eq(SceneInstanceEntity::getProcessId, options.getProcessId())
        );

        if (instance.getSceneExperienceId() != null) {
            SceneExperienceEntity sceneExperience = new SceneExperienceEntity();
            sceneExperience.setId(instance.getSceneExperienceId());
            sceneExperience.setStatus(SceneExperienceStatusEnum.IDLE);
            sceneExperienceService.updateById(sceneExperience);
        }
    }
}
