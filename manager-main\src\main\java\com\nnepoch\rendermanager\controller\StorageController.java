package com.nnepoch.rendermanager.controller;

import com.nnepoch.rendermanager.api.dto.response.StorageUploadResDTO;
import com.nnepoch.rendermanager.biz.service.StorageService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR> 2024/6/21
 */
@RestController
@RequestMapping(path = "/api/v1/storages", produces = "application/json")
@RequiredArgsConstructor
@Tag(name = "Storage", description = "文件存储相关接口")
@SecurityRequirement(name = "bearerAuth")
@Validated
public class StorageController {
    private final StorageService storageService;

    @PostMapping(path = "/files", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @Operation(summary = "上传文件", description = "上传文件到Minio服务器")
    @ApiResponse(responseCode = "200", description = "上传成功")
    public StorageUploadResDTO uploadFile(@RequestParam("file") @NotNull MultipartFile file) throws Exception {
        return storageService.uploadFile(file);
    }
}
