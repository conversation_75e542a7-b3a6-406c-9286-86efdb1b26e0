package com.nnepoch.rendermanager.api.dto.response;

import com.nnepoch.rendermanager.api.dto.response.base.BaseResDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> 2024/6/17
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "场景节点调度信息")
public class SceneScheduleResDTO extends BaseResDTO {
    private String sceneVersion;
    private String nodeStatus;
    private String nodeName;
    private String nodeHashId;
    private String nodeOrganization;
}
