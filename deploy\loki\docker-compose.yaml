version: "3"

services:
  loki:
    image: grafana/loki:2.9.2
    command: -config.file=/etc/loki/local-config.yaml
    restart: unless-stopped
    networks:
      - loki
    ports:
      - 13100:3100

  promtail:
    image: grafana/promtail:2.9.2
    volumes:
      - /var/lib/docker:/var/lib/docker
      - ./promtail/config.yml:/etc/promtail/config.yml
    command: -config.file=/etc/promtail/config.yml
    restart: unless-stopped
    networks:
      - loki

  grafana:
    environment:
      - GF_PATHS_PROVISIONING=/etc/grafana/provisioning
      - GF_AUTH_ANONYMOUS_ENABLED=true
      - GF_AUTH_ANONYMOUS_ORG_ROLE=Admin
      - GF_SECURITY_ALLOW_EMBEDDING=true
      - GF_SECURITY_COOKIE_SECURE=true
      - GF_SERVER_ROOT_URL=https://rmp.nnepoch.com/grafana/
      - GF_SERVER_DOMAIN=rmp.nnepoch.com
    entrypoint:
      - sh
      - -euc
      - |
        mkdir -p /etc/grafana/provisioning/datasources
        cat <<EOF > /etc/grafana/provisioning/datasources/ds.yaml
        apiVersion: 1
        datasources:
        - name: Loki
          type: loki
          access: proxy 
          orgId: 1
          url: http://loki:3100
          basicAuth: false
          isDefault: true
          version: 1
          editable: false
        EOF
        /run.sh
    image: grafana/grafana:latest
    restart: unless-stopped
    ports:
      - 13000:3000
    networks:
      - loki

networks:
  loki:
    name: dasv-network
    external: true