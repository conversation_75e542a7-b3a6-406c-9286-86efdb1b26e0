package com.nnepoch.rendermanager.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> 2024/6/20
 */
@AllArgsConstructor
@Getter
public enum SceneIndustryEnum {
    PARKS(1, "园区"),
    CULTURAL_TOURISM(2, "文旅"),
    DIGITAL_MARKETING(3, "数字营销"),
    COMMUNITIES(4, "社区"),
    FACTORIES(5, "工厂"),
    JUSTICE(6, "司法"),
    EXHIBITION_HALL(7, "展馆");

    public final Integer code;
    public final String desc;

    public static SceneIndustryEnum fromCode(Integer code) {
        for (SceneIndustryEnum industry : SceneIndustryEnum.values()) {
            if (industry.code.equals(code)) {
                return industry;
            }
        }
        return null;
    }
}
