package com.nnepoch.rendermanager.api.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.nnepoch.rendermanager.api.dto.response.base.BaseResDTO;
import com.nnepoch.rendermanager.api.enums.NodeStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> 2024/6/17
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "节点信息")
public class NodeResDTO extends BaseResDTO {
    @Schema(description = "节点ID")
    private String hashId;

    @Schema(description = "节点SN")
    private String sn;

    @Schema(description = "节点os")
    private String os;

    @Schema(description = "节点os版本")
    private String osVersion;

    @Schema(description = "节点名称")
    private String hostname;

    @Schema(description = "节点状态")
    private NodeStatusEnum status;

    @Schema(description = "是否收集节点日志")
    private Boolean enableLogCollection;

    @Schema(description = "节点场景数量")
    private Integer sceneCount;

    @Schema(description = "节点已运行场景数量")
    private Integer sceneRunningCount;

    @Schema(description = "节点最大可运行场景实例数量")
    private Integer maxSceneInstanceCount;

    @Schema(description = "节点已占用坐席数量")
    private Integer sceneSeatsTaken;

    @Schema(description = "节点客户单位")
    private String organization;

    @Schema(description = "节点客户手机号")
    private String mobile;

    @Schema(description = "节点客户邮箱")
    private String email;

    @Schema(description = "节点归属管理员")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long ownedBy;

    @Schema(description = "节点所属用户信息")
    private SimpleUserResDTO owner;
}
