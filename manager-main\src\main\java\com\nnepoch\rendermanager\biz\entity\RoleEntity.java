package com.nnepoch.rendermanager.biz.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nnepoch.rendermanager.biz.entity.enums.UserPermissionEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Arrays;
import java.util.Date;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR> <PERSON> 2024/8/5
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value = "role")
@Data
public class RoleEntity extends BaseEntity {
    private String name;

    private String permissions;

    private Date deletedAt;

    @TableField(exist = false)
    private Set<UserPermissionEnum> permissionSet;

    public void setPermissionSet(Set<UserPermissionEnum> permissionSet) {
        this.permissionSet = permissionSet;
        this.permissions = permissionSet.stream().map(UserPermissionEnum::getAuthority).reduce((a, b) -> a + "," + b).orElse("");
    }

    public static Set<UserPermissionEnum> getPermissionSet(String permissions) {
        return Arrays.stream(permissions.split(",")).map(UserPermissionEnum::fromPermission).collect(Collectors.toSet());
    }
}
