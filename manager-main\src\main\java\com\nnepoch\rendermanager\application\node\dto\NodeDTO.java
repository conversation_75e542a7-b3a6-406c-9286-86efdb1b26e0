package com.nnepoch.rendermanager.application.node.dto;

import com.nnepoch.rendermanager.api.enums.NodeStatusEnum;
import com.nnepoch.rendermanager.domain.node.aggregate.Node;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 节点数据传输对象
 * <AUTHOR> Architect
 */
@Data
public class NodeDTO {
    private Long id;
    private String sn;
    private String hostname;
    private String hashId;
    private NodeStatusEnum status;
    private String os;
    private String osVersion;
    private Integer maxSceneInstanceCount;
    private Integer sceneRunningCount;
    private Integer sceneSeatsTaken;
    private Boolean enableLogCollection;
    private String organization;
    private String mobile;
    private String email;
    private Long ownedBy;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    
    /**
     * 从领域对象转换为DTO
     */
    public static NodeDTO fromDomain(Node node) {
        NodeDTO dto = new NodeDTO();
        dto.setId(node.getId().getValue());
        dto.setSn(node.getAddress().getSn());
        dto.setHostname(node.getAddress().getHostname());
        dto.setHashId(node.getAddress().getHashId());
        dto.setStatus(node.getStatus());
        dto.setOs(node.getSpec().getOs());
        dto.setOsVersion(node.getSpec().getOsVersion());
        dto.setMaxSceneInstanceCount(node.getSpec().getMaxSceneInstanceCount());
        dto.setSceneRunningCount(node.getSceneRunningCount());
        dto.setSceneSeatsTaken(node.getSceneSeatsTaken());
        dto.setEnableLogCollection(node.isEnableLogCollection());
        dto.setOrganization(node.getSpec().getOrganization());
        dto.setMobile(node.getSpec().getMobile());
        dto.setEmail(node.getSpec().getEmail());
        dto.setOwnedBy(node.getOwnedBy().getValue());
        dto.setCreatedAt(node.getCreatedAt());
        dto.setUpdatedAt(node.getUpdatedAt());
        return dto;
    }
}
