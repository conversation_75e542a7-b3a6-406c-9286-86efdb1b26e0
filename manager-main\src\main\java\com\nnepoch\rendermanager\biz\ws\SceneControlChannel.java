package com.nnepoch.rendermanager.biz.ws;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.nnepoch.rendermanager.api.dto.request.SceneStartOptionDTO;
import com.nnepoch.rendermanager.api.dto.request.SceneStopOptionDTO;
import com.nnepoch.rendermanager.api.dto.response.SceneResDTO;
import com.nnepoch.rendermanager.api.dto.response.base.ActionResDTO;
import com.nnepoch.rendermanager.biz.dto.ws.WebSocketMessageDTO;
import com.nnepoch.rendermanager.biz.entity.NodeEntity;
import com.nnepoch.rendermanager.biz.entity.SceneExperienceEntity;
import com.nnepoch.rendermanager.biz.entity.SceneInstanceEntity;
import com.nnepoch.rendermanager.biz.entity.UserEntity;
import com.nnepoch.rendermanager.biz.event.SceneControlEvent;
import com.nnepoch.rendermanager.biz.service.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.context.event.EventListener;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.PongMessage;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.handler.TextWebSocketHandler;

import java.io.IOException;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * <AUTHOR> Che 2024/7/2
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class SceneControlChannel extends TextWebSocketHandler {
    protected static final ConcurrentMap<String, WebSocketSession> connections = new ConcurrentHashMap<>();
    private static final ObjectMapper objectMapper = new ObjectMapper().configure(
        DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false
    );
    private final JwtService jwtService;
    private final UserService userService;
    private final SceneService sceneService;
    private final SceneExperienceService sceneExperienceService;
    private final SceneInstanceService sceneInstanceService;

    @Override
    public void afterConnectionEstablished(@NotNull WebSocketSession session) throws Exception {
        connections.put(session.getId(), session);
        log.info("New WebSocket connection opened, session id: {}, total count: {}", session.getId(), connections.size());
        Map<String, Object> data = new HashMap<>();
        data.put("sid", session.getId());
        sendSuccessResponse(session, "conn.info", data);
    }

    @Override
    protected void handleTextMessage(WebSocketSession session, @NotNull TextMessage message) throws Exception {
        log.info("Received message from client: {}, session id: {}", message, session.getId());
        UserEntity userContext = (UserEntity) session.getAttributes().get("user");
        SceneExperienceEntity sceneExperienceContext = (SceneExperienceEntity) session.getAttributes().get("sceneExperience");
        log.info("websocket handshake user: {} sceneExperience: {}", userContext, sceneExperienceContext);

        try {
            WebSocketMessageDTO msg = objectMapper.readValue(message.asBytes(), WebSocketMessageDTO.class);
            if (msg == null || msg.getTrigger() == null || msg.getData() == null) {
                sendErrorResponse(session, HttpStatus.BAD_REQUEST.name(), "Invalid message format");
                return;
            }

            if (!msg.getTrigger().equals("auth") && userContext == null && sceneExperienceContext == null) {
                sendErrorResponse(session, HttpStatus.FORBIDDEN.name(), HttpStatus.FORBIDDEN.getReasonPhrase());
                return;
            }

            handleSceneControlMessage(session, msg);
        } catch (Exception e) {
            log.error("Error handling message from client: {}, session id: {}", message, session.getId(), e);
            var errorMessage = e.getClass().getSimpleName() + ": " + e.getMessage();
            sendErrorResponse(session, HttpStatus.INTERNAL_SERVER_ERROR.name(), errorMessage);
        }
    }

    @Override
    public void afterConnectionClosed(WebSocketSession session, @NotNull CloseStatus status) throws Exception {
        log.info("WebSocket connection closed, session id: {}, total count: {}", session.getId(), connections.size());
        connections.remove(session.getId());
        session.close(status);

        Long sceneId = (Long) session.getAttributes().get("sceneId");
        if (sceneId != null) {
            sceneService.stopScene(sceneId, new SceneStopOptionDTO(session.getId()));
        }
    }

    @Override
    protected void handlePongMessage(@NotNull WebSocketSession session, @NotNull PongMessage message) throws Exception {
        Map<String, Object> data = new HashMap<>();
        data.put("pong", true);
        sendSuccessResponse(session, "scene.pong", data);
    }

    @EventListener
    public void handleSceneControlEvent(SceneControlEvent event) throws IOException {
        if (event.eventType().equals("callback")) {
            sendControlCallback(event.sessionId(), event.data());
        }
    }

    public void sendControlCallback(String sessionId, Map<String, Object> data) throws IOException {
        String callbackData = objectMapper.writeValueAsString(data);

        connections.values().parallelStream()
            .filter(session -> session.getId().equals(sessionId))
            .forEach(session -> {
                try {
                    session.sendMessage(new TextMessage(callbackData));
                } catch (IOException e) {
                    log.error("Failed to send control callback to session {}", sessionId, e);
                }
            });
    }

    private void handleSceneControlMessage(WebSocketSession session, WebSocketMessageDTO message) throws IOException {
        switch (message.getTrigger()) {
            case "auth":
                handleAuthTrigger(session, message);
                break;
            case "scene.start":
                handleSceneStartTrigger(session, message);
                break;
            case "scene.stop":
                Long sceneIdContext = (Long) session.getAttributes().get("sceneId");
                Map<String, Object> replyData = new HashMap<>();
                sceneService.stopScene(sceneIdContext, new SceneStopOptionDTO(session.getId()));
                sendSuccessResponse(session, message.getTrigger(), replyData, Map.of("success", false, "message", "scene is stopping..."));
                break;
            case "scene.restart":
                handleSceneRestartTrigger(session, message);
                break;
            case "scene.ping":
                handleScenePingHandle(session);
                break;
            default:
                sendErrorResponse(session, HttpStatus.BAD_REQUEST.name(), "Invalid trigger");
                break;
        }
    }

    private void handleScenePingHandle(WebSocketSession session) throws IOException {
        SceneExperienceEntity sceneExperienceContext = (SceneExperienceEntity) session.getAttributes().get("sceneExperience");
        SceneInstanceEntity instanceContext = (SceneInstanceEntity) session.getAttributes().get("sceneInstance");
        Map<String, Object> replyData = new HashMap<>();

        if (instanceContext == null) {
            SceneInstanceEntity instance = sceneInstanceService.getBySessionId(session.getId());
            if (instance == null) {
                sendSuccessResponse(session, "scene.pong", replyData, Map.of("message", "scene is processing..."));
                return;
            }

            session.getAttributes().put("sceneInstance", instance);
        }

        if (sceneExperienceContext != null && instanceContext != null) {
            SceneExperienceEntity sceneExperience = sceneExperienceService.getOne(Wrappers.<SceneExperienceEntity>lambdaQuery().eq(SceneExperienceEntity::getId, sceneExperienceContext.getId()));
            if (Boolean.TRUE.equals(sceneExperience.getIsBanned())) {
                sendErrorResponse(session, "EXPERIENCE_BANNED", "Scene experience is banned");
                session.close(CloseStatus.POLICY_VIOLATION);
                return;
            }
            if ((System.currentTimeMillis() - instanceContext.getStartTime().getTime()) / 60000 + 1 > sceneExperience.getDuration()) {
                sendErrorResponse(session, "DURATION_OVER_LIMIT", "Scene experience duration is over limit");
                session.close(CloseStatus.POLICY_VIOLATION);
                return;
            }
        }

        sendSuccessResponse(session, "scene.pong", replyData);
    }

    private void handleSceneStartTrigger(WebSocketSession session, WebSocketMessageDTO message) throws IOException {
        Map<String, Object> replyData = new HashMap<>();
        UserEntity userContext = (UserEntity) session.getAttributes().get("user");
        SceneExperienceEntity sceneExperienceContext = (SceneExperienceEntity) session.getAttributes().get("sceneExperience");
        Long sceneId = Long.parseLong((String) message.getData().get("id"));
        SceneStartOptionDTO options = objectMapper.convertValue(message.getData().get("options"), SceneStartOptionDTO.class);

        SceneResDTO scene = sceneService.getSceneInfo(sceneId);
        if (Boolean.TRUE.equals(scene.getIsBanned())) {
            sendErrorResponse(session, "SCENE_BANNED", "Scene banned");
            session.close(CloseStatus.POLICY_VIOLATION);
            return;
        }

        if (sceneExperienceContext != null) {
            options.setSceneExperienceId(sceneExperienceContext.getId());
        }

        if (userContext != null) {
            options.setOwnedBy(userContext.getId());
        }

        options.setSessionId(session.getId());
        options.setMode(SceneStartOptionDTO.ModeEnum.PRIVATE);
        ActionResDTO<NodeEntity> actionRes = sceneService.startScene(sceneId, options);
        session.getAttributes().put("node", actionRes.getData());
        session.getAttributes().put("sceneId", sceneId);

        sendSuccessResponse(session, message.getTrigger(), replyData, Map.of("success", false, "message", "scene is starting..."));
    }

    private void handleSceneRestartTrigger(WebSocketSession session, WebSocketMessageDTO message) throws IOException {
        SceneStartOptionDTO options = objectMapper.convertValue(message.getData().get("options"), SceneStartOptionDTO.class);
        options.setIsRestart(true);

        sceneInstanceService.restartInstance(session.getId(), options);
        sendSuccessResponse(session, message.getTrigger(), new HashMap<>());
    }

    private void handleAuthTrigger(WebSocketSession session, WebSocketMessageDTO message) throws IOException {
        Map<String, Object> replyData = new HashMap<>();
        String token = (String) message.getData().get("token");
        String accessKey = (String) message.getData().get("ak"); // 外部预览
        final String INVALID_TOKEN = "Invalid token";
        if ((token == null && accessKey == null) || (token != null && accessKey != null)) {
            sendErrorResponse(session, HttpStatus.UNAUTHORIZED.name(), "Invalid authentication request");
            return;
        }

        if (token != null) {
            // 内部预览
            if (jwtService.isTokenInValid(token)) {
                sendErrorResponse(session, HttpStatus.UNAUTHORIZED.name(), INVALID_TOKEN);
                return;
            }

            String mobile = (String) jwtService.parseJWT(token).getClaim("mobile");
            if (mobile == null) {
                sendErrorResponse(session, HttpStatus.UNAUTHORIZED.name(), INVALID_TOKEN);
                return;
            }

            UserEntity user = userService.getOne(Wrappers.<UserEntity>lambdaQuery().eq(UserEntity::getMobile, mobile));
            if (!jwtService.validateJWT(token, user)) {
                sendErrorResponse(session, HttpStatus.UNAUTHORIZED.name(), INVALID_TOKEN);
                return;
            }

            session.getAttributes().put("user", user);

            replyData.put("user", userService.getSimpleUserById(user.getId()));
            sendSuccessResponse(session, message.getTrigger(), replyData);
        }

        if (accessKey != null) {
            // 外部预览
            SceneExperienceEntity sceneExperience = sceneExperienceService.getByHashId(accessKey);
            if (Boolean.TRUE.equals(sceneExperience.getIsBanned())) {
                sendErrorResponse(session, "EXPERIENCE_BANNED", "Scene experience is banned");
                session.close(CloseStatus.POLICY_VIOLATION);
                return;
            }
            if (sceneExperience.getExpiredAt().before(new Date())) {
                sendErrorResponse(session, "EXPERIENCE_EXPIRED", "Scene experience is expired");
                session.close(CloseStatus.POLICY_VIOLATION);
                return;
            }
            session.getAttributes().put("sceneExperience", sceneExperience);

            SceneResDTO scene = sceneService.getSceneInfo(sceneExperience.getSceneId());
            replyData.put("scene", scene);
            replyData.put("experience", sceneExperience);
            sendSuccessResponse(session, message.getTrigger(), replyData);
        }
    }

    private void sendErrorResponse(WebSocketSession session, String code, String error) {
        try {
            Map<String, Object> replyMap = new HashMap<>();
            replyMap.put("code", code);
            replyMap.put("error", error);
            session.sendMessage(new TextMessage(objectMapper.writeValueAsString(replyMap)));
        } catch (Exception e) {
            log.error("Failed to send error response to client", e);
        }
    }

    @SafeVarargs
    private void sendSuccessResponse(WebSocketSession session, String trigger, Map<String, Object> data, Map<String, Object>... additionalProperties) {
        try {
            Map<String, Object> replyMap = new HashMap<>();
            replyMap.put("trigger", trigger);
            replyMap.put("data", data);

            if (additionalProperties != null) {
                for (Map<String, Object> additionalProperty : additionalProperties) {
                    if (additionalProperty != null) {
                        replyMap.putAll(additionalProperty);
                    }
                }
            }

            session.sendMessage(new TextMessage(objectMapper.writeValueAsString(replyMap)));
        } catch (Exception e) {
            log.error("Failed to send success response to client", e);
        }
    }
}
