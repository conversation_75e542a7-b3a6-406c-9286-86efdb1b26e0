package com.nnepoch.rendermanager.infrastructure.event.store;

import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 存储的事件
 * <AUTHOR> Architect
 */
@Data
@Builder
public class StoredEvent {
    private String eventId;
    private String aggregateId;
    private String aggregateType;
    private String eventType;
    private String eventData;
    private String metadata;
    private Long version;
    private LocalDateTime occurredOn;
    private LocalDateTime storedOn;
    
    /**
     * 反序列化事件数据
     */
    @SuppressWarnings("unchecked")
    public <T> T getEventData(Class<T> eventClass) {
        // 使用Jackson或其他JSON库反序列化
        // 这里简化处理
        return null;
    }
    
    /**
     * 获取元数据
     */
    public EventMetadata getMetadata() {
        // 解析metadata字段
        return EventMetadata.fromJson(metadata);
    }
}

/**
 * 事件元数据
 */
@Data
@Builder
class EventMetadata {
    private String userId;
    private String correlationId;
    private String causationId;
    private String source;
    private String version;
    
    public static EventMetadata fromJson(String json) {
        // JSON反序列化逻辑
        return EventMetadata.builder().build();
    }
    
    public String toJson() {
        // JSON序列化逻辑
        return "{}";
    }
}
