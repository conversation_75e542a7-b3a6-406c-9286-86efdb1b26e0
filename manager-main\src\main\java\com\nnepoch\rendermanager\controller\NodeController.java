package com.nnepoch.rendermanager.controller;

/**
 * <AUTHOR> 2024/6/17
 */

import com.nnepoch.rendermanager.api.dto.request.NodeQueryDTO;
import com.nnepoch.rendermanager.api.dto.request.NodeReqDTO;
import com.nnepoch.rendermanager.api.dto.response.NodeResDTO;
import com.nnepoch.rendermanager.api.dto.response.base.PageResDTO;
import com.nnepoch.rendermanager.biz.dto.mq.NodeInfoMQDTO;
import com.nnepoch.rendermanager.biz.service.NodeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping(path = "/api/v1/nodes", produces = "application/json")
@RequiredArgsConstructor
@Tag(name = "Node", description = "节点管理相关接口")
@SecurityRequirement(name = "bearerAuth")
public class NodeController {
    private final NodeService nodeService;

    //    @OpenApi
    @GetMapping
    @Operation(summary = "获取节点列表", description = "获取节点列表")
    @ApiResponse(responseCode = "200", description = "节点列表")
    public PageResDTO<NodeResDTO> getNodeList(@ParameterObject NodeQueryDTO queryDTO) {
        return nodeService.getNodeList(queryDTO);
    }

    @GetMapping("{id}")
    @Operation(summary = "获取节点信息", description = "根据节点ID获取节点信息")
    @ApiResponse(responseCode = "200", description = "节点信息")
    public NodeResDTO getNodeInfo(@PathVariable("id") Long id) {
        return nodeService.getNodeInfo(id);
    }

    @GetMapping("best")
    @Operation(summary = "获取最佳节点", description = "获取最佳节点")
    @ApiResponse(responseCode = "200", description = "最佳节点信息")
    public NodeResDTO getBestNode() {
        return nodeService.getBestNode();
    }

    @PostMapping
    @Operation(summary = "新增节点", description = "新增节点")
    @ApiResponse(responseCode = "200", description = "新增节点成功")
    public NodeResDTO addNode(@Valid @RequestBody NodeInfoMQDTO reqDTO) {
        return nodeService.syncNode(reqDTO);
    }

    @PreAuthorize("hasAnyRole('ADMIN', 'OPERATOR')")
    @PutMapping("{id}")
    @Operation(summary = "更新节点", description = "更新节点")
    @ApiResponse(responseCode = "200", description = "更新节点成功")
    public NodeResDTO updateNode(@PathVariable("id") Long id, @Valid @RequestBody NodeReqDTO reqDTO) {
        return nodeService.updateNode(id, reqDTO);
    }
}
