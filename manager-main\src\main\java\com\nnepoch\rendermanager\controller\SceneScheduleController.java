package com.nnepoch.rendermanager.controller;

import com.nnepoch.rendermanager.api.dto.request.SceneScheduleQueryDTO;
import com.nnepoch.rendermanager.api.dto.response.SceneScheduleResDTO;
import com.nnepoch.rendermanager.api.dto.response.base.PageResDTO;
import com.nnepoch.rendermanager.biz.service.SceneScheduleService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> 2024/6/17
 */
@RestController
@RequestMapping(path = "/api/v1/scene-schedule", produces = "application/json")
@RequiredArgsConstructor
@Tag(name = "Scene Schedule", description = "场景调度(节点分发)相关接口")
@SecurityRequirement(name = "bearerAuth")
public class SceneScheduleController {
    private final SceneScheduleService sceneScheduleService;

    @GetMapping("")
    @Operation(summary = "获取场景调度列表", description = "根据条件获取场景调度列表")
    @ApiResponse(responseCode = "200", description = "场景调度列表")
    public PageResDTO<SceneScheduleResDTO> getSceneScheduleList(@ParameterObject SceneScheduleQueryDTO queryDTO) {
        return sceneScheduleService.getSceneScheduleList(queryDTO);
    }

    @GetMapping("/{id}")
    @Operation(summary = "获取场景调度详情", description = "根据id获取场景调度详情")
    @ApiResponse(responseCode = "200", description = "场景调度详情")
    public SceneScheduleResDTO getSceneScheduleInfo(@PathVariable Long id) {
        return sceneScheduleService.getSceneScheduleInfo(id);
    }
}
