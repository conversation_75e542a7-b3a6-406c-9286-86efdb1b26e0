package com.nnepoch.rendermanager.domain.scene.repository;

import com.nnepoch.rendermanager.domain.scene.aggregate.Scene;
import com.nnepoch.rendermanager.domain.shared.repository.Repository;
import com.nnepoch.rendermanager.domain.shared.valueobject.SceneId;

import java.util.List;
import java.util.Optional;

/**
 * 场景仓储接口
 * <AUTHOR> Architect
 */
public interface SceneRepository extends Repository<Scene, SceneId> {
    
    /**
     * 根据名称查找场景
     */
    Optional<Scene> findByName(String name);
    
    /**
     * 根据名称和版本查找场景
     */
    Optional<Scene> findByNameAndVersion(String name, String version);
    
    /**
     * 查找官方场景列表
     */
    List<Scene> findOfficialScenes();
    
    /**
     * 查找可用场景列表
     */
    List<Scene> findAvailableScenes();
    
    /**
     * 根据行业查找场景
     */
    List<Scene> findByIndustry(String industry);
    
    /**
     * 检查场景名称是否存在
     */
    boolean existsByName(String name);
    
    /**
     * 检查场景名称和版本是否存在
     */
    boolean existsByNameAndVersion(String name, String version);
}
