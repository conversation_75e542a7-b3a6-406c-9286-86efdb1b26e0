package com.nnepoch.rendermanager.config.properties;

import io.minio.MinioClient;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> <PERSON> 2024/6/21
 */
@Data
@Component
@ConfigurationProperties(prefix = "app.minio")
public class AppMinioProperties {
    private String url;
    private String endpoint;
    private String accessKey;
    private String secretKey;
    private String publicBucketName;

    @Bean
    public MinioClient minioClient() {
        return MinioClient.builder()
            .endpoint(endpoint)
            .credentials(accessKey, secretKey)
            .build();
    }
}
