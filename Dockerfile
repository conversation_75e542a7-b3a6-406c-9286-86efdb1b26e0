# FROM docker.das-security.cn/dasv/openjdk:11-jdk-openj9-alpine
FROM openjdk:21-jdk-slim

LABEL Maintainer="Aaron Che"

# 添加jar包到容器并重命名
COPY ./manager-main/target/render-manager-main-*.jar app.jar

# 解决时区问题
#RUN cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime \
#&& echo 'Asia/Shanghai' >/etc/timezone

# 将arthas镜像中的jar包拷贝到本镜像中
COPY --from=hengyunabc/arthas:latest /opt/arthas /opt/arthas

# 使用端口
EXPOSE 8080

ENV SPRING_PROFILES_ACTIVE="prod"

ENTRYPOINT ["/bin/sh","-c","java $JAVA_OPTS  -Djava.security.egd=file:/dev/./urandom -jar app.jar"]