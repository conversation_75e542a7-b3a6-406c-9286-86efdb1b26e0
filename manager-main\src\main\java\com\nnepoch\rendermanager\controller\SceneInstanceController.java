package com.nnepoch.rendermanager.controller;

import com.nnepoch.rendermanager.api.dto.request.SceneInstanceQueryDTO;
import com.nnepoch.rendermanager.api.dto.response.SceneInstanceResDTO;
import com.nnepoch.rendermanager.api.dto.response.base.ActionResDTO;
import com.nnepoch.rendermanager.api.dto.response.base.PageResDTO;
import com.nnepoch.rendermanager.biz.service.SceneInstanceService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.MimeTypeUtils;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR> 2024/8/8
 */
@RestController
@RequestMapping(path = "/api/v1/scene-instance", produces = MimeTypeUtils.APPLICATION_JSON_VALUE)
@RequiredArgsConstructor
@Tag(name = "Scene Instance", description = "场景实例相关接口")
@SecurityRequirement(name = "bearerAuth")
public class SceneInstanceController {
    private final SceneInstanceService sceneInstanceService;

    @GetMapping()
    @Operation(summary = "查询场景实例列表")
    @ApiResponse(responseCode = "200", description = "查询成功")
    public PageResDTO<SceneInstanceResDTO> getSceneInstanceList(@ParameterObject SceneInstanceQueryDTO queryDTO) {
        return sceneInstanceService.getList(queryDTO);
    }

    @PreAuthorize("hasAnyRole('ADMIN', 'OPERATOR')")
    @PostMapping("{id}/free")
    @Operation(summary = "释放场景实例")
    @ApiResponse(responseCode = "200", description = "释放成功")
    public ActionResDTO<Void> freeSceneInstance(@PathVariable("id") Long id) {
        return sceneInstanceService.freeInstance(id);
    }
}
