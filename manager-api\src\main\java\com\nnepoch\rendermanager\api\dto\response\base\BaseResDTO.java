package com.nnepoch.rendermanager.api.dto.response.base;


import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR> 2024/6/13
 */
@Data
@Schema(description = "基础响应DTO")
public class BaseResDTO {
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    @Schema(description = "创建时间")
    private Date createdAt;
    
    @Schema(description = "更新时间")
    private Date updatedAt;
    
    @Schema(description = "删除时间")
    private Date deletedAt;

    @Schema(description = "备注", example = "备注")
    private String remark;
}
