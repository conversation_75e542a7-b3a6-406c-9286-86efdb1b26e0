package com.nnepoch.rendermanager.infrastructure.anticorruption;

import com.nnepoch.rendermanager.biz.entity.NodeEntity;
import com.nnepoch.rendermanager.biz.entity.SceneEntity;
import com.nnepoch.rendermanager.biz.entity.UserEntity;
import com.nnepoch.rendermanager.domain.node.aggregate.Node;
import com.nnepoch.rendermanager.domain.node.valueobject.NodeAddress;
import com.nnepoch.rendermanager.domain.node.valueobject.NodeSpec;
import com.nnepoch.rendermanager.domain.scene.aggregate.Scene;
import com.nnepoch.rendermanager.domain.scene.valueobject.SceneConfig;
import com.nnepoch.rendermanager.domain.shared.valueobject.NodeId;
import com.nnepoch.rendermanager.domain.shared.valueobject.SceneId;
import com.nnepoch.rendermanager.domain.shared.valueobject.UserId;
import com.nnepoch.rendermanager.domain.user.aggregate.User;
import com.nnepoch.rendermanager.domain.user.valueobject.UserProfile;
import org.springframework.stereotype.Component;

import java.time.ZoneId;

/**
 * 遗留系统适配器 - 防腐层实现
 * 负责在新DDD模型和遗留数据模型之间进行转换
 * <AUTHOR> Architect
 */
@Component
public class LegacySystemAdapter {
    
    /**
     * 将遗留用户实体转换为用户聚合
     */
    public User toDomainUser(UserEntity entity) {
        if (entity == null) return null;
        
        try {
            UserProfile profile = new UserProfile(
                entity.getUsername(),
                entity.getEmail(),
                entity.getMobile()
            );
            
            UserId userId = UserId.of(entity.getId());
            
            // 使用工厂方法创建用户聚合
            User user = User.create(userId, profile, entity.getPassword(), entity.getRole());
            
            // 设置额外状态
            if (entity.getIsBanned()) {
                user.ban();
            }
            
            // 设置时间戳（需要通过反射或专门的方法）
            setUserTimestamps(user, entity);
            
            return user;
            
        } catch (Exception e) {
            throw new AdapterException("Failed to convert UserEntity to User domain object", e);
        }
    }
    
    /**
     * 将用户聚合转换为遗留实体
     */
    public UserEntity toLegacyUser(User user) {
        if (user == null) return null;
        
        UserEntity entity = new UserEntity();
        entity.setId(user.getId().getValue());
        entity.setUsername(user.getProfile().getUsername());
        entity.setEmail(user.getProfile().getEmail());
        entity.setMobile(user.getProfile().getMobile());
        entity.setPassword(user.getPasswordHash());
        entity.setRole(user.getRole());
        entity.setIsBanned(user.isBanned());
        
        // 转换时间戳
        if (user.getLoginAt() != null) {
            entity.setLoginAt(java.util.Date.from(
                user.getLoginAt().atZone(ZoneId.systemDefault()).toInstant()));
        }
        
        return entity;
    }
    
    /**
     * 将遗留节点实体转换为节点聚合
     */
    public Node toDomainNode(NodeEntity entity) {
        if (entity == null) return null;
        
        try {
            NodeAddress address = new NodeAddress(
                entity.getSn(),
                entity.getHostname(),
                entity.getHashId()
            );
            
            NodeSpec spec = new NodeSpec(
                entity.getOs(),
                entity.getOsVersion(),
                entity.getMaxSceneInstanceCount(),
                entity.getOrganization(),
                entity.getMobile(),
                entity.getEmail()
            );
            
            NodeId nodeId = NodeId.of(entity.getId());
            UserId ownerId = UserId.of(entity.getOwnedBy());
            
            Node node = Node.register(nodeId, address, spec, ownerId);
            
            // 设置运行时状态
            setNodeRuntimeState(node, entity);
            
            return node;
            
        } catch (Exception e) {
            throw new AdapterException("Failed to convert NodeEntity to Node domain object", e);
        }
    }
    
    /**
     * 将节点聚合转换为遗留实体
     */
    public NodeEntity toLegacyNode(Node node) {
        if (node == null) return null;
        
        NodeEntity entity = new NodeEntity();
        entity.setId(node.getId().getValue());
        entity.setHashId(node.getAddress().getHashId());
        entity.setSn(node.getAddress().getSn());
        entity.setHostname(node.getAddress().getHostname());
        entity.setStatus(node.getStatus());
        entity.setEnableLogCollection(node.isEnableLogCollection());
        entity.setOs(node.getSpec().getOs());
        entity.setOsVersion(node.getSpec().getOsVersion());
        entity.setMaxSceneInstanceCount(node.getSpec().getMaxSceneInstanceCount());
        entity.setSceneRunningCount(node.getSceneRunningCount());
        entity.setSceneSeatsTaken(node.getSceneSeatsTaken());
        entity.setOrganization(node.getSpec().getOrganization());
        entity.setMobile(node.getSpec().getMobile());
        entity.setEmail(node.getSpec().getEmail());
        entity.setOwnedBy(node.getOwnedBy().getValue());
        
        return entity;
    }
    
    /**
     * 将遗留场景实体转换为场景聚合
     */
    public Scene toDomainScene(SceneEntity entity) {
        if (entity == null) return null;
        
        try {
            SceneConfig config = new SceneConfig(
                entity.getName(),
                entity.getVersion(),
                entity.getDescription(),
                entity.getIndustry(),
                entity.getLink(),
                entity.getIsOfficial(),
                entity.getIntro()
            );
            
            SceneId sceneId = SceneId.of(entity.getId());
            Scene scene = Scene.create(sceneId, config, entity.getThumb());
            
            // 设置额外状态
            if (entity.getIsBanned()) {
                scene.ban(entity.getBannedTime());
            }
            
            // 设置运行时座位信息
            if (entity.getSeatsTaken() != null && entity.getSeatsTotal() != null) {
                scene.updateSeatInfo(entity.getSeatsTaken(), entity.getSeatsTotal());
            }
            
            return scene;
            
        } catch (Exception e) {
            throw new AdapterException("Failed to convert SceneEntity to Scene domain object", e);
        }
    }
    
    /**
     * 将场景聚合转换为遗留实体
     */
    public SceneEntity toLegacyScene(Scene scene) {
        if (scene == null) return null;
        
        SceneEntity entity = new SceneEntity();
        entity.setId(scene.getId().getValue());
        entity.setName(scene.getConfig().getName());
        entity.setVersion(scene.getConfig().getVersion());
        entity.setDescription(scene.getConfig().getDescription());
        entity.setIndustry(scene.getConfig().getIndustry());
        entity.setLink(scene.getConfig().getLink());
        entity.setIsOfficial(scene.getConfig().isOfficial());
        entity.setIntro(scene.getConfig().getIntro());
        entity.setThumb(scene.getThumb());
        entity.setIsBanned(scene.isBanned());
        entity.setBannedTime(scene.getBannedTime());
        entity.setChangelog(scene.getChangelog());
        
        // 转换时间戳
        if (scene.getViewedAt() != null) {
            entity.setViewedAt(java.util.Date.from(
                scene.getViewedAt().atZone(ZoneId.systemDefault()).toInstant()));
        }
        
        return entity;
    }
    
    /**
     * 批量转换用户列表
     */
    public java.util.List<User> toDomainUsers(java.util.List<UserEntity> entities) {
        return entities.stream()
            .map(this::toDomainUser)
            .filter(java.util.Objects::nonNull)
            .collect(java.util.stream.Collectors.toList());
    }
    
    /**
     * 批量转换节点列表
     */
    public java.util.List<Node> toDomainNodes(java.util.List<NodeEntity> entities) {
        return entities.stream()
            .map(this::toDomainNode)
            .filter(java.util.Objects::nonNull)
            .collect(java.util.stream.Collectors.toList());
    }
    
    private void setUserTimestamps(User user, UserEntity entity) {
        // 通过反射或专门的方法设置时间戳
        // 这里简化处理
    }
    
    private void setNodeRuntimeState(Node node, NodeEntity entity) {
        // 设置节点的运行时状态
        if (entity.getStatus() != null) {
            switch (entity.getStatus()) {
                case ONLINE -> node.goOnline();
                case OFFLINE -> node.goOffline();
            }
        }
        
        if (entity.getEnableLogCollection()) {
            node.enableLogCollection();
        }
    }
}

/**
 * 适配器异常
 */
class AdapterException extends RuntimeException {
    public AdapterException(String message, Throwable cause) {
        super(message, cause);
    }
}
