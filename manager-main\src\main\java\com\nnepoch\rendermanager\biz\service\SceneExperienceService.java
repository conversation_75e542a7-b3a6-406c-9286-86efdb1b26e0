package com.nnepoch.rendermanager.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.nnepoch.rendermanager.api.dto.request.SceneExperienceQueryDTO;
import com.nnepoch.rendermanager.api.dto.request.SceneExperienceReqDTO;
import com.nnepoch.rendermanager.api.dto.response.SceneExperienceResDTO;
import com.nnepoch.rendermanager.api.dto.response.base.ActionResDTO;
import com.nnepoch.rendermanager.api.dto.response.base.PageResDTO;
import com.nnepoch.rendermanager.biz.entity.SceneExperienceEntity;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> 2024/6/17
 */
@Service
public interface SceneExperienceService extends IService<SceneExperienceEntity> {
    PageResDTO<SceneExperienceResDTO> getSceneExperiences(SceneExperienceQueryDTO queryDTO);

    SceneExperienceResDTO getSceneExperience(Long id);

    SceneExperienceEntity getByHashId(String hashId);

    SceneExperienceResDTO addSceneExperience(SceneExperienceReqDTO reqDTO);

    SceneExperienceResDTO updateSceneExperience(Long id, SceneExperienceReqDTO reqDTO);

    ActionResDTO<Void> banSceneExperience(Long id);

    ActionResDTO<Void> permitSceneExperience(Long id);
}
