package com.nnepoch.rendermanager.exception;


import com.nnepoch.rendermanager.api.dto.response.base.ErrorResDTO;
import io.micrometer.tracing.Span;
import io.micrometer.tracing.Tracer;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.DisabledException;
import org.springframework.security.authorization.AuthorizationDeniedException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.context.request.WebRequest;

import java.util.Optional;

/**
 * <AUTHOR> <PERSON>e 2024/6/14
 */
@RestControllerAdvice
@Slf4j
@RequiredArgsConstructor
public class GlobalExceptionHandlerAdvice {
    private final Tracer tracer;

    @ResponseBody
    @ExceptionHandler(value = BizException.class)
    public Object handleBizException(BizException e, WebRequest request, HttpServletResponse response) {
        log.error(">>> 业务异常，具体信息为：", e);

        String traceId = "";
        Span currentSpan = tracer.currentSpan();
        if (currentSpan != null) {
            traceId = currentSpan.context().traceId();
        }

        HttpStatus status = e.getHttpStatus();
        response.setStatus(status.value());

        return new ErrorResDTO(
            status.name(),
            request.getDescription(true),
            traceId,
            e.getMessage(),
            e.getClass().getSimpleName()
        );
    }

    @ResponseBody
    @ExceptionHandler(value = MethodArgumentNotValidException.class)
    @ResponseStatus(HttpStatus.UNPROCESSABLE_ENTITY)
    public Object handleBadRequestException(MethodArgumentNotValidException e, WebRequest request) {
        log.error(">>> 请求参数验证失败，具体信息为：", e);

        String traceId = "";
        Span currentSpan = tracer.currentSpan();
        if (currentSpan != null) {
            traceId = currentSpan.context().traceId();
        }

        BindingResult bindingResult = e.getBindingResult();
        String message = null;
        if (bindingResult.hasErrors()) {
            FieldError fieldError = bindingResult.getFieldError();
            if (fieldError != null) {
                message = fieldError.getDefaultMessage();
            }
        }

        return new ErrorResDTO(
            HttpStatus.UNPROCESSABLE_ENTITY.name(),
            request.getDescription(true),
            traceId,
            message,
            Optional.of(bindingResult.getAllErrors())
        );
    }

    @ResponseBody
    @ExceptionHandler(value = BadCredentialsException.class)
    @ResponseStatus(HttpStatus.UNAUTHORIZED)
    public Object handleCredentialException(BadCredentialsException e, WebRequest request) {
        log.error(">>> 认证异常，具体信息为：", e);
        String traceId = "";
        Span currentSpan = tracer.currentSpan();
        if (currentSpan != null) {
            traceId = currentSpan.context().traceId();
        }

        return new ErrorResDTO(
            HttpStatus.UNAUTHORIZED.name(),
            request.getDescription(true),
            traceId,
            "用户名或密码错误",
            Optional.of(e.getClass().getSimpleName())
        );
    }

    @ResponseBody
    @ExceptionHandler(value = DisabledException.class)
    @ResponseStatus(HttpStatus.FORBIDDEN)
    public Object handleUserDisabledException(Exception e, WebRequest request) {
        log.error(">>> 用户被禁用，具体信息为：", e);
        String traceId = "";
        Span currentSpan = tracer.currentSpan();
        if (currentSpan != null) {
            traceId = currentSpan.context().traceId();
        }

        return new ErrorResDTO(
            HttpStatus.FORBIDDEN.name(),
            request.getDescription(true),
            traceId,
            "用户被禁用",
            Optional.of(e.getClass().getSimpleName())
        );
    }

    @ResponseBody
    @ExceptionHandler(value = AuthorizationDeniedException.class)
    @ResponseStatus(HttpStatus.FORBIDDEN)
    public Object handleAuthorizationException(AuthorizationDeniedException e, WebRequest request) {
        log.error(">>> 权限不足，具体信息为：", e);
        String traceId = "";
        Span currentSpan = tracer.currentSpan();
        if (currentSpan != null) {
            traceId = currentSpan.context().traceId();
        }

        return new ErrorResDTO(
            HttpStatus.FORBIDDEN.name(),
            request.getDescription(true),
            traceId,
            "权限不足",
            Optional.of(e.getClass().getSimpleName())
        );
    }

    @ResponseBody
    @ExceptionHandler(value = SignatureException.class)
    @ResponseStatus(HttpStatus.UNAUTHORIZED)
    public Object handleSignatureException(SignatureException e, WebRequest request) {
        log.error(">>> 开放接口签名异常，具体信息为：", e);
        String traceId = "";
        Span currentSpan = tracer.currentSpan();
        if (currentSpan != null) {
            traceId = currentSpan.context().traceId();
        }

        return new ErrorResDTO(
            HttpStatus.UNAUTHORIZED.name(),
            request.getDescription(true),
            traceId,
            e.getMessage(),
            Optional.of(e.getClass().getSimpleName())
        );
    }

    @ResponseBody
    @ExceptionHandler(value = Exception.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public Object handleException(Exception e, WebRequest request) {
        log.error(">>> 系统异常，具体信息为：", e);

        String traceId = "";
        Span currentSpan = tracer.currentSpan();
        if (currentSpan != null) {
            traceId = currentSpan.context().traceId();
        }

        return new ErrorResDTO(
            HttpStatus.INTERNAL_SERVER_ERROR.name(),
            request.getDescription(true),
            traceId,
            "系统异常",
            Optional.of(e.getClass().getSimpleName())
        );
    }
}
