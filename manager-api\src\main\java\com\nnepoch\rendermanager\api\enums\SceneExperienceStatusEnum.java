package com.nnepoch.rendermanager.api.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR> 2024/6/20
 */
@RequiredArgsConstructor
@Getter
public enum SceneExperienceStatusEnum {
    IDLE(1, "未访问"),
    VISITING(2, "访问中"),
    DISABLED(3, "禁用");

    public final Integer code;
    public final String desc;

    public static SceneExperienceStatusEnum fromCode(Integer code) {
        for (SceneExperienceStatusEnum status : SceneExperienceStatusEnum.values()) {
            if (status.code.equals(code)) {
                return status;
            }
        }
        return null;
    }
}
