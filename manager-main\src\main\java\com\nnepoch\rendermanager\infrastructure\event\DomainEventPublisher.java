package com.nnepoch.rendermanager.infrastructure.event;

import com.nnepoch.rendermanager.domain.shared.event.DomainEvent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

/**
 * 领域事件发布器
 * <AUTHOR> Migration
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class DomainEventPublisher {
    
    private final ApplicationEventPublisher applicationEventPublisher;
    
    /**
     * 发布领域事件
     */
    public void publish(DomainEvent event) {
        log.info("Publishing domain event: {} with ID: {}", event.getEventType(), event.getEventId());
        applicationEventPublisher.publishEvent(event);
    }
}
