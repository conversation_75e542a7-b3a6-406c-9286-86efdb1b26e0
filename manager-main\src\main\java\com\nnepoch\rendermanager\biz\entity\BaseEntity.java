package com.nnepoch.rendermanager.biz.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR> 2024/6/13
 */
@Data
public class BaseEntity {
    @TableId(type = IdType.ASSIGN_ID)
    @OrderBy(sort = 10)
    private Long id;

    private String remark;

    @TableField(fill = FieldFill.INSERT)
    private Date createdAt;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updatedAt;

    @TableLogic(delval = "now()", value = "null")
    private Date deletedAt;
}
