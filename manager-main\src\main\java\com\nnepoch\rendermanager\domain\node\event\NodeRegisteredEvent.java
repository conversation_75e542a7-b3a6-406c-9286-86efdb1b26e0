package com.nnepoch.rendermanager.domain.node.event;

import com.nnepoch.rendermanager.domain.shared.event.DomainEvent;
import com.nnepoch.rendermanager.domain.shared.valueobject.NodeId;
import lombok.Getter;

/**
 * 节点注册事件
 * <AUTHOR> Migration
 */
@Getter
public class NodeRegisteredEvent extends DomainEvent {
    private final NodeId nodeId;
    private final String sn;
    private final String hostname;
    
    public NodeRegisteredEvent(NodeId nodeId, String sn, String hostname) {
        super();
        this.nodeId = nodeId;
        this.sn = sn;
        this.hostname = hostname;
    }
    
    @Override
    public String getEventType() {
        return "NodeRegistered";
    }
}
