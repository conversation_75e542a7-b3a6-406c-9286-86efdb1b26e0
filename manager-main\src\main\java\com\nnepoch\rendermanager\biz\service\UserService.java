package com.nnepoch.rendermanager.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.nnepoch.rendermanager.api.dto.request.ModifyPasswordReqDTO;
import com.nnepoch.rendermanager.api.dto.request.SimpleUserQueryDTO;
import com.nnepoch.rendermanager.api.dto.request.UserQueryDTO;
import com.nnepoch.rendermanager.api.dto.request.UserReqDTO;
import com.nnepoch.rendermanager.api.dto.response.SimpleUserResDTO;
import com.nnepoch.rendermanager.api.dto.response.UserResDTO;
import com.nnepoch.rendermanager.api.dto.response.base.ActionResDTO;
import com.nnepoch.rendermanager.api.dto.response.base.PageResDTO;
import com.nnepoch.rendermanager.biz.entity.UserEntity;
import org.springframework.security.core.userdetails.UserDetailsService;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> <PERSON>e 2024/6/13
 */
public interface UserService extends IService<UserEntity> {
    List<Map<String, Object>> getSimpleUserList(SimpleUserQueryDTO queryDTO);

    PageResDTO<UserResDTO> getUserList(UserQueryDTO queryDTO);

    UserResDTO getCurUser();

    UserResDTO addUser(UserReqDTO reqDTO);

    <T> T getUserByMobile(String mobile, Class<T> clazz);

    void doAfterSignin(UserEntity userEntity);

    UserResDTO updateUser(Long id, UserReqDTO reqDTO);

    ActionResDTO<Void> banUser(Long id);

    ActionResDTO<Void> permitUser(Long id);

    ActionResDTO<Void> deleteUser(Long id);

    ActionResDTO<Map<String, String>> resetPassword(Long id);

    ActionResDTO<Void> modifyPassword(Long id, ModifyPasswordReqDTO reqDTO);

    UserEntity getUserByMobileWithPassword(String mobile);

    SimpleUserResDTO getSimpleUserById(Long id);

    UserDetailsService userDetailsService();
}
