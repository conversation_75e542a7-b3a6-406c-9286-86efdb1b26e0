package com.nnepoch.rendermanager.api.dto.request;

import com.nnepoch.rendermanager.api.enums.UserRoleEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR> 2024/6/13
 */
@Data
@Schema(description = "用户请求对象")
public class UserReqDTO {
    @Schema(requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "用户名不能为空")
    private String username;

    private String email;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "手机号不能为空")
    private String mobile;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "角色不能为空")
    private UserRoleEnum role;

    @Schema(description = "备注")
    private String remark;
}
