package com.nnepoch.rendermanager.biz.mq;

import com.nnepoch.rendermanager.config.RabbitConfig;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.MessagePostProcessor;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Component
public class DelayProducer {
    @Resource
    private RabbitTemplate rabbitTemplate;

    public void send(String msg, Integer delaySeconds) {
        log.info("send message to delay queue, msg:{}, delay seconds:{}", msg, delaySeconds);

        MessagePostProcessor messagePostProcessor = message -> {
            Map<String, Object> args = new HashMap<>();
            args.put("x-delay", delaySeconds * 1000);
            message.getMessageProperties().setHeaders(args);

            return message;
        };

        rabbitTemplate.convertAndSend(RabbitConfig.DELAY_EXCHANGE_NAME, RabbitConfig.DELAY_ROUTING_KEY, msg, messagePostProcessor);
    }
}
