package com.nnepoch.rendermanager.api.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR> 2024/6/20
 */
@RequiredArgsConstructor
@Getter
public enum NodeStatusEnum {
    ONLINE(1, "在线"),
    OFFLINE(2, "离线"),
    UNKNOWN(-1, "未知");

    public final Integer code;
    public final String desc;

    public static NodeStatusEnum fromCode(Integer code) {
        for (NodeStatusEnum status : NodeStatusEnum.values()) {
            if (status.code.equals(code)) {
                return status;
            }
        }
        return UNKNOWN;
    }
}
