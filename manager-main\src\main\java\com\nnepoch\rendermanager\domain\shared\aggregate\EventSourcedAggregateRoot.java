package com.nnepoch.rendermanager.domain.shared.aggregate;

import com.nnepoch.rendermanager.domain.shared.event.DomainEvent;
import com.nnepoch.rendermanager.domain.shared.valueobject.EntityId;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;

/**
 * 事件溯源聚合根
 * <AUTHOR> Architect
 */
public abstract class EventSourcedAggregateRoot<ID extends EntityId> extends AggregateRoot<ID> {
    
    private Long version = 0L;
    private final List<DomainEvent> uncommittedEvents = new ArrayList<>();
    
    protected EventSourcedAggregateRoot(ID id) {
        super(id);
    }
    
    /**
     * 应用事件并记录为未提交事件
     */
    protected void applyEvent(DomainEvent event) {
        applyEventInternal(event);
        uncommittedEvents.add(event);
    }
    
    /**
     * 从历史事件重建聚合状态
     */
    public void loadFromHistory(List<DomainEvent> history) {
        for (DomainEvent event : history) {
            applyEventInternal(event);
            version++;
        }
    }
    
    /**
     * 获取未提交的事件
     */
    public List<DomainEvent> getUncommittedEvents() {
        return new ArrayList<>(uncommittedEvents);
    }
    
    /**
     * 标记事件为已提交
     */
    public void markEventsAsCommitted() {
        uncommittedEvents.clear();
        clearDomainEvents();
    }
    
    /**
     * 获取当前版本号
     */
    public Long getVersion() {
        return version;
    }
    
    /**
     * 设置版本号（用于从存储加载时）
     */
    public void setVersion(Long version) {
        this.version = version;
    }
    
    /**
     * 内部应用事件（不记录为未提交）
     */
    private void applyEventInternal(DomainEvent event) {
        try {
            // 使用反射调用对应的事件处理方法
            String methodName = "apply" + event.getClass().getSimpleName();
            Method method = this.getClass().getDeclaredMethod(methodName, event.getClass());
            method.setAccessible(true);
            method.invoke(this, event);
        } catch (Exception e) {
            throw new IllegalStateException("Failed to apply event: " + event.getClass().getSimpleName(), e);
        }
    }
    
    /**
     * 创建快照
     */
    public abstract AggregateSnapshot createSnapshot();
    
    /**
     * 从快照恢复
     */
    public abstract void restoreFromSnapshot(AggregateSnapshot snapshot);
    
    /**
     * 检查是否需要创建快照
     */
    public boolean shouldCreateSnapshot() {
        // 每100个事件创建一次快照
        return version % 100 == 0 && version > 0;
    }
}

/**
 * 聚合快照接口
 */
interface AggregateSnapshot {
    String getAggregateId();
    String getAggregateType();
    Long getVersion();
    Object getData();
}
