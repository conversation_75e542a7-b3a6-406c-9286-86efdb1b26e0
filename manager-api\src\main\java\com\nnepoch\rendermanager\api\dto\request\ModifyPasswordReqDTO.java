package com.nnepoch.rendermanager.api.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

/**
 * <AUTHOR> 2024/6/16
 */
@Data
@Schema(description = "修改用户密码")
public class ModifyPasswordReqDTO {
    @Pattern(regexp = "^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{6,16}$", message = "密码必须包含字母和数字，长度在6-16位之间")
    private String oldPassword;

    @Pattern(regexp = "^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{6,16}$", message = "密码必须包含字母和数字，长度在6-16位之间")
    private String newPassword;
}
