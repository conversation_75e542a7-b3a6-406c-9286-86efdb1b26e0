-- This is the initial setup of the database tables.
-- 用户表
CREATE TABLE IF NOT EXISTS public.user
(
    id         bigint PRIMARY KEY NOT NULL,
    username   varchar(255)       NOT NULL,
    role       varchar(20)        NOT NULL DEFAULT 'member',
    password   varchar(255),
    email      varchar(255),
    mobile     varchar(20),
    login_at   timestamp,
    is_banned  boolean            NOT NULL DEFAULT false,
    created_at timestamp          NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp          NOT NULL DEFAULT CURRENT_TIMESTAMP,
    remark     varchar(255),
    deleted_at timestamp,
    CONSTRAINT uq_user_username UNIQUE (username),
    CONSTRAINT uq_user_mobile UNIQUE (mobile)
);

-- 场景表
CREATE TABLE IF NOT EXISTS public.scene
(
    id          bigint PRIMARY KEY NOT NULL,
    name        varchar(255)       NOT NULL,
    thumb       varchar(255),
    description text,
    status      smallint,
    industry    smallint,
    link        varchar(255),
    is_official boolean            NOT NULL DEFAULT true,
    intro       text,
    viewed_at   timestamp,
    created_at  timestamp          NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at  timestamp          NOT NULL DEFAULT CURRENT_TIMESTAMP,
    remark      varchar(255),
    deleted_at  timestamp,
    CONSTRAINT uq_scene_name UNIQUE (name)
);

-- 节点表
CREATE TABLE IF NOT EXISTS public.node
(
    id                    bigint PRIMARY KEY NOT NULL,
    hash_id               varchar(20)        NOT NULL,
    sn                    varchar(64)        NOT NULL,
    hostname              varchar(255)       NOT NULL,
    os                    varchar(20)        NOT NULL,
    os_version            varchar(20),
    status                smallint           NOT NULL DEFAULT 1,
    enable_log_collection boolean            NOT NULL DEFAULT true,
    scene_count           int                NOT NULL DEFAULT 0,
    scene_running_count   int                NOT NULL DEFAULT 0,
    scene_seats_taken     int                NOT NULL DEFAULT 0,
    organization          varchar(255),
    mobile                varchar(20),
    email                 varchar(255),
    owned_by              bigint,
    created_at            timestamp          NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at            timestamp          NOT NULL DEFAULT CURRENT_TIMESTAMP,
    remark                varchar(255),
    deleted_at            timestamp,
    CONSTRAINT uq_node_hash_id UNIQUE (hash_id),
    CONSTRAINT uq_node_sn UNIQUE (sn)
);
CREATE INDEX IF NOT EXISTS idx_node_owned_by ON node (owned_by);

-- 场景节点关联表
CREATE TABLE IF NOT EXISTS public.scene_node
(
    id         bigint PRIMARY KEY NOT NULL,
    scene_id   bigint             NOT NULL,
    node_id    bigint             NOT NULL,
    version    varchar(20)        NOT NULL,
    created_at timestamp          NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp          NOT NULL DEFAULT CURRENT_TIMESTAMP,
    remark     varchar(255),
    deleted_at timestamp,
    CONSTRAINT uq_scene_node_scene_id_node_id_version UNIQUE (scene_id, node_id, version)
);
CREATE INDEX IF NOT EXISTS idx_scene_node_scene_id ON scene_node (scene_id);
CREATE INDEX IF NOT EXISTS idx_scene_node_node_id ON scene_node (node_id);

-- 场景实例表
CREATE TABLE IF NOT EXISTS public.scene_instance
(
    id           bigint PRIMARY KEY NOT NULL,
    scene_id     bigint             NOT NULL,
    node_id      bigint             NOT NULL,
    session_id   varchar(255),
    display_name varchar(255)       NOT NULL,
    program_name varchar(255)       NOT NULL,
    exe_name     varchar(255)       NOT NULL,
    version      varchar(20),
    resolution   varchar(20),
    fps          smallint,
    bitrate      int,
    app_dir      varchar(255)       NOT NULL,
    exe          varchar(255)       NOT NULL,
    process_id   int4               NOT NULL,
    start_time   timestamp          NOT NULL,
    run_time     int                NOT NULL,
    memory       int,
    cpu_usage    float4
);
CREATE INDEX IF NOT EXISTS idx_scene_instance_scene_id ON scene_instance (scene_id);
CREATE INDEX IF NOT EXISTS idx_scene_instance_node_id ON scene_instance (node_id);

-- 场景分发节点记录表
CREATE TABLE IF NOT EXISTS public.scene_schedule
(
    id            bigint PRIMARY KEY NOT NULL,
    scene_id      bigint             NOT NULL,
    node_id       bigint             NOT NULL,
    scene_version varchar(20)        NOT NULL,
    created_by    bigint             NOT NULL,
    created_at    timestamp          NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at    timestamp          NOT NULL DEFAULT CURRENT_TIMESTAMP,
    remark        varchar(255),
    deleted_at    timestamp
);
CREATE INDEX IF NOT EXISTS idx_scene_schedule_scene_id ON scene_schedule (scene_id);
CREATE INDEX IF NOT EXISTS idx_scene_schedule_node_id ON scene_schedule (node_id);

-- 场景体验表
CREATE TABLE IF NOT EXISTS public.scene_experience
(
    id           bigint PRIMARY KEY NOT NULL,
    scene_id     bigint             NOT NULL,
    status       smallint           NOT NULL DEFAULT 1,
    username     varchar(255)       NOT NULL,
    organization varchar(255),
    mobile       varchar(20),
    email        varchar(255),
    duration     int                NOT NULL,
    expired_at   timestamp          NOT NULL,
    viewed_at    timestamp,
    created_by   bigint             NOT NULL,
    created_at   timestamp          NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at   timestamp          NOT NULL DEFAULT CURRENT_TIMESTAMP,
    remark       varchar(255),
    deleted_at   timestamp
);
CREATE INDEX IF NOT EXISTS idx_scene_experience_scene_id ON scene_experience (scene_id);

