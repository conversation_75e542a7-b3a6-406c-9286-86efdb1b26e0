package com.nnepoch.rendermanager.runner;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.nnepoch.rendermanager.api.enums.UserRoleEnum;
import com.nnepoch.rendermanager.biz.entity.UserEntity;
import com.nnepoch.rendermanager.biz.service.UserService;
import com.nnepoch.rendermanager.config.Constants;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.security.crypto.argon2.Argon2PasswordEncoder;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@RequiredArgsConstructor
public class InitUserCommandLineRunner implements CommandLineRunner {
    private final UserService userService;

    @Override
    public void run(String... args) throws Exception {
        log.info("Running InitUserCommandLineRunner");

        UserEntity user = userService.getOne(Wrappers.<UserEntity>lambdaQuery().eq(UserEntity::getMobile, Constants.ADMIN_MOBILE));
        if (user == null) {
            UserEntity admin = new UserEntity();
            admin.setUsername(Constants.ADMIN_USERNAME);
            admin.setPassword(Argon2PasswordEncoder.defaultsForSpringSecurity_v5_8().encode(Constants.ADMIN_PASSWORD));
            admin.setMobile(Constants.ADMIN_MOBILE);
            admin.setRole(UserRoleEnum.ADMIN);
            admin.setEmail("<EMAIL>");
            userService.save(admin);
        }

        UserEntity openApiUser = userService.getOne(Wrappers.<UserEntity>lambdaQuery().eq(UserEntity::getMobile, Constants.OpenAPI_MOBILE));
        if (openApiUser == null) {
            UserEntity openApi = new UserEntity();
            openApi.setUsername(Constants.OpenAPI_USERNAME);
            openApi.setPassword(Argon2PasswordEncoder.defaultsForSpringSecurity_v5_8().encode(Constants.OpenAPI_PASSWORD));
            openApi.setMobile(Constants.OpenAPI_MOBILE);
            openApi.setRole(UserRoleEnum.MEMBER);
            openApi.setEmail("<EMAIL>");
            userService.save(openApi);
        }

        log.info("InitUserCommandLineRunner finished");
    }
}
