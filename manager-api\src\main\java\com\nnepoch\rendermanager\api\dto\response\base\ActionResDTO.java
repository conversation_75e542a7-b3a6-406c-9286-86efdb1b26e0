package com.nnepoch.rendermanager.api.dto.response.base;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> 2024/6/14
 */
@Data
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@Schema(description = "操作响应DTO")
public class ActionResDTO<T> {
    @Schema(description = "操作是否成功")
    private Boolean success;

    @Schema(description = "操作返回数据")
    private T data;

    @Schema(description = "操作错误信息")
    private String cause;

    public ActionResDTO(Boolean success) {
        this.success = success;
    }

    public ActionResDTO(Boolean success, T data) {
        this.success = success;
        this.data = data;
    }

    public static ActionResDTO<Void> success() {
        return new ActionResDTO<>(true);
    }

    public static <T> ActionResDTO<T> success(T data) {
        return new ActionResDTO<>(true, data);
    }

    public static ActionResDTO<Void> fail() {
        return new ActionResDTO<>(false);
    }
}
