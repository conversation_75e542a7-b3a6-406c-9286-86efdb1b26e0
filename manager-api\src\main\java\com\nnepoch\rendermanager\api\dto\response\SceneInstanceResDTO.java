package com.nnepoch.rendermanager.api.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.sql.Timestamp;

/**
 * <AUTHOR> 2024/8/8
 */
@Data
@Schema(description = "场景内部体验信息")
public class SceneInstanceResDTO {
    @Schema(description = "实例 ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    @Schema(description = "节点ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long nodeId;

    @Schema(description = "节点 SN")
    private String nodeSn;

    @Schema(description = "实例会话 ID")
    private String sessionId;

    @Schema(description = "进程 ID")
    private Integer processId;

    @Schema(description = "场景体验 ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long sceneExperienceId;

    @Schema(description = "分辨率")
    private String resolution;

    @Schema(description = "帧率")
    private Short fps;

    @Schema(description = "码率")
    private Integer bitrate;

    @Schema(description = "内存占用")
    private Long memory;

    @Schema(description = "CPU 使用率")
    private Float cpuUsage;

    @Schema(description = "最近访问时间")
    private Timestamp startTime;

    @Schema(description = "运行时长 单位：秒")
    private Integer runTime;

    @Schema(description = "实例归属人")
    private SimpleUserResDTO owner;

    @Schema(description = "场景体验用户信息")
    private SimpleUserResDTO sceneExperience;
}
