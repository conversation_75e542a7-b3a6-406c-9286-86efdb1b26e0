package com.nnepoch.rendermanager.infrastructure.event.store;

import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 存储的快照
 * <AUTHOR> Architect
 */
@Data
@Builder
public class StoredSnapshot {
    private String aggregateId;
    private String aggregateType;
    private String snapshotData;
    private Long version;
    private LocalDateTime createdAt;
    
    /**
     * 反序列化快照数据
     */
    @SuppressWarnings("unchecked")
    public <T> T getSnapshotData(Class<T> snapshotClass) {
        // 使用Jackson或其他JSON库反序列化
        // 这里简化处理
        return null;
    }
}
