package com.nnepoch.rendermanager.config;

import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.security.SecurityScheme;
import org.springdoc.core.models.GroupedOpenApi;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR> 2024/6/13
 */
@Configuration
public class SpringDocConfig {
    @Bean
    public OpenAPI apiDocs(@Value("${springdoc.version}") String appVersion) {
        return new OpenAPI()
            .components(
                new Components()
                    .addSecuritySchemes(
                        "bearerAuth",
                        new SecurityScheme()
                            .type(SecurityScheme.Type.HTTP)
                            .bearerFormat("JWT")
                            .in(SecurityScheme.In.HEADER)
                            .name("Authorization")
                            .scheme("Bearer")
                    )
            )
            .info(
                new Info()
                    .title("Render Manager API Documentation")
                    .version(appVersion)
                    .description("This is the API documentation for Render Manager.")
            );
    }

    @Bean
    public GroupedOpenApi openApiGroup() {
        return GroupedOpenApi.builder()
            .group("open-api")
            .displayName("Open API")
            .pathsToMatch("/open/**")
            .build();
    }
}
