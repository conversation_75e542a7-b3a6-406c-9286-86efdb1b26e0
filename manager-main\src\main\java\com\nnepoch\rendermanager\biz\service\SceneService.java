package com.nnepoch.rendermanager.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.nnepoch.rendermanager.api.dto.request.*;
import com.nnepoch.rendermanager.api.dto.response.NodeResDTO;
import com.nnepoch.rendermanager.api.dto.response.SceneResDTO;
import com.nnepoch.rendermanager.api.dto.response.base.ActionResDTO;
import com.nnepoch.rendermanager.api.dto.response.base.PageResDTO;
import com.nnepoch.rendermanager.biz.dto.mq.SceneInfoMQDTO;
import com.nnepoch.rendermanager.biz.entity.NodeEntity;
import com.nnepoch.rendermanager.biz.entity.SceneEntity;

/**
 * <AUTHOR> 2024/6/17
 */
public interface SceneService extends IService<SceneEntity> {
    PageResDTO<SceneResDTO> getSceneList(SceneQueryDTO queryDTO);

    SceneEntity syncScene(SceneInfoMQDTO scene, NodeResDTO node);

    SceneResDTO getSceneInfo(Long id);

    SceneEntity getSceneInfoByName(String name);

    SceneResDTO addScene(SceneCreateDTO reqDTO);

    SceneResDTO updateScene(Long id, SceneUpdateDTO reqDTO);

    ActionResDTO<NodeEntity> startScene(Long id, SceneStartOptionDTO optionDTO);

    ActionResDTO<Void> stopScene(Long id, SceneStopOptionDTO optionDTO);

    ActionResDTO<Void> restartScene(Long id);

    ActionResDTO<Void> banScene(Long id, Integer bannedTime) throws JsonProcessingException;

    ActionResDTO<Void> permitScene(Long id);

    ActionResDTO<Void> freeSceneInstance(Long id);
}
