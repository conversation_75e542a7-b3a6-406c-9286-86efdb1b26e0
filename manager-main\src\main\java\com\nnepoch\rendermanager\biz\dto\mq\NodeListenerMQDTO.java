package com.nnepoch.rendermanager.biz.dto.mq;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.SneakyThrows;

import java.util.HashMap;
import java.util.Map;

@Data
@AllArgsConstructor
public class NodeListenerMQDTO {
    private NodeListenerRpcActionEnum trigger;
    private NodeListenerRpcPayload data;

    @SneakyThrows
    @Override
    public String toString() {
        ObjectMapper mapper = new ObjectMapper();

        Map<String, Object> map = new HashMap<String, Object>();
        map.put("trigger", trigger.getAction());
        map.put("data", data);

        return mapper.writeValueAsString(map);
    }


    @AllArgsConstructor
    @Getter
    public enum NodeListenerRpcActionEnum {
        NODE_UPDATE("node.update", "节点信息更新"),
        NODE_PING("node.ping", "节点心跳");

        private final String action;
        private final String desc;
    }

    public record NodeListenerRpcPayload<T>(T payload) {
    }
}
