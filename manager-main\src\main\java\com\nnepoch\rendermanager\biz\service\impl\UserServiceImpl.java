package com.nnepoch.rendermanager.biz.service.impl;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.RandomUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nnepoch.rendermanager.api.dto.request.ModifyPasswordReqDTO;
import com.nnepoch.rendermanager.api.dto.request.SimpleUserQueryDTO;
import com.nnepoch.rendermanager.api.dto.request.UserQueryDTO;
import com.nnepoch.rendermanager.api.dto.request.UserReqDTO;
import com.nnepoch.rendermanager.api.dto.response.SimpleUserResDTO;
import com.nnepoch.rendermanager.api.dto.response.UserResDTO;
import com.nnepoch.rendermanager.api.dto.response.base.ActionResDTO;
import com.nnepoch.rendermanager.api.dto.response.base.PageResDTO;
import com.nnepoch.rendermanager.api.enums.UserRoleEnum;
import com.nnepoch.rendermanager.biz.entity.UserEntity;
import com.nnepoch.rendermanager.biz.mapper.RoleMapper;
import com.nnepoch.rendermanager.biz.mapper.UserMapper;
import com.nnepoch.rendermanager.biz.service.UserService;
import com.nnepoch.rendermanager.exception.BizException;
import com.nnepoch.rendermanager.utils.SqlOrderUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.scheduling.annotation.Async;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.crypto.argon2.Argon2PasswordEncoder;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Che 2024/6/13
 */
@RequiredArgsConstructor
@Service
public class UserServiceImpl extends ServiceImpl<UserMapper, UserEntity> implements UserService {
    private final UserMapper userMapper;
    private final RoleMapper roleMapper;

    @Override
    public List<Map<String, Object>> getSimpleUserList(SimpleUserQueryDTO queryDTO) {
        LambdaQueryWrapper<UserEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(CharSequenceUtil.isNotEmpty(queryDTO.getUsername()), UserEntity::getUsername, queryDTO.getUsername());
        if (CharSequenceUtil.isNotEmpty(queryDTO.getRoles())) {
            String[] roleNames = queryDTO.getRoles().split(",");
            List<UserRoleEnum> roles = Arrays.stream(roleNames).map(UserRoleEnum::fromName).toList();

            if (roles.contains(null)) {
                throw new BizException(HttpStatus.UNPROCESSABLE_ENTITY, "角色不存在");
            }

            wrapper.in(UserEntity::getRole, roles);
        }
        wrapper.select(UserEntity::getId, UserEntity::getUsername);

        return userMapper.selectList(wrapper).stream().map(user -> {
            Map<String, Object> map = new HashMap<>();
            map.put("id", user.getId().toString());
            map.put("username", user.getUsername());
            map.put("avatar", user.getAvatar());
            return map;
        }).toList();
    }

    @Override
    public PageResDTO<UserResDTO> getUserList(UserQueryDTO queryDTO) {
        Page<UserEntity> pageObject = new Page<>(queryDTO.getPage(), queryDTO.getSize());

        LambdaQueryWrapper<UserEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(CharSequenceUtil.isNotEmpty(queryDTO.getUsername()), UserEntity::getUsername, queryDTO.getUsername());
        if (queryDTO.getRole() != null) {
            wrapper.eq(UserEntity::getRole, queryDTO.getRole().getCode());
        }
        if (CharSequenceUtil.isNotEmpty(queryDTO.getSort())) {
            pageObject.addOrder(SqlOrderUtil.parseOrderItem(queryDTO.getSort()));
        }

        IPage<UserEntity> page = page(pageObject, wrapper);

        return new PageResDTO<>(
            page.getRecords().stream().map(this::toDTO).toList(),
            new PageResDTO.Pagination(page.getCurrent(), page.getSize(), page.getTotal())
        );
    }

    @Override
    public UserResDTO getCurUser() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        UserEntity user = (UserEntity) authentication.getPrincipal();
        return toDTO(user);
    }

    @Override
    public UserResDTO addUser(UserReqDTO reqDTO) {
        UserEntity entity = toEntity(reqDTO);
        String rawPassword = generatePasswordWithDigitAndLetter(8);
        entity.setRawPassword(rawPassword);
        entity.setPassword(Argon2PasswordEncoder.defaultsForSpringSecurity_v5_8().encode(rawPassword));
        userMapper.insert(entity);
        return toDTO(entity);
    }

    @Override
    public <T> T getUserByMobile(String mobile, Class<T> clazz) {
        UserEntity user = userMapper.selectOne(Wrappers.<UserEntity>lambdaQuery().eq(UserEntity::getMobile, mobile));

        if (clazz.isAssignableFrom(UserEntity.class)) {
            return clazz.cast(user);
        } else {
            return clazz.cast(toDTO(user));
        }
    }

    @Override
    @Async
    public void doAfterSignin(UserEntity user) {
        user.setLoginAt(new Date());
        userMapper.updateById(user);
    }

    @Override
    public UserResDTO updateUser(Long id, UserReqDTO reqDTO) {
        UserEntity entity = toEntity(reqDTO);
        entity.setId(id);
        userMapper.updateById(entity);
        return toDTO(entity);
    }

    @Override
    public ActionResDTO<Void> banUser(Long id) {
        UserEntity entity = new UserEntity();
        entity.setId(id);
        entity.setIsBanned(true);
        userMapper.updateById(entity);
        return ActionResDTO.success();
    }

    @Override
    public ActionResDTO<Void> permitUser(Long id) {
        UserEntity entity = new UserEntity();
        entity.setId(id);
        entity.setIsBanned(false);
        userMapper.updateById(entity);
        return ActionResDTO.success();
    }

    @Override
    public ActionResDTO<Void> deleteUser(Long id) {
        UserEntity user = userMapper.selectById(id);
        if (user.getUsername().equals("admin")) {
            throw new BizException("不允许删除管理员");
        }

        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        UserEntity currentUser = (UserEntity) authentication.getPrincipal();
        if (currentUser.getId().equals(id)) {
            throw new BizException("不允许删除自己");
        }

        userMapper.physicalDeleteById(id);
        return ActionResDTO.success();
    }

    @Override
    public ActionResDTO<Map<String, String>> resetPassword(Long id) {
        UserEntity entity = new UserEntity();
        entity.setId(id);
        // 生成 8位随机密码
        String rawPassword = generatePasswordWithDigitAndLetter(8);
        entity.setRawPassword(rawPassword);
        entity.setPassword(Argon2PasswordEncoder.defaultsForSpringSecurity_v5_8().encode(rawPassword));
        userMapper.insertOrUpdate(entity);
        return ActionResDTO.success(Map.of("rawPassword", rawPassword));
    }

    @Override
    public ActionResDTO<Void> modifyPassword(Long id, ModifyPasswordReqDTO reqDTO) {
        UserEntity entity = userMapper.selectOne(
            Wrappers.lambdaQuery(UserEntity.class)
                .eq(UserEntity::getId, id)
                .select(UserEntity::getId, UserEntity::getPassword)
        );
        boolean matched = Argon2PasswordEncoder.defaultsForSpringSecurity_v5_8().matches(reqDTO.getOldPassword(), entity.getPassword());

        if (!matched) {
            throw new BizException("旧密码错误");
        }

        entity.setPassword(Argon2PasswordEncoder.defaultsForSpringSecurity_v5_8().encode(reqDTO.getNewPassword()));
        userMapper.updateById(entity);
        return ActionResDTO.success();
    }

    @Override
    public UserEntity getUserByMobileWithPassword(String mobile) {
        return userMapper.getUserByMobileWithPassword(mobile);
    }

    @Override
    public SimpleUserResDTO getSimpleUserById(Long id) {
        return userMapper.getSimpleUserById(id);
    }

    @Override
    public UserDetailsService userDetailsService() {
        return new UserDetailsService() {
            @Override
            public UserDetails loadUserByUsername(String mobile) throws UsernameNotFoundException {
                // 手机号登录，作为唯一用户名（适配 spring security）
                UserEntity user = getUserByMobileWithPassword(mobile);
                if (user == null) {
                    throw new UsernameNotFoundException("用户名或密码错误");
                }

                return user;
            }
        };
    }

    private String generatePasswordWithDigitAndLetter(int length) {
        StringBuilder password = new StringBuilder();
        String letters = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";
        String digits = "0123456789";

        // 确保密码至少有一个字母和一个数字
        password.append(RandomUtil.randomChar(letters));
        password.append(RandomUtil.randomChar(digits));

        String remainChars = RandomUtil.randomString(length - 2);
        password.append(remainChars);

        // 打乱密码顺序
        List<Character> charList = password.toString().chars()
            .mapToObj(c -> (char) c)
            .collect(Collectors.toList());
        Collections.shuffle(charList);

        StringBuilder result = new StringBuilder(charList.size());
        for (Character c : charList) {
            result.append(c);
        }

        return result.toString();
    }

    private UserEntity toEntity(UserReqDTO reqDTO) {
        UserEntity entity = new UserEntity();
        entity.setUsername(reqDTO.getUsername());
        entity.setRole(reqDTO.getRole());
        entity.setEmail(reqDTO.getEmail());
        entity.setMobile(reqDTO.getMobile());
        entity.setRemark(reqDTO.getRemark());
        return entity;
    }

    private UserResDTO toDTO(UserEntity entity) {
        if (entity == null) {
            return null;
        }

        UserResDTO resDTO = new UserResDTO();
        resDTO.setId(entity.getId());
        resDTO.setUsername(entity.getUsername());
        resDTO.setRole(entity.getRole());
        resDTO.setIsBanned(entity.getIsBanned());
        resDTO.setRawPassword(entity.getRawPassword());
        resDTO.setEmail(entity.getEmail());
        resDTO.setMobile(entity.getMobile());
        resDTO.setAvatar(entity.getAvatar());
        resDTO.setRemark(entity.getRemark());
        resDTO.setLoginAt(entity.getLoginAt());
        resDTO.setCreatedAt(entity.getCreatedAt());
        resDTO.setUpdatedAt(entity.getUpdatedAt());
        resDTO.setDeletedAt(entity.getDeletedAt());
        return resDTO;
    }
}
