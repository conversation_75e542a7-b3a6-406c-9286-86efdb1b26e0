-- DDD基础设施表结构
-- 事件存储表
CREATE TABLE IF NOT EXISTS event_store (
    id BIGSERIAL PRIMARY KEY,
    event_id VARCHAR(255) NOT NULL UNIQUE,
    aggregate_id VARCHAR(255) NOT NULL,
    aggregate_type VARCHAR(100) NOT NULL,
    event_type VARCHAR(100) NOT NULL,
    event_data JSONB NOT NULL,
    metadata JSONB,
    version BIGINT NOT NULL,
    occurred_on TIMESTAMP NOT NULL,
    stored_on TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT uk_event_store_aggregate_version UNIQUE (aggregate_id, aggregate_type, version)
);

-- 事件存储索引
CREATE INDEX IF NOT EXISTS idx_event_store_aggregate ON event_store (aggregate_id, aggregate_type);
CREATE INDEX IF NOT EXISTS idx_event_store_event_type ON event_store (event_type);
CREATE INDEX IF NOT EXISTS idx_event_store_occurred_on ON event_store (occurred_on);
CREATE INDEX IF NOT EXISTS idx_event_store_stored_on ON event_store (stored_on);

-- 聚合快照表
CREATE TABLE IF NOT EXISTS aggregate_snapshots (
    id BIGSERIAL PRIMARY KEY,
    aggregate_id VARCHAR(255) NOT NULL,
    aggregate_type VARCHAR(100) NOT NULL,
    snapshot_data JSONB NOT NULL,
    version BIGINT NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT uk_aggregate_snapshots UNIQUE (aggregate_id, aggregate_type)
);

-- 快照表索引
CREATE INDEX IF NOT EXISTS idx_aggregate_snapshots_type ON aggregate_snapshots (aggregate_type);
CREATE INDEX IF NOT EXISTS idx_aggregate_snapshots_version ON aggregate_snapshots (version);

-- Saga状态表
CREATE TABLE IF NOT EXISTS saga_state (
    saga_id VARCHAR(255) PRIMARY KEY,
    saga_type VARCHAR(100) NOT NULL,
    state VARCHAR(50) NOT NULL,
    data JSONB NOT NULL,
    started_at TIMESTAMP NOT NULL,
    completed_at TIMESTAMP,
    failure_reason TEXT,
    retry_count INTEGER NOT NULL DEFAULT 0,
    version BIGINT NOT NULL DEFAULT 0,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Saga状态索引
CREATE INDEX IF NOT EXISTS idx_saga_state_type ON saga_state (saga_type);
CREATE INDEX IF NOT EXISTS idx_saga_state_state ON saga_state (state);
CREATE INDEX IF NOT EXISTS idx_saga_state_started_at ON saga_state (started_at);

-- 用户读模型表（CQRS）
CREATE TABLE IF NOT EXISTS user_read_model (
    user_id BIGINT PRIMARY KEY,
    username VARCHAR(100) NOT NULL UNIQUE,
    email VARCHAR(255) NOT NULL UNIQUE,
    mobile VARCHAR(20) NOT NULL UNIQUE,
    role VARCHAR(20) NOT NULL,
    is_banned BOOLEAN NOT NULL DEFAULT FALSE,
    login_count INTEGER NOT NULL DEFAULT 0,
    last_login_at TIMESTAMP,
    status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE',
    created_at TIMESTAMP NOT NULL,
    updated_at TIMESTAMP NOT NULL
);

-- 用户读模型索引
CREATE INDEX IF NOT EXISTS idx_user_read_model_role ON user_read_model (role);
CREATE INDEX IF NOT EXISTS idx_user_read_model_status ON user_read_model (status);
CREATE INDEX IF NOT EXISTS idx_user_read_model_last_login ON user_read_model (last_login_at);
CREATE INDEX IF NOT EXISTS idx_user_read_model_created_at ON user_read_model (created_at);

-- 用户统计表
CREATE TABLE IF NOT EXISTS user_statistics (
    stat_key VARCHAR(100) PRIMARY KEY,
    stat_value BIGINT NOT NULL DEFAULT 0,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 初始化用户统计数据
INSERT INTO user_statistics (stat_key, stat_value) VALUES 
    ('TOTAL_USERS', 0),
    ('ADMIN_USERS', 0),
    ('OPERATOR_USERS', 0),
    ('MEMBER_USERS', 0),
    ('BANNED_USERS', 0),
    ('ACTIVE_USERS', 0)
ON CONFLICT (stat_key) DO NOTHING;

-- 用户登录历史表
CREATE TABLE IF NOT EXISTS user_login_history (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    login_at TIMESTAMP NOT NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    success BOOLEAN NOT NULL DEFAULT TRUE,
    failure_reason TEXT
);

-- 登录历史索引
CREATE INDEX IF NOT EXISTS idx_user_login_history_user_id ON user_login_history (user_id);
CREATE INDEX IF NOT EXISTS idx_user_login_history_login_at ON user_login_history (login_at);

-- 节点读模型表
CREATE TABLE IF NOT EXISTS node_read_model (
    node_id BIGINT PRIMARY KEY,
    sn VARCHAR(100) NOT NULL UNIQUE,
    hostname VARCHAR(255) NOT NULL,
    status VARCHAR(20) NOT NULL,
    os VARCHAR(50),
    os_version VARCHAR(50),
    max_capacity INTEGER NOT NULL,
    current_load INTEGER NOT NULL DEFAULT 0,
    load_percentage DECIMAL(5,2) GENERATED ALWAYS AS (
        CASE WHEN max_capacity > 0 THEN (current_load::DECIMAL / max_capacity * 100) ELSE 0 END
    ) STORED,
    organization VARCHAR(255),
    owner_id BIGINT,
    last_heartbeat TIMESTAMP,
    created_at TIMESTAMP NOT NULL,
    updated_at TIMESTAMP NOT NULL
);

-- 节点读模型索引
CREATE INDEX IF NOT EXISTS idx_node_read_model_status ON node_read_model (status);
CREATE INDEX IF NOT EXISTS idx_node_read_model_load ON node_read_model (load_percentage);
CREATE INDEX IF NOT EXISTS idx_node_read_model_organization ON node_read_model (organization);
CREATE INDEX IF NOT EXISTS idx_node_read_model_owner ON node_read_model (owner_id);

-- 场景读模型表
CREATE TABLE IF NOT EXISTS scene_read_model (
    scene_id BIGINT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    version VARCHAR(50),
    industry VARCHAR(50),
    is_official BOOLEAN NOT NULL DEFAULT FALSE,
    is_banned BOOLEAN NOT NULL DEFAULT FALSE,
    total_deployments INTEGER NOT NULL DEFAULT 0,
    active_deployments INTEGER NOT NULL DEFAULT 0,
    total_users INTEGER NOT NULL DEFAULT 0,
    rating DECIMAL(3,2) DEFAULT 0.0,
    created_at TIMESTAMP NOT NULL,
    updated_at TIMESTAMP NOT NULL
);

-- 场景读模型索引
CREATE INDEX IF NOT EXISTS idx_scene_read_model_name ON scene_read_model (name);
CREATE INDEX IF NOT EXISTS idx_scene_read_model_industry ON scene_read_model (industry);
CREATE INDEX IF NOT EXISTS idx_scene_read_model_official ON scene_read_model (is_official);
CREATE INDEX IF NOT EXISTS idx_scene_read_model_rating ON scene_read_model (rating);

-- 投影状态表（用于跟踪投影进度）
CREATE TABLE IF NOT EXISTS projection_state (
    projection_name VARCHAR(100) PRIMARY KEY,
    last_processed_event_id VARCHAR(255),
    last_processed_position BIGINT NOT NULL DEFAULT 0,
    status VARCHAR(20) NOT NULL DEFAULT 'RUNNING',
    error_message TEXT,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 初始化投影状态
INSERT INTO projection_state (projection_name, last_processed_position) VALUES 
    ('user_read_model', 0),
    ('node_read_model', 0),
    ('scene_read_model', 0),
    ('user_statistics', 0)
ON CONFLICT (projection_name) DO NOTHING;

-- 事件处理锁表（防止重复处理）
CREATE TABLE IF NOT EXISTS event_processing_locks (
    lock_name VARCHAR(100) PRIMARY KEY,
    locked_by VARCHAR(255) NOT NULL,
    locked_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NOT NULL
);

-- 定期清理过期锁的函数
CREATE OR REPLACE FUNCTION cleanup_expired_locks()
RETURNS void AS $$
BEGIN
    DELETE FROM event_processing_locks WHERE expires_at < CURRENT_TIMESTAMP;
END;
$$ LANGUAGE plpgsql;

-- 创建定期清理任务（需要pg_cron扩展）
-- SELECT cron.schedule('cleanup-locks', '*/5 * * * *', 'SELECT cleanup_expired_locks();');

-- 事件存储分区（按月分区，提高查询性能）
-- CREATE TABLE event_store_y2024m01 PARTITION OF event_store
-- FOR VALUES FROM ('2024-01-01') TO ('2024-02-01');

-- 添加注释
COMMENT ON TABLE event_store IS '领域事件存储表，用于事件溯源';
COMMENT ON TABLE aggregate_snapshots IS '聚合快照表，用于优化事件重放性能';
COMMENT ON TABLE saga_state IS 'Saga状态表，用于管理长时间运行的业务流程';
COMMENT ON TABLE user_read_model IS '用户读模型表，CQRS查询端优化';
COMMENT ON TABLE projection_state IS '投影状态表，跟踪事件投影进度';

-- 创建更新时间戳的触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 为需要的表添加更新时间戳触发器
CREATE TRIGGER update_user_read_model_updated_at
    BEFORE UPDATE ON user_read_model
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_node_read_model_updated_at
    BEFORE UPDATE ON node_read_model
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_scene_read_model_updated_at
    BEFORE UPDATE ON scene_read_model
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_saga_state_updated_at
    BEFORE UPDATE ON saga_state
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
