package com.nnepoch.rendermanager.api.dto.response.base;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * <AUTHOR> 2024/6/14
 */
@Data
@AllArgsConstructor
public class ErrorResDTO {
    @Schema(description = "错误码")
    private String code;

    @Schema(description = "请求信息")
    private String req;

    @Schema(description = "traceId")
    private String traceId;

    @Schema(description = "错误信息")
    private String message;

    @Schema(description = "错误原因")
    private Object cause;

    public ErrorResDTO(String code, String req, String traceId, String message) {
        this.code = code;
        this.req = req;
        this.traceId = traceId;
        this.message = message;
    }
}
