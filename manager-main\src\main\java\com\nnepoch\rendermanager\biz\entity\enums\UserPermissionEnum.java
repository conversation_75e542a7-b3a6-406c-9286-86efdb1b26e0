package com.nnepoch.rendermanager.biz.entity.enums;

import org.springframework.security.core.GrantedAuthority;

/**
 * <AUTHOR> 2024/8/5
 */
public enum UserPermissionEnum implements GrantedAuthority {
    // 登录用户
    USER_READ_ME("user.read_me"),
    USER_UPDATE_ME("user.update_me"),

    // 用户管理
    USER_READ("user.read"),
    USER_CREATE("user.create"),
    USER_UPDATE("user.update"),
    USER_DELETE("user.delete"),
    USER_RESET_PASSWORD("user.reset_password"),
    USER_BAN("user.ban"),
    USER_PERMIT("user.permit"),

    // 节点管理
    NODE_READ("node.read"),
    NODE_CREATE("node.create"),
    NODE_UPDATE("node.update"),
    NODE_DELETE("node.delete"),

    // 场景管理
    SCENE_READ("scene.read"),
    SCENE_CREATE("scene.create"),
    SCENE_UPDATE("scene.update"),
    SCENE_DELETE("scene.delete"),
    SCENE_START("scene.start"),
    SCENE_STOP("scene.stop"),
    SCENE_RESTART("scene.restart"),

    // 体验管理
    SCENE_EXPERIENCE_READ("scene_experience.read"),
    SCENE_EXPERIENCE_CREATE("scene_experience.create"),
    SCENE_EXPERIENCE_UPDATE("scene_experience.update"),
    SCENE_EXPERIENCE_DELETE("scene_experience.delete"),
    ;

    private final String permission;

    UserPermissionEnum(String permission) {
        this.permission = permission;
    }

    public static UserPermissionEnum fromPermission(String permission) {
        for (UserPermissionEnum userPermission : UserPermissionEnum.values()) {
            if (userPermission.getAuthority().equals(permission)) {
                return userPermission;
            }
        }
        throw new IllegalArgumentException("Unknown permission: " + permission);
    }

    @Override
    public String getAuthority() {
        return permission;
    }
}
