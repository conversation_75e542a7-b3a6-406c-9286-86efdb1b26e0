package com.nnepoch.rendermanager.biz.service.impl;

import com.aliyun.dingtalkcontact_1_0.models.GetUserHeaders;
import com.aliyun.dingtalkcontact_1_0.models.GetUserResponseBody;
import com.aliyun.dingtalkoauth2_1_0.models.GetUserTokenRequest;
import com.aliyun.dingtalkoauth2_1_0.models.GetUserTokenResponse;
import com.aliyun.teaopenapi.models.Config;
import com.nnepoch.rendermanager.api.dto.request.UserReqDTO;
import com.nnepoch.rendermanager.api.dto.response.SigninResDTO;
import com.nnepoch.rendermanager.api.dto.response.UserResDTO;
import com.nnepoch.rendermanager.api.enums.UserRoleEnum;
import com.nnepoch.rendermanager.biz.entity.UserEntity;
import com.nnepoch.rendermanager.biz.service.DingtalkService;
import com.nnepoch.rendermanager.biz.service.UserService;
import com.nnepoch.rendermanager.config.properties.AppDingtalkProperties;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> Che 2024/8/21
 */
@Service
@RequiredArgsConstructor
public class DingtalkServiceImpl implements DingtalkService {
    private final AppDingtalkProperties appDingtalkProperties;
    private final UserService userService;
    private final JwtServiceImpl jwtService;

    public static com.aliyun.dingtalkoauth2_1_0.Client authClient() throws Exception {
        Config config = new Config();
        config.protocol = "https";
        config.regionId = "central";
        return new com.aliyun.dingtalkoauth2_1_0.Client(config);
    }

    // https://login.dingtalk.com/oauth2/auth?redirect_uri=http%3A%2F%2F127.0.0.1%3A8080%2Fdingtalk%2Fauth&response_type=code&client_id=dingrdzgkx3xriswefze&scope=openid&state=dddd&prompt=consent
    public SigninResDTO auth(String authCode) throws Exception {
        com.aliyun.dingtalkoauth2_1_0.Client client = authClient();

        GetUserTokenRequest getUserTokenRequest = new GetUserTokenRequest()
            .setClientId(appDingtalkProperties.getClientId())
            .setClientSecret(appDingtalkProperties.getClientSecret())
            .setCode(authCode)
            .setGrantType("authorization_code");

        GetUserTokenResponse getUserTokenResponse = client.getUserToken(getUserTokenRequest);

        String accessToken = getUserTokenResponse.getBody().getAccessToken();

        GetUserResponseBody dingtalkUser = getUserInfo(accessToken);
        UserResDTO user = userService.getUserByMobile(dingtalkUser.getMobile(), UserResDTO.class);
        if (user == null) {
            UserReqDTO toInsertUser = new UserReqDTO();
            toInsertUser.setMobile(dingtalkUser.getMobile());
            toInsertUser.setUsername(dingtalkUser.getNick());
            toInsertUser.setEmail(dingtalkUser.getEmail());
            toInsertUser.setRole(UserRoleEnum.MEMBER);
            toInsertUser.setRemark("钉钉用户");
            user = userService.addUser(toInsertUser);
        }

        UserEntity userEntity = new UserEntity();
        userEntity.setId(user.getId());
        userEntity.setMobile(user.getMobile());
        userEntity.setRole(user.getRole());

        SigninResDTO authRes = SigninResDTO.from(user);
        authRes.setAccessToken(jwtService.generateJWT(userEntity));

        userService.doAfterSignin(userEntity);

        return authRes;
    }

    public static com.aliyun.dingtalkcontact_1_0.Client contactClient() throws Exception {
        Config config = new Config();
        config.protocol = "https";
        config.regionId = "central";
        return new com.aliyun.dingtalkcontact_1_0.Client(config);
    }

    private GetUserResponseBody getUserInfo(String accessToken) throws Exception {
        com.aliyun.dingtalkcontact_1_0.Client client = contactClient();
        GetUserHeaders getUserHeaders = new GetUserHeaders();
        getUserHeaders.xAcsDingtalkAccessToken = accessToken;
        return client.getUserWithOptions("me", getUserHeaders, new com.aliyun.teautil.models.RuntimeOptions()).getBody();
    }
}
