package com.nnepoch.rendermanager.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.nnepoch.rendermanager.api.dto.request.NodeQueryDTO;
import com.nnepoch.rendermanager.api.dto.request.NodeReqDTO;
import com.nnepoch.rendermanager.api.dto.response.NodeResDTO;
import com.nnepoch.rendermanager.api.dto.response.base.PageResDTO;
import com.nnepoch.rendermanager.biz.dto.mq.NodeInfoMQDTO;
import com.nnepoch.rendermanager.biz.entity.NodeEntity;

/**
 * <AUTHOR> <PERSON> 2024/6/17
 */
public interface NodeService extends IService<NodeEntity> {
    PageResDTO<NodeResDTO> getNodeList(NodeQueryDTO queryDTO);

    NodeResDTO getNodeInfo(Long id);

    NodeResDTO getNodeBySn(String sn);

    NodeResDTO syncNode(NodeInfoMQDTO reqDTO);

    NodeResDTO updateNode(Long id, NodeReqDTO reqDTO);

    // 选择最优节点
    NodeResDTO getBestNode();
}
