spring:
  datasource:
    url: jdbc:postgresql://${DATABASE_HOST:dtc.io}:${DATABASE_PORT:30001}/${DATABASE_NAME:render_manager_dev}?useSSL=false&useUnicode=true&characterEncoding=utf-8&serverTimezone=UTC
    username: ${DATABASE_USERNAME:epoch}
    password: ${DATABASE_PASSWORD:P@88w0rd}
    driver-class-name: org.postgresql.Driver
    hikari:
      auto-commit: true
      connection-test-query: SELECT 1
      pool-name: HikariCP
      maximum-pool-size: 20
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
      minimum-idle: 10
  data:
    redis:
      host: ${REDIS_HOST:dtc.io}
      port: ${REDIS_PORT:30002}
      database: 0
      timeout: 10000
      password: ${REDIS_PASSWORD:tV0zD6zY3kI0fL4w}

decorator:
  datasource:
    p6spy:
      enable-logging: true
      multiline: true
      logging: file
      log-file: logs/spy.log