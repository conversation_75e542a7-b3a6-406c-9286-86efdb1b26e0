package com.nnepoch.rendermanager.infrastructure.event.handler;

import com.nnepoch.rendermanager.domain.user.event.UserCreatedEvent;
import com.nnepoch.rendermanager.domain.user.event.UserLoginEvent;
import com.nnepoch.rendermanager.domain.user.event.UserRoleChangedEvent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * 用户领域事件处理器
 * <AUTHOR> Migration
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class UserEventHandler {
    
    /**
     * 处理用户创建事件
     */
    @EventListener
    @Async
    public void handleUserCreated(UserCreatedEvent event) {
        log.info("User created: {} with role: {}", event.getUsername(), event.getRole());
        
        // 可以在这里执行:
        // 1. 发送欢迎邮件
        // 2. 初始化用户权限
        // 3. 记录审计日志
        // 4. 发送通知给管理员
        
        // 示例: 发送欢迎邮件
        sendWelcomeEmail(event.getUsername());
        
        // 示例: 记录审计日志
        recordAuditLog("USER_CREATED", event.getUserId().getValue(), event.getUsername());
    }
    
    /**
     * 处理用户登录事件
     */
    @EventListener
    @Async
    public void handleUserLogin(UserLoginEvent event) {
        log.info("User logged in: {}", event.getUsername());
        
        // 可以在这里执行:
        // 1. 更新登录统计
        // 2. 检查异常登录
        // 3. 记录登录日志
        
        recordLoginLog(event.getUserId().getValue(), event.getUsername());
    }
    
    /**
     * 处理用户角色变更事件
     */
    @EventListener
    @Async
    public void handleUserRoleChanged(UserRoleChangedEvent event) {
        log.info("User role changed: {} from {} to {}", 
            event.getUserId(), event.getOldRole(), event.getNewRole());
        
        // 可以在这里执行:
        // 1. 更新权限缓存
        // 2. 发送角色变更通知
        // 3. 记录权限变更日志
        
        updatePermissionCache(event.getUserId().getValue(), event.getNewRole());
        recordAuditLog("ROLE_CHANGED", event.getUserId().getValue(), 
            String.format("%s -> %s", event.getOldRole(), event.getNewRole()));
    }
    
    private void sendWelcomeEmail(String username) {
        // 发送欢迎邮件的实现
        log.debug("Sending welcome email to: {}", username);
    }
    
    private void recordAuditLog(String action, Long userId, String details) {
        // 记录审计日志的实现
        log.debug("Audit log: {} - User: {} - Details: {}", action, userId, details);
    }
    
    private void recordLoginLog(Long userId, String username) {
        // 记录登录日志的实现
        log.debug("Login log: User {} (ID: {}) logged in", username, userId);
    }
    
    private void updatePermissionCache(Long userId, Object newRole) {
        // 更新权限缓存的实现
        log.debug("Updating permission cache for user: {} with role: {}", userId, newRole);
    }
}
