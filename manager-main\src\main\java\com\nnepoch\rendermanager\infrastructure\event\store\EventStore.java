package com.nnepoch.rendermanager.infrastructure.event.store;

import com.nnepoch.rendermanager.domain.shared.event.DomainEvent;
import com.nnepoch.rendermanager.domain.shared.valueobject.EntityId;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 事件存储接口
 * <AUTHOR> Architect
 */
public interface EventStore {
    
    /**
     * 保存事件
     */
    void saveEvent(DomainEvent event, EntityId aggregateId, String aggregateType, Long version);
    
    /**
     * 批量保存事件
     */
    void saveEvents(List<DomainEvent> events, EntityId aggregateId, String aggregateType, Long version);
    
    /**
     * 获取聚合的所有事件
     */
    List<StoredEvent> getEventsForAggregate(EntityId aggregateId, String aggregateType);
    
    /**
     * 获取聚合指定版本后的事件
     */
    List<StoredEvent> getEventsForAggregateAfterVersion(EntityId aggregateId, String aggregateType, Long version);
    
    /**
     * 获取指定时间范围的事件
     */
    List<StoredEvent> getEventsBetween(LocalDateTime start, LocalDateTime end);
    
    /**
     * 获取指定类型的事件
     */
    List<StoredEvent> getEventsByType(String eventType);
    
    /**
     * 获取最新的事件版本号
     */
    Optional<Long> getLatestVersion(EntityId aggregateId, String aggregateType);
    
    /**
     * 创建快照
     */
    void saveSnapshot(EntityId aggregateId, String aggregateType, Object snapshot, Long version);
    
    /**
     * 获取最新快照
     */
    Optional<StoredSnapshot> getLatestSnapshot(EntityId aggregateId, String aggregateType);
}
