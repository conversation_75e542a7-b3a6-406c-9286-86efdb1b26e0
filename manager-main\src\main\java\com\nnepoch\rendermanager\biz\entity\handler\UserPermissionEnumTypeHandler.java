package com.nnepoch.rendermanager.biz.entity.handler;

import com.nnepoch.rendermanager.biz.entity.enums.UserPermissionEnum;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * <AUTHOR> <PERSON> 2024/8/5
 */
public class UserPermissionEnumTypeHandler extends BaseTypeHandler<UserPermissionEnum> {
    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, UserPermissionEnum parameter, JdbcType jdbcType) throws SQLException {
        ps.setString(i, parameter.getAuthority());
    }

    @Override
    public UserPermissionEnum getNullableResult(ResultSet rs, String columnName) throws SQLException {
        String authority = rs.getString(columnName);
        return UserPermissionEnum.valueOf(authority);
    }

    @Override
    public UserPermissionEnum getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        String authority = rs.getString(columnIndex);
        return UserPermissionEnum.valueOf(authority);
    }

    @Override
    public UserPermissionEnum getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        String authority = cs.getString(columnIndex);
        return UserPermissionEnum.valueOf(authority);
    }
}
