package com.nnepoch.rendermanager.biz.entity.handler;

import com.nnepoch.rendermanager.api.enums.SceneIndustryEnum;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * <AUTHOR> <PERSON> 2023/7/30
 */
public class SceneIndustryEnumTypeHandler extends BaseTypeHandler<SceneIndustryEnum> {
    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, SceneIndustryEnum parameter, JdbcType jdbcType) throws SQLException {
        ps.setInt(i, parameter.getCode());
    }

    @Override
    public SceneIndustryEnum getNullableResult(ResultSet rs, String columnName) throws SQLException {
        Integer code = rs.getInt(columnName);
        return SceneIndustryEnum.fromCode(code);
    }

    @Override
    public SceneIndustryEnum getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        Integer code = rs.getInt(columnIndex);
        return SceneIndustryEnum.fromCode(code);
    }

    @Override
    public SceneIndustryEnum getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        Integer code = cs.getInt(columnIndex);
        return SceneIndustryEnum.fromCode(code);
    }
}
