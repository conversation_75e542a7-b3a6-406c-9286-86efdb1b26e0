package com.nnepoch.rendermanager.domain.scene.valueobject;

import com.nnepoch.rendermanager.api.enums.SceneIndustryEnum;
import lombok.EqualsAndHashCode;
import lombok.Getter;

/**
 * 场景配置值对象
 * <AUTHOR> Migration
 */
@Getter
@EqualsAndHashCode
public class SceneConfig {
    private final String name;
    private final String version;
    private final String description;
    private final SceneIndustryEnum industry;
    private final String link;
    private final boolean isOfficial;
    private final String intro;
    
    public SceneConfig(String name, String version, String description, 
                      SceneIndustryEnum industry, String link, 
                      boolean isOfficial, String intro) {
        if (name == null || name.trim().isEmpty()) {
            throw new IllegalArgumentException("Scene name cannot be empty");
        }
        if (version == null || version.trim().isEmpty()) {
            throw new IllegalArgumentException("Scene version cannot be empty");
        }
        
        this.name = name.trim();
        this.version = version.trim();
        this.description = description;
        this.industry = industry;
        this.link = link;
        this.isOfficial = isOfficial;
        this.intro = intro;
    }
    
    /**
     * 检查是否为官方场景
     */
    public boolean isOfficialScene() {
        return isOfficial;
    }
    
    /**
     * 获取场景的完整标识
     */
    public String getFullIdentifier() {
        return String.format("%s@%s", name, version);
    }
}
