package com.nnepoch.rendermanager.api.feign.fallback;

import com.nnepoch.rendermanager.api.feign.WebFeignService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> 2024/6/13
 */
@Component
@Slf4j
public class WebFeignFallbackFactory implements FallbackFactory<WebFeignService> {
    @Override
    public WebFeignService create(Throwable cause) {
        log.error("yield fallback: {}", cause.getMessage(), cause);
        return new WebFeignService() {
            @Override
            public String getInfo() {
                return "WebFeignService getInfo fallback";
            }
        };
    }
}
