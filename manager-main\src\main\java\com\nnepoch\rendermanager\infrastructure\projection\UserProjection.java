package com.nnepoch.rendermanager.infrastructure.projection;

import com.nnepoch.rendermanager.domain.user.event.UserCreatedEvent;
import com.nnepoch.rendermanager.domain.user.event.UserLoginEvent;
import com.nnepoch.rendermanager.domain.user.event.UserRoleChangedEvent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

/**
 * 用户读模型投影
 * <AUTHOR> Architect
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class UserProjection {
    
    private final JdbcTemplate jdbcTemplate;
    
    @ProjectionHandler(events = UserCreatedEvent.class, projection = "user_read_model")
    @Transactional
    public void handle(UserCreatedEvent event) {
        log.info("Projecting UserCreatedEvent: {}", event.getUserId());
        
        String sql = """
            INSERT INTO user_read_model (
                user_id, username, role, is_banned, created_at, updated_at,
                login_count, last_login_at, status
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """;
        
        jdbcTemplate.update(sql,
            event.getUserId().getValue(),
            event.getUsername(),
            event.getRole().name(),
            false,
            event.getOccurredOn(),
            event.getOccurredOn(),
            0,
            null,
            "ACTIVE"
        );
        
        // 更新统计信息
        updateUserStatistics("INCREMENT_TOTAL");
        updateUserStatistics("INCREMENT_" + event.getRole().name());
    }
    
    @ProjectionHandler(events = UserLoginEvent.class, projection = "user_read_model")
    @Transactional
    public void handle(UserLoginEvent event) {
        log.info("Projecting UserLoginEvent: {}", event.getUserId());
        
        String sql = """
            UPDATE user_read_model 
            SET login_count = login_count + 1, 
                last_login_at = ?, 
                updated_at = ?
            WHERE user_id = ?
            """;
        
        jdbcTemplate.update(sql,
            event.getOccurredOn(),
            LocalDateTime.now(),
            event.getUserId().getValue()
        );
        
        // 记录登录历史
        recordLoginHistory(event);
    }
    
    @ProjectionHandler(events = UserRoleChangedEvent.class, projection = "user_read_model")
    @Transactional
    public void handle(UserRoleChangedEvent event) {
        log.info("Projecting UserRoleChangedEvent: {}", event.getUserId());
        
        String sql = """
            UPDATE user_read_model 
            SET role = ?, updated_at = ?
            WHERE user_id = ?
            """;
        
        jdbcTemplate.update(sql,
            event.getNewRole().name(),
            LocalDateTime.now(),
            event.getUserId().getValue()
        );
        
        // 更新角色统计
        updateUserStatistics("DECREMENT_" + event.getOldRole().name());
        updateUserStatistics("INCREMENT_" + event.getNewRole().name());
    }
    
    private void updateUserStatistics(String operation) {
        String sql = """
            INSERT INTO user_statistics (stat_key, stat_value, updated_at)
            VALUES (?, 1, ?)
            ON CONFLICT (stat_key) DO UPDATE SET
                stat_value = CASE 
                    WHEN ? LIKE 'INCREMENT%' THEN user_statistics.stat_value + 1
                    WHEN ? LIKE 'DECREMENT%' THEN GREATEST(user_statistics.stat_value - 1, 0)
                    ELSE user_statistics.stat_value
                END,
                updated_at = ?
            """;
        
        LocalDateTime now = LocalDateTime.now();
        jdbcTemplate.update(sql, operation, now, operation, operation, now);
    }
    
    private void recordLoginHistory(UserLoginEvent event) {
        String sql = """
            INSERT INTO user_login_history (user_id, login_at, ip_address, user_agent)
            VALUES (?, ?, ?, ?)
            """;
        
        jdbcTemplate.update(sql,
            event.getUserId().getValue(),
            event.getOccurredOn(),
            "unknown", // 从事件元数据中获取
            "unknown"  // 从事件元数据中获取
        );
    }
}
