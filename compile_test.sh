#!/bin/bash

echo "Testing DDD compilation..."

# 检查Java版本
echo "Java version:"
java -version

echo ""
echo "Compiling DDD classes..."

# 编译关键的DDD类
javac -cp "manager-main/target/classes:manager-main/target/dependency/*" \
  manager-main/src/main/java/com/nnepoch/rendermanager/domain/shared/valueobject/EntityId.java \
  manager-main/src/main/java/com/nnepoch/rendermanager/domain/shared/valueobject/UserId.java \
  manager-main/src/main/java/com/nnepoch/rendermanager/domain/user/valueobject/UserProfile.java \
  manager-main/src/main/java/com/nnepoch/rendermanager/domain/user/aggregate/User.java

if [ $? -eq 0 ]; then
    echo "✅ DDD classes compiled successfully!"
else
    echo "❌ Compilation failed!"
    exit 1
fi

echo "✅ All tests passed!"
