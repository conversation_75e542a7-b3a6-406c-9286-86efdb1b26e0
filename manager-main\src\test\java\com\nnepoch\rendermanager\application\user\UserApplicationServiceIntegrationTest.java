package com.nnepoch.rendermanager.application.user;

import com.nnepoch.rendermanager.api.enums.UserRoleEnum;
import com.nnepoch.rendermanager.application.user.command.CreateUserCommand;
import com.nnepoch.rendermanager.application.user.dto.UserDTO;
import com.nnepoch.rendermanager.application.user.service.UserApplicationService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

/**
 * 用户应用服务集成测试
 * 验证DDD应用层的正确性
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
class UserApplicationServiceIntegrationTest {
    
    @Autowired
    private UserApplicationService userApplicationService;
    
    @Test
    void should_create_user_successfully() {
        // Given
        CreateUserCommand command = new CreateUserCommand();
        command.setUsername("testuser" + System.currentTimeMillis());
        command.setEmail("test" + System.currentTimeMillis() + "@example.com");
        command.setMobile("138" + String.format("%08d", System.currentTimeMillis() % 100000000));
        command.setRole(UserRoleEnum.MEMBER);
        
        // When
        UserDTO result = userApplicationService.createUser(command);
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result.getUsername()).isEqualTo(command.getUsername());
        assertThat(result.getEmail()).isEqualTo(command.getEmail());
        assertThat(result.getMobile()).isEqualTo(command.getMobile());
        assertThat(result.getRole()).isEqualTo(command.getRole());
        assertThat(result.isBanned()).isFalse();
        assertThat(result.getRawPassword()).isNotNull(); // 应该返回生成的密码
    }
    
    @Test
    void should_throw_exception_when_username_already_exists() {
        // Given
        String duplicateUsername = "duplicate" + System.currentTimeMillis();
        
        CreateUserCommand command1 = new CreateUserCommand();
        command1.setUsername(duplicateUsername);
        command1.setEmail("test1" + System.currentTimeMillis() + "@example.com");
        command1.setMobile("138" + String.format("%08d", System.currentTimeMillis() % 100000000));
        command1.setRole(UserRoleEnum.MEMBER);
        
        CreateUserCommand command2 = new CreateUserCommand();
        command2.setUsername(duplicateUsername); // 重复的用户名
        command2.setEmail("test2" + System.currentTimeMillis() + "@example.com");
        command2.setMobile("139" + String.format("%08d", System.currentTimeMillis() % 100000000));
        command2.setRole(UserRoleEnum.MEMBER);
        
        // When
        userApplicationService.createUser(command1);
        
        // Then
        assertThatThrownBy(() -> userApplicationService.createUser(command2))
            .isInstanceOf(IllegalArgumentException.class)
            .hasMessage("用户名已存在");
    }
    
    @Test
    void should_login_successfully_with_correct_credentials() {
        // Given
        CreateUserCommand command = new CreateUserCommand();
        command.setUsername("logintest" + System.currentTimeMillis());
        command.setEmail("logintest" + System.currentTimeMillis() + "@example.com");
        command.setMobile("138" + String.format("%08d", System.currentTimeMillis() % 100000000));
        command.setRole(UserRoleEnum.MEMBER);
        command.setPassword("testpassword");
        
        UserDTO createdUser = userApplicationService.createUser(command);
        
        // When
        UserDTO loginResult = userApplicationService.login(command.getUsername(), "testpassword");
        
        // Then
        assertThat(loginResult).isNotNull();
        assertThat(loginResult.getId()).isEqualTo(createdUser.getId());
        assertThat(loginResult.getLoginAt()).isNotNull();
    }
    
    @Test
    void should_throw_exception_when_login_with_wrong_password() {
        // Given
        CreateUserCommand command = new CreateUserCommand();
        command.setUsername("wrongpasstest" + System.currentTimeMillis());
        command.setEmail("wrongpasstest" + System.currentTimeMillis() + "@example.com");
        command.setMobile("138" + String.format("%08d", System.currentTimeMillis() % 100000000));
        command.setRole(UserRoleEnum.MEMBER);
        command.setPassword("correctpassword");
        
        userApplicationService.createUser(command);
        
        // When & Then
        assertThatThrownBy(() -> userApplicationService.login(command.getUsername(), "wrongpassword"))
            .isInstanceOf(IllegalArgumentException.class)
            .hasMessage("密码错误");
    }
    
    @Test
    void should_ban_and_unban_user_successfully() {
        // Given
        CreateUserCommand command = new CreateUserCommand();
        command.setUsername("bantest" + System.currentTimeMillis());
        command.setEmail("bantest" + System.currentTimeMillis() + "@example.com");
        command.setMobile("138" + String.format("%08d", System.currentTimeMillis() % 100000000));
        command.setRole(UserRoleEnum.MEMBER);
        
        UserDTO createdUser = userApplicationService.createUser(command);
        
        // When - Ban user
        userApplicationService.banUser(createdUser.getId());
        
        // Then
        UserDTO bannedUser = userApplicationService.getUserById(createdUser.getId()).orElseThrow();
        assertThat(bannedUser.isBanned()).isTrue();
        
        // When - Unban user
        userApplicationService.unbanUser(createdUser.getId());
        
        // Then
        UserDTO unbannedUser = userApplicationService.getUserById(createdUser.getId()).orElseThrow();
        assertThat(unbannedUser.isBanned()).isFalse();
    }
}
