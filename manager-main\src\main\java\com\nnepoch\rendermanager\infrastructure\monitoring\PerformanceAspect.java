package com.nnepoch.rendermanager.infrastructure.monitoring;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.Instant;

/**
 * DDD性能监控切面
 * 自动收集聚合、应用服务、仓储等关键组件的性能指标
 * <AUTHOR> Architect
 */
@Aspect
@Component
@RequiredArgsConstructor
@Slf4j
public class PerformanceAspect {
    
    private final PerformanceMetrics performanceMetrics;
    
    /**
     * 监控聚合根操作
     */
    @Around("execution(* com.nnepoch.rendermanager.domain..aggregate.*.*(..)) && " +
            "!execution(* *.get*(..))")
    public Object monitorAggregateOperations(ProceedingJoinPoint joinPoint) throws Throwable {
        String aggregateType = extractAggregateType(joinPoint);
        String operation = joinPoint.getSignature().getName();
        
        Instant start = Instant.now();
        try {
            Object result = joinPoint.proceed();
            
            Duration duration = Duration.between(start, Instant.now());
            performanceMetrics.recordAggregateOperation(aggregateType, operation, duration);
            
            // 记录成功的业务操作
            performanceMetrics.recordBusinessMetric("aggregate.operation.success",
                "type", aggregateType, "operation", operation);
            
            return result;
            
        } catch (Exception e) {
            Duration duration = Duration.between(start, Instant.now());
            performanceMetrics.recordAggregateOperation(aggregateType, operation + ".error", duration);
            
            // 记录失败的业务操作
            performanceMetrics.recordBusinessMetric("aggregate.operation.failure",
                "type", aggregateType, "operation", operation, "error", e.getClass().getSimpleName());
            
            throw e;
        }
    }
    
    /**
     * 监控应用服务操作
     */
    @Around("execution(* com.nnepoch.rendermanager.application..service.*.*(..))")
    public Object monitorApplicationServices(ProceedingJoinPoint joinPoint) throws Throwable {
        String serviceName = extractServiceName(joinPoint);
        String operation = joinPoint.getSignature().getName();
        
        Instant start = Instant.now();
        try {
            Object result = joinPoint.proceed();
            
            Duration duration = Duration.between(start, Instant.now());
            performanceMetrics.recordBusinessMetric("application.service.operation",
                "service", serviceName, "operation", operation, "status", "success");
            
            // 记录操作耗时
            performanceMetrics.setGaugeValue("application.service.duration",
                duration.toMillis(), "service", serviceName, "operation", operation);
            
            return result;
            
        } catch (Exception e) {
            Duration duration = Duration.between(start, Instant.now());
            performanceMetrics.recordBusinessMetric("application.service.operation",
                "service", serviceName, "operation", operation, "status", "failure",
                "error", e.getClass().getSimpleName());
            
            throw e;
        }
    }
    
    /**
     * 监控仓储操作
     */
    @Around("execution(* com.nnepoch.rendermanager..repository.*.*(..))")
    public Object monitorRepositoryOperations(ProceedingJoinPoint joinPoint) throws Throwable {
        String repositoryName = extractRepositoryName(joinPoint);
        String operation = joinPoint.getSignature().getName();
        
        Instant start = Instant.now();
        try {
            Object result = joinPoint.proceed();
            
            Duration duration = Duration.between(start, Instant.now());
            performanceMetrics.recordDatabaseOperation(repositoryName, operation, duration);
            
            return result;
            
        } catch (Exception e) {
            Duration duration = Duration.between(start, Instant.now());
            performanceMetrics.recordDatabaseOperation(repositoryName, operation + ".error", duration);
            
            throw e;
        }
    }
    
    /**
     * 监控事件处理器
     */
    @Around("@annotation(org.springframework.context.event.EventListener)")
    public Object monitorEventHandlers(ProceedingJoinPoint joinPoint) throws Throwable {
        String handlerName = extractHandlerName(joinPoint);
        Object[] args = joinPoint.getArgs();
        String eventType = args.length > 0 ? args[0].getClass().getSimpleName() : "Unknown";
        
        Instant start = Instant.now();
        boolean success = false;
        
        try {
            Object result = joinPoint.proceed();
            success = true;
            return result;
            
        } catch (Exception e) {
            log.error("Event handler failed: {} for event: {}", handlerName, eventType, e);
            throw e;
            
        } finally {
            Duration duration = Duration.between(start, Instant.now());
            performanceMetrics.recordEventProcessing(eventType, handlerName, duration, success);
        }
    }
    
    /**
     * 监控缓存操作
     */
    @Around("execution(* com.nnepoch.rendermanager.infrastructure.cache.*.*(..))")
    public Object monitorCacheOperations(ProceedingJoinPoint joinPoint) throws Throwable {
        String operation = joinPoint.getSignature().getName();
        String cacheType = extractCacheType(joinPoint);
        
        Instant start = Instant.now();
        try {
            Object result = joinPoint.proceed();
            
            // 判断是否命中缓存
            boolean hit = isCacheHit(operation, result);
            performanceMetrics.recordCacheOperation(cacheType, operation, hit);
            
            return result;
            
        } catch (Exception e) {
            performanceMetrics.recordCacheOperation(cacheType, operation + ".error", false);
            throw e;
        }
    }
    
    /**
     * 监控控制器操作
     */
    @Around("execution(* com.nnepoch.rendermanager..controller.*.*(..))")
    public Object monitorControllerOperations(ProceedingJoinPoint joinPoint) throws Throwable {
        String endpoint = extractEndpoint(joinPoint);
        String method = "POST"; // 简化实现，实际应该从注解中获取
        
        Instant start = Instant.now();
        int statusCode = 200;
        
        try {
            Object result = joinPoint.proceed();
            return result;
            
        } catch (IllegalArgumentException e) {
            statusCode = 400;
            throw e;
        } catch (IllegalStateException e) {
            statusCode = 409;
            throw e;
        } catch (Exception e) {
            statusCode = 500;
            throw e;
            
        } finally {
            Duration duration = Duration.between(start, Instant.now());
            performanceMetrics.recordApiCall(endpoint, method, statusCode, duration);
        }
    }
    
    private String extractAggregateType(ProceedingJoinPoint joinPoint) {
        String className = joinPoint.getTarget().getClass().getSimpleName();
        return className.replace("Impl", "").replace("Repository", "");
    }
    
    private String extractServiceName(ProceedingJoinPoint joinPoint) {
        String className = joinPoint.getTarget().getClass().getSimpleName();
        return className.replace("ApplicationService", "").replace("Service", "");
    }
    
    private String extractRepositoryName(ProceedingJoinPoint joinPoint) {
        String className = joinPoint.getTarget().getClass().getSimpleName();
        return className.replace("RepositoryImpl", "").replace("Repository", "");
    }
    
    private String extractHandlerName(ProceedingJoinPoint joinPoint) {
        return joinPoint.getTarget().getClass().getSimpleName();
    }
    
    private String extractCacheType(ProceedingJoinPoint joinPoint) {
        String className = joinPoint.getTarget().getClass().getSimpleName();
        return className.replace("CacheManager", "").replace("Cache", "");
    }
    
    private String extractEndpoint(ProceedingJoinPoint joinPoint) {
        String className = joinPoint.getTarget().getClass().getSimpleName();
        String methodName = joinPoint.getSignature().getName();
        return className.replace("Controller", "").toLowerCase() + "." + methodName;
    }
    
    private boolean isCacheHit(String operation, Object result) {
        // 根据操作类型和结果判断是否命中缓存
        if (operation.startsWith("get") || operation.startsWith("find")) {
            if (result instanceof java.util.Optional) {
                return ((java.util.Optional<?>) result).isPresent();
            }
            return result != null;
        }
        return false; // 写操作不算命中
    }
}
