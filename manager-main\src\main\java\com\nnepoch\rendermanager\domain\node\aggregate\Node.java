package com.nnepoch.rendermanager.domain.node.aggregate;

import com.nnepoch.rendermanager.api.enums.NodeStatusEnum;
import com.nnepoch.rendermanager.domain.node.event.NodeOnlineEvent;
import com.nnepoch.rendermanager.domain.node.event.NodeOfflineEvent;
import com.nnepoch.rendermanager.domain.node.event.NodeRegisteredEvent;
import com.nnepoch.rendermanager.domain.node.valueobject.NodeAddress;
import com.nnepoch.rendermanager.domain.node.valueobject.NodeSpec;
import com.nnepoch.rendermanager.domain.shared.aggregate.AggregateRoot;
import com.nnepoch.rendermanager.domain.shared.valueobject.NodeId;
import com.nnepoch.rendermanager.domain.shared.valueobject.UserId;
import lombok.Getter;

import java.time.LocalDateTime;

/**
 * 节点聚合根
 * <AUTHOR> Migration
 */
@Getter
public class Node extends AggregateRoot<NodeId> {
    private NodeAddress address;
    private NodeSpec spec;
    private NodeStatusEnum status;
    private boolean enableLogCollection;
    private int sceneCount;
    private int sceneRunningCount;
    private int sceneSeatsTaken;
    private UserId ownedBy;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    
    // 私有构造函数
    private Node(NodeId id, NodeAddress address, NodeSpec spec, UserId ownedBy) {
        super(id);
        this.address = address;
        this.spec = spec;
        this.status = NodeStatusEnum.OFFLINE;
        this.enableLogCollection = false;
        this.sceneCount = 0;
        this.sceneRunningCount = 0;
        this.sceneSeatsTaken = 0;
        this.ownedBy = ownedBy;
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * 注册新节点
     */
    public static Node register(NodeId id, NodeAddress address, NodeSpec spec, UserId ownedBy) {
        Node node = new Node(id, address, spec, ownedBy);
        node.addDomainEvent(new NodeRegisteredEvent(id, address.getSn(), address.getHostname()));
        return node;
    }
    
    /**
     * 节点上线
     */
    public void goOnline() {
        if (this.status == NodeStatusEnum.ONLINE) {
            return; // 已经在线，无需重复操作
        }
        
        this.status = NodeStatusEnum.ONLINE;
        this.updatedAt = LocalDateTime.now();
        addDomainEvent(new NodeOnlineEvent(this.id, address.getSn()));
    }
    
    /**
     * 节点下线
     */
    public void goOffline() {
        if (this.status == NodeStatusEnum.OFFLINE) {
            return; // 已经离线，无需重复操作
        }
        
        this.status = NodeStatusEnum.OFFLINE;
        this.updatedAt = LocalDateTime.now();
        addDomainEvent(new NodeOfflineEvent(this.id, address.getSn()));
    }
    
    /**
     * 启动场景实例
     */
    public void startSceneInstance() {
        if (!canStartNewScene()) {
            throw new IllegalStateException("Node has reached maximum capacity");
        }
        
        this.sceneRunningCount++;
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * 停止场景实例
     */
    public void stopSceneInstance() {
        if (this.sceneRunningCount <= 0) {
            throw new IllegalStateException("No running scenes to stop");
        }
        
        this.sceneRunningCount--;
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * 占用座位
     */
    public void takeSeat() {
        this.sceneSeatsTaken++;
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * 释放座位
     */
    public void releaseSeat() {
        if (this.sceneSeatsTaken <= 0) {
            throw new IllegalStateException("No seats to release");
        }
        
        this.sceneSeatsTaken--;
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * 检查是否可以启动新场景
     */
    public boolean canStartNewScene() {
        return status == NodeStatusEnum.ONLINE && 
               spec.hasCapacityFor(sceneRunningCount);
    }
    
    /**
     * 获取剩余容量
     */
    public int getRemainingCapacity() {
        return spec.getRemainingCapacity(sceneRunningCount);
    }
    
    /**
     * 启用日志收集
     */
    public void enableLogCollection() {
        this.enableLogCollection = true;
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * 禁用日志收集
     */
    public void disableLogCollection() {
        this.enableLogCollection = false;
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * 更新节点规格
     */
    public void updateSpec(NodeSpec newSpec) {
        this.spec = newSpec;
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * 检查节点是否健康
     */
    public boolean isHealthy() {
        return status == NodeStatusEnum.ONLINE &&
               sceneRunningCount <= spec.getMaxSceneInstanceCount();
    }

    // Getter methods for accessing private fields
    public NodeAddress getAddress() {
        return address;
    }

    public NodeSpec getSpec() {
        return spec;
    }

    public NodeStatusEnum getStatus() {
        return status;
    }

    public boolean isEnableLogCollection() {
        return enableLogCollection;
    }

    public int getSceneCount() {
        return sceneCount;
    }

    public int getSceneRunningCount() {
        return sceneRunningCount;
    }

    public int getSceneSeatsTaken() {
        return sceneSeatsTaken;
    }

    public UserId getOwnedBy() {
        return ownedBy;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }
}
