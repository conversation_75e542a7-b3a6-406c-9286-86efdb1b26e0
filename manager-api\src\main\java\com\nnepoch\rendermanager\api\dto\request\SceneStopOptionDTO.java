package com.nnepoch.rendermanager.api.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> 2024/7/2
 */
@Data
@Schema(description = "场景关闭选项")
@NoArgsConstructor
public class SceneStopOptionDTO {
    private String type = "Stop";

    @Schema(description = "websocket session id")
    private String sessionId;

    @Schema(description = "节点id")
    private String nodeId;

    @Schema(description = "进程id")
    private Integer processId = 0;

    public SceneStopOptionDTO(String sessionId) {
        this.sessionId = sessionId;
    }

    public SceneStopOptionDTO(String sessionId, Integer processId) {
        this.sessionId = sessionId;
        this.processId = processId;
    }
}
