# 测试环境配置
spring:
  datasource:
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    driver-class-name: org.h2.Driver
    username: sa
    password: 
  
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true
    
  redis:
    host: localhost
    port: 6379
    database: 1
    
  rabbitmq:
    host: localhost
    port: 5672
    username: guest
    password: guest
    
  flyway:
    enabled: false # 测试环境禁用Flyway

# 日志配置
logging:
  level:
    com.nnepoch.rendermanager: DEBUG
    org.springframework.security: DEBUG
    
# 缓存配置
cache:
  enabled: false # 测试环境禁用缓存

# 性能监控
management:
  metrics:
    enabled: false # 测试环境禁用指标收集
