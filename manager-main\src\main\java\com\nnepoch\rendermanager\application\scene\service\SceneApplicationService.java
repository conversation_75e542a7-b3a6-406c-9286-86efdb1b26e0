package com.nnepoch.rendermanager.application.scene.service;

import com.nnepoch.rendermanager.application.scene.command.CreateSceneCommand;
import com.nnepoch.rendermanager.application.scene.command.StartSceneCommand;
import com.nnepoch.rendermanager.application.scene.dto.SceneDTO;
import com.nnepoch.rendermanager.domain.node.aggregate.Node;
import com.nnepoch.rendermanager.domain.node.repository.NodeRepository;
import com.nnepoch.rendermanager.domain.node.service.NodeDomainService;
import com.nnepoch.rendermanager.domain.scene.aggregate.Scene;
import com.nnepoch.rendermanager.domain.scene.repository.SceneRepository;
import com.nnepoch.rendermanager.domain.scene.service.SceneDomainService;
import com.nnepoch.rendermanager.domain.scene.valueobject.SceneConfig;
import com.nnepoch.rendermanager.domain.shared.valueobject.SceneId;
import com.nnepoch.rendermanager.infrastructure.messaging.DomainEventPublisher;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

/**
 * 场景应用服务
 * <AUTHOR> Migration
 */
@Service
@RequiredArgsConstructor
@Transactional
public class SceneApplicationService {
    
    private final SceneRepository sceneRepository;
    private final NodeRepository nodeRepository;
    private final SceneDomainService sceneDomainService;
    private final NodeDomainService nodeDomainService;
    private final DomainEventPublisher eventPublisher;
    
    /**
     * 创建场景
     */
    public SceneDTO createScene(CreateSceneCommand command) {
        // 1. 验证业务规则
        sceneDomainService.validateSceneCreation(command.getName());
        
        // 2. 创建场景配置
        SceneConfig config = new SceneConfig(
            command.getName(),
            command.getVersion(),
            command.getDescription(),
            command.getIndustry(),
            command.getLink(),
            command.getIsOfficial(),
            command.getIntro()
        );
        
        // 3. 创建场景聚合
        SceneId sceneId = SceneId.of(generateSceneId());
        Scene scene = Scene.create(sceneId, config, command.getThumb());
        
        // 4. 保存场景
        sceneRepository.save(scene);
        
        // 5. 发布领域事件
        scene.getDomainEvents().forEach(eventPublisher::publish);
        scene.clearDomainEvents();
        
        return SceneDTO.fromDomain(scene);
    }
    
    /**
     * 启动场景 - 跨聚合操作示例
     */
    public SceneDTO startScene(StartSceneCommand command) {
        // 1. 获取场景
        Scene scene = sceneRepository.findById(SceneId.of(command.getSceneId()))
            .orElseThrow(() -> new IllegalArgumentException("场景不存在"));
        
        // 2. 检查场景是否可用
        if (!scene.isAvailable()) {
            throw new IllegalStateException("场景已被封禁，无法启动");
        }
        
        // 3. 选择最佳节点
        Node bestNode = nodeDomainService.findBestAvailableNode()
            .orElseThrow(() -> new IllegalStateException("没有可用的节点"));
        
        // 4. 在节点上启动场景实例
        bestNode.startSceneInstance();
        
        // 5. 记录场景访问
        scene.recordView();
        
        // 6. 保存变更
        nodeRepository.save(bestNode);
        sceneRepository.save(scene);
        
        // 7. 发布事件
        publishDomainEvents(scene, bestNode);
        
        return SceneDTO.fromDomain(scene);
    }
    
    /**
     * 封禁场景
     */
    public void banScene(Long sceneId, Integer bannedTimeInMinutes) {
        Scene scene = sceneRepository.findById(SceneId.of(sceneId))
            .orElseThrow(() -> new IllegalArgumentException("场景不存在"));
        
        scene.ban(bannedTimeInMinutes);
        sceneRepository.save(scene);
        
        // 发布事件
        scene.getDomainEvents().forEach(eventPublisher::publish);
        scene.clearDomainEvents();
    }
    
    /**
     * 解封场景
     */
    public void unbanScene(Long sceneId) {
        Scene scene = sceneRepository.findById(SceneId.of(sceneId))
            .orElseThrow(() -> new IllegalArgumentException("场景不存在"));
        
        scene.unban();
        sceneRepository.save(scene);
        
        // 发布事件
        scene.getDomainEvents().forEach(eventPublisher::publish);
        scene.clearDomainEvents();
    }
    
    /**
     * 根据ID获取场景
     */
    @Transactional(readOnly = true)
    public Optional<SceneDTO> getSceneById(Long id) {
        return sceneRepository.findById(SceneId.of(id))
            .map(SceneDTO::fromDomain);
    }
    
    /**
     * 根据名称获取场景
     */
    @Transactional(readOnly = true)
    public Optional<SceneDTO> getSceneByName(String name) {
        return sceneRepository.findByName(name)
            .map(SceneDTO::fromDomain);
    }
    
    private void publishDomainEvents(Scene scene, Node node) {
        // 发布场景相关事件
        scene.getDomainEvents().forEach(eventPublisher::publish);
        scene.clearDomainEvents();
        
        // 发布节点相关事件
        node.getDomainEvents().forEach(eventPublisher::publish);
        node.clearDomainEvents();
    }
    
    private Long generateSceneId() {
        return System.currentTimeMillis(); // 简化实现
    }
}
