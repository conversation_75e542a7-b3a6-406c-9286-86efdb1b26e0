package com.nnepoch.rendermanager.domain.scene.event;

import com.nnepoch.rendermanager.domain.shared.event.DomainEvent;
import com.nnepoch.rendermanager.domain.shared.valueobject.SceneId;
import lombok.Getter;

/**
 * 场景解封事件
 * <AUTHOR> Architect
 */
@Getter
public class SceneUnbannedEvent extends DomainEvent {
    private final SceneId sceneId;
    private final String sceneName;
    
    public SceneUnbannedEvent(SceneId sceneId, String sceneName) {
        super();
        this.sceneId = sceneId;
        this.sceneName = sceneName;
    }
    
    @Override
    public String getEventType() {
        return "SceneUnbanned";
    }
}
