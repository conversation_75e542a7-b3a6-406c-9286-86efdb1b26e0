package com.nnepoch.rendermanager.domain.node.valueobject;

import lombok.EqualsAndHashCode;
import lombok.Getter;

/**
 * 节点规格值对象
 * <AUTHOR> Migration
 */
@Getter
@EqualsAndHashCode
public class NodeSpec {
    private final String os;
    private final String osVersion;
    private final Integer maxSceneInstanceCount;
    private final String organization;
    private final String mobile;
    private final String email;
    
    public NodeSpec(String os, String osVersion, Integer maxSceneInstanceCount, 
                   String organization, String mobile, String email) {
        if (maxSceneInstanceCount == null || maxSceneInstanceCount <= 0) {
            throw new IllegalArgumentException("Max scene instance count must be positive");
        }
        
        this.os = os;
        this.osVersion = osVersion;
        this.maxSceneInstanceCount = maxSceneInstanceCount;
        this.organization = organization;
        this.mobile = mobile;
        this.email = email;
    }
    
    /**
     * 检查是否有足够容量运行新场景
     */
    public boolean hasCapacityFor(int currentRunningCount) {
        return currentRunningCount < maxSceneInstanceCount;
    }
    
    /**
     * 计算剩余容量
     */
    public int getRemainingCapacity(int currentRunningCount) {
        return Math.max(0, maxSceneInstanceCount - currentRunningCount);
    }
}
