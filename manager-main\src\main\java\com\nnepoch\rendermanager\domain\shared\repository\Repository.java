package com.nnepoch.rendermanager.domain.shared.repository;

import com.nnepoch.rendermanager.domain.shared.aggregate.AggregateRoot;
import com.nnepoch.rendermanager.domain.shared.valueobject.EntityId;

import java.util.Optional;

/**
 * 仓储接口基类
 * <AUTHOR> Migration
 */
public interface Repository<T extends AggregateRoot<ID>, ID extends EntityId> {
    
    /**
     * 根据ID查找聚合
     */
    Optional<T> findById(ID id);
    
    /**
     * 保存聚合
     */
    void save(T aggregate);
    
    /**
     * 删除聚合
     */
    void delete(T aggregate);
    
    /**
     * 根据ID删除聚合
     */
    void deleteById(ID id);
    
    /**
     * 检查聚合是否存在
     */
    boolean existsById(ID id);
}
