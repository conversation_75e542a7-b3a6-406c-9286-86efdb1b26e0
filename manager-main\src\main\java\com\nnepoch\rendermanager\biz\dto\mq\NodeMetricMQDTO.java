package com.nnepoch.rendermanager.biz.dto.mq;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> 2024/7/4
 */
@Data
public class NodeMetricMQDTO {
    private String name;
    private String kernelVersion;
    private String osVersion;
    private String longOsVersion;
    private String hostName;
    private String arch;
    private String bootTime;
    private Float[] loadAverage;
    private Map<String, Object> memory;
    private List<Map<String, Object>> cpus;
    private List<Map<String, Object>> gpus;
    private List<Map<String, Object>> disks;
    private Map<String, Object> networks;
    private Map<String, Object> metrics;
}
