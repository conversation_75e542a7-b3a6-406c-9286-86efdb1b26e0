package com.nnepoch.rendermanager.api.dto.response;

import com.nnepoch.rendermanager.api.dto.response.base.BaseResDTO;
import com.nnepoch.rendermanager.api.enums.UserRoleEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <AUTHOR> Che 2024/6/13
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "用户信息")
public class UserResDTO extends BaseResDTO {
    private String username;

    private UserRoleEnum role;

    @Schema(description = "是否已被封禁")
    private Boolean isBanned;

    @Schema(description = "密码明文, 仅首次创建用户时返回")
    private String rawPassword;

    private String email;

    private String mobile;

    private String avatar;

    @Schema(description = "最近登录时间")
    private Date loginAt;
}
