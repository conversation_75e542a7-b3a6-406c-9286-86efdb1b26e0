package com.nnepoch.rendermanager.controller;

/**
 * <AUTHOR> 2024/6/17
 */

import com.fasterxml.jackson.core.JsonProcessingException;
import com.nnepoch.rendermanager.api.dto.request.*;
import com.nnepoch.rendermanager.api.dto.response.SceneResDTO;
import com.nnepoch.rendermanager.api.dto.response.base.ActionResDTO;
import com.nnepoch.rendermanager.api.dto.response.base.PageResDTO;
import com.nnepoch.rendermanager.biz.entity.NodeEntity;
import com.nnepoch.rendermanager.biz.service.SceneService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@RestController
@RequestMapping(path = "/api/v1/scenes", produces = "application/json")
@RequiredArgsConstructor
@Tag(name = "Scene", description = "场景管理相关接口")
@SecurityRequirement(name = "bearerAuth")
public class SceneController {
    private final SceneService sceneService;

    //    @OpenApi
    @GetMapping
    @Operation(summary = "获取场景列表", description = "根据查询条件获取场景列表")
    @ApiResponse(responseCode = "200", description = "场景列表")
    public PageResDTO<SceneResDTO> getSceneList(@ParameterObject SceneQueryDTO queryDTO) {
        return sceneService.getSceneList(queryDTO);
    }

    @PreAuthorize("hasAnyRole('ADMIN', 'OPERATOR')")
    @PostMapping
    @Operation(summary = "创建场景", description = "创建一个新的场景")
    @ApiResponse(responseCode = "200", description = "创建场景结果")
    public SceneResDTO addScene(@Valid @RequestBody SceneCreateDTO reqDTO) {
        return sceneService.addScene(reqDTO);
    }

    @PreAuthorize("hasAnyRole('ADMIN', 'OPERATOR')")
    @PatchMapping("/{id}")
    @Operation(summary = "更新场景", description = "根据场景ID更新场景信息")
    @ApiResponse(responseCode = "200", description = "更新场景结果")
    public SceneResDTO updateScene(@PathVariable("id") Long id, @Valid @RequestBody SceneUpdateDTO reqDTO) {
        return sceneService.updateScene(id, reqDTO);
    }

    @GetMapping("/{id}")
    @Operation(summary = "获取场景详情", description = "根据场景ID获取场景详情")
    @ApiResponse(responseCode = "200", description = "场景详情")
    public SceneResDTO getSceneInfo(@PathVariable("id") Long id) {
        return sceneService.getSceneInfo(id);
    }

    @PreAuthorize("hasAnyRole('ADMIN', 'OPERATOR')")
    @PostMapping("/{id}/start")
    @Operation(summary = "启动场景", description = "根据场景ID启动场景")
    @ApiResponse(responseCode = "200", description = "启动场景结果")
    public ActionResDTO<NodeEntity> startScene(@PathVariable("id") Long id, @Valid @RequestBody SceneStartOptionDTO reqDTO) {
        return sceneService.startScene(id, reqDTO);
    }

    @PreAuthorize("hasAnyRole('ADMIN', 'OPERATOR')")
    @PostMapping("/{id}/stop")
    @Operation(summary = "停止场景", description = "根据场景ID停止场景")
    @ApiResponse(responseCode = "200", description = "停止场景结果")
    public ActionResDTO<Void> stopScene(@PathVariable("id") Long id, @Valid @RequestBody SceneStopOptionDTO reqDTO) {
        return sceneService.stopScene(id, reqDTO);
    }

    @PreAuthorize("hasAnyRole('ADMIN', 'OPERATOR')")
    @PostMapping("/{id}/restart")
    @Operation(summary = "重启场景", description = "根据场景ID重启场景")
    @ApiResponse(responseCode = "200", description = "重启场景结果")
    public ActionResDTO<Void> restartScene(@PathVariable("id") Long id) {
        return sceneService.restartScene(id);
    }

    @PreAuthorize("hasAnyRole('ADMIN', 'OPERATOR')")
    @PostMapping("/{id}/banned")
    @Operation(summary = "禁用场景")
    @ApiResponse(responseCode = "200", description = "禁用场景结果")
    public ActionResDTO<Void> banScene(
        @PathVariable("id") Long id,
        @Valid
        @NotNull(message = "请求体不能为空")
        @Schema(
            requiredProperties = {"bannedTime"},
            requiredMode = Schema.RequiredMode.REQUIRED,
            example = "{ \"bannedTime\": 10 }",
            description = "禁用时间参数, 单位：分钟， -1 表示永久禁用",
            additionalProperties = Schema.AdditionalPropertiesValue.FALSE
        )
        @RequestBody
        Map<String, Integer> reqDTO
    ) throws JsonProcessingException {
        Integer bannedTime = reqDTO.get("bannedTime");

        return sceneService.banScene(id, bannedTime);
    }

    @PreAuthorize("hasAnyRole('ADMIN', 'OPERATOR')")
    @PostMapping("/{id}/permitted")
    @Operation(summary = "启用场景")
    @ApiResponse(responseCode = "200", description = "启用场景结果")
    public ActionResDTO<Void> permitScene(@PathVariable("id") Long id) {
        return sceneService.permitScene(id);
    }

    @PreAuthorize("hasAnyRole('ADMIN', 'OPERATOR')")
    @PostMapping("/{id}/free")
    @Operation(summary = "释放场景实例")
    @ApiResponse(responseCode = "200", description = "释放场景实例结果")
    public ActionResDTO<Void> freeSceneInstance(@PathVariable("id") Long id) {
        return sceneService.freeSceneInstance(id);
    }
}
