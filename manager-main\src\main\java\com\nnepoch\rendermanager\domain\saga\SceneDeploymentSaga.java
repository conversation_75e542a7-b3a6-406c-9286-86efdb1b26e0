package com.nnepoch.rendermanager.domain.saga;

import com.nnepoch.rendermanager.domain.node.event.NodeOnlineEvent;
import com.nnepoch.rendermanager.domain.scene.event.SceneCreatedEvent;
import com.nnepoch.rendermanager.domain.shared.valueobject.NodeId;
import com.nnepoch.rendermanager.domain.shared.valueobject.SceneId;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * 场景部署Saga
 * 协调场景创建、节点分配、资源准备等跨聚合操作
 * <AUTHOR> Architect
 */
@Data
@Slf4j
public class SceneDeploymentSaga {
    
    private String sagaId;
    private SceneId sceneId;
    private NodeId assignedNodeId;
    private SagaState state;
    private LocalDateTime startedAt;
    private LocalDateTime completedAt;
    private String failureReason;
    private int retryCount;
    
    public enum SagaState {
        STARTED,
        SCENE_CREATED,
        NODE_ALLOCATED,
        RESOURCES_PREPARED,
        DEPLOYMENT_INITIATED,
        COMPLETED,
        FAILED,
        COMPENSATING,
        COMPENSATED
    }
    
    public SceneDeploymentSaga() {
        this.sagaId = UUID.randomUUID().toString();
        this.state = SagaState.STARTED;
        this.startedAt = LocalDateTime.now();
        this.retryCount = 0;
    }
    
    /**
     * 处理场景创建事件
     */
    public void handle(SceneCreatedEvent event) {
        if (state != SagaState.STARTED) {
            log.warn("Received SceneCreatedEvent in invalid state: {}", state);
            return;
        }
        
        this.sceneId = event.getSceneId();
        this.state = SagaState.SCENE_CREATED;
        
        log.info("Saga {}: Scene created, proceeding to node allocation", sagaId);
        
        // 触发下一步：节点分配
        triggerNodeAllocation();
    }
    
    /**
     * 处理节点上线事件
     */
    public void handle(NodeOnlineEvent event) {
        if (state != SagaState.SCENE_CREATED) {
            return; // 不是我们关心的状态
        }
        
        this.assignedNodeId = event.getNodeId();
        this.state = SagaState.NODE_ALLOCATED;
        
        log.info("Saga {}: Node allocated, preparing resources", sagaId);
        
        // 触发下一步：资源准备
        triggerResourcePreparation();
    }
    
    /**
     * 处理资源准备完成
     */
    public void handleResourcesPrepared() {
        if (state != SagaState.NODE_ALLOCATED) {
            log.warn("Received ResourcesPrepared in invalid state: {}", state);
            return;
        }
        
        this.state = SagaState.RESOURCES_PREPARED;
        
        log.info("Saga {}: Resources prepared, initiating deployment", sagaId);
        
        // 触发下一步：部署启动
        triggerDeploymentInitiation();
    }
    
    /**
     * 处理部署完成
     */
    public void handleDeploymentCompleted() {
        if (state != SagaState.DEPLOYMENT_INITIATED) {
            log.warn("Received DeploymentCompleted in invalid state: {}", state);
            return;
        }
        
        this.state = SagaState.COMPLETED;
        this.completedAt = LocalDateTime.now();
        
        log.info("Saga {}: Deployment completed successfully", sagaId);
    }
    
    /**
     * 处理失败
     */
    public void handleFailure(String reason) {
        this.failureReason = reason;
        this.state = SagaState.FAILED;
        
        log.error("Saga {}: Failed with reason: {}", sagaId, reason);
        
        // 启动补偿流程
        startCompensation();
    }
    
    /**
     * 重试操作
     */
    public boolean canRetry() {
        return retryCount < 3 && state == SagaState.FAILED;
    }
    
    public void retry() {
        if (!canRetry()) {
            throw new IllegalStateException("Cannot retry saga in current state");
        }
        
        this.retryCount++;
        this.state = SagaState.STARTED;
        this.failureReason = null;
        
        log.info("Saga {}: Retrying (attempt {})", sagaId, retryCount);
    }
    
    /**
     * 启动补偿流程
     */
    private void startCompensation() {
        this.state = SagaState.COMPENSATING;
        
        log.info("Saga {}: Starting compensation", sagaId);
        
        // 根据当前状态执行相应的补偿操作
        switch (state) {
            case DEPLOYMENT_INITIATED -> compensateDeployment();
            case RESOURCES_PREPARED -> compensateResourcePreparation();
            case NODE_ALLOCATED -> compensateNodeAllocation();
            case SCENE_CREATED -> compensateSceneCreation();
        }
        
        this.state = SagaState.COMPENSATED;
        this.completedAt = LocalDateTime.now();
    }
    
    private void triggerNodeAllocation() {
        // 发送命令或事件来分配节点
        log.debug("Triggering node allocation for scene: {}", sceneId);
    }
    
    private void triggerResourcePreparation() {
        // 发送命令来准备资源
        log.debug("Triggering resource preparation for node: {}", assignedNodeId);
    }
    
    private void triggerDeploymentInitiation() {
        // 发送命令来启动部署
        log.debug("Triggering deployment initiation");
    }
    
    private void compensateDeployment() {
        // 停止部署，清理资源
        log.debug("Compensating deployment");
    }
    
    private void compensateResourcePreparation() {
        // 释放已准备的资源
        log.debug("Compensating resource preparation");
    }
    
    private void compensateNodeAllocation() {
        // 释放节点分配
        log.debug("Compensating node allocation");
    }
    
    private void compensateSceneCreation() {
        // 删除已创建的场景
        log.debug("Compensating scene creation");
    }
    
    /**
     * 检查Saga是否完成
     */
    public boolean isCompleted() {
        return state == SagaState.COMPLETED || state == SagaState.COMPENSATED;
    }
    
    /**
     * 检查Saga是否失败
     */
    public boolean isFailed() {
        return state == SagaState.FAILED;
    }
    
    /**
     * 获取执行时长
     */
    public long getExecutionTimeInSeconds() {
        LocalDateTime endTime = completedAt != null ? completedAt : LocalDateTime.now();
        return java.time.Duration.between(startedAt, endTime).getSeconds();
    }
}
