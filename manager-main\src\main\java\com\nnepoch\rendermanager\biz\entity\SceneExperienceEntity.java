package com.nnepoch.rendermanager.biz.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nnepoch.rendermanager.api.dto.response.SimpleUserResDTO;
import com.nnepoch.rendermanager.api.enums.SceneExperienceStatusEnum;
import com.nnepoch.rendermanager.biz.entity.handler.SceneExperienceStatusEnumTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <AUTHOR> <PERSON> 2024/6/17
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("scene_experience")
public class SceneExperienceEntity extends BaseEntity {
    private String hashId;
    private Long sceneId;
    private SceneExperienceStatusEnum status;
    private Boolean isBanned;
    private String username;
    private String organization;
    private String mobile;
    private String email;

    @TableField(exist = false)
    private String avatar;

    private Integer duration;
    private Date expiredAt;
    private Integer maxConcurrent;
    private Date viewedAt;
    private Long createdBy;

    @TableField(exist = false)
    private SimpleUserResDTO creator;

    public String getAvatar() {
        return "/avatar/9.x/big-smile/svg?seed=" + this.getId();
    }
}
