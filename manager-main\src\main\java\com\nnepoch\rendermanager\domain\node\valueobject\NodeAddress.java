package com.nnepoch.rendermanager.domain.node.valueobject;

import lombok.EqualsAndHashCode;
import lombok.Getter;

/**
 * 节点地址值对象
 * <AUTHOR> Migration
 */
@Getter
@EqualsAndHashCode
public class NodeAddress {
    private final String sn;
    private final String hostname;
    private final String hashId;
    
    public NodeAddress(String sn, String hostname, String hashId) {
        if (sn == null || sn.trim().isEmpty()) {
            throw new IllegalArgumentException("Node SN cannot be empty");
        }
        if (hostname == null || hostname.trim().isEmpty()) {
            throw new IllegalArgumentException("Hostname cannot be empty");
        }
        
        this.sn = sn.trim();
        this.hostname = hostname.trim();
        this.hashId = hashId;
    }
    
    /**
     * 生成节点的唯一标识
     */
    public String getUniqueIdentifier() {
        return String.format("%s-%s", sn, hostname);
    }
}
