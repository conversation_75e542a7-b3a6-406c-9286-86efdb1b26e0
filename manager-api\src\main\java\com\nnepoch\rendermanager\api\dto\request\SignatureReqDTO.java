package com.nnepoch.rendermanager.api.dto.request;

import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "签名请求")
public class SignatureReqDTO {
    @Parameter(description = "客户端ID", required = true, example = "081fcf4505ad93e22e5d")
    private String clientId;

    @Parameter(description = "时间戳", required = true)
    private Long timestamp;

    @Parameter(description = "随机数", required = true)
    private String nonce;

    @Parameter(description = "签名", required = true)
    private String sign;
}
