package com.nnepoch.rendermanager.api.dto.request;

import com.nnepoch.rendermanager.api.dto.request.base.PageQueryDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> 2024/8/8
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "场景实例查询条件")
public class SceneInstanceQueryDTO extends PageQueryDTO {
    @Schema(description = "场景ID")
    private Long sceneId;

    @Schema(description = "节点ID")
    private Long nodeId;
}
