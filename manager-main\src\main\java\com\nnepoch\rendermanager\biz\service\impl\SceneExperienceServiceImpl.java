package com.nnepoch.rendermanager.biz.service.impl;

import cn.hutool.core.lang.id.NanoId;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nnepoch.rendermanager.api.dto.request.SceneExperienceQueryDTO;
import com.nnepoch.rendermanager.api.dto.request.SceneExperienceReqDTO;
import com.nnepoch.rendermanager.api.dto.response.SceneExperienceResDTO;
import com.nnepoch.rendermanager.api.dto.response.base.ActionResDTO;
import com.nnepoch.rendermanager.api.dto.response.base.PageResDTO;
import com.nnepoch.rendermanager.api.enums.SceneExperienceStatusEnum;
import com.nnepoch.rendermanager.api.enums.UserRoleEnum;
import com.nnepoch.rendermanager.biz.entity.SceneExperienceEntity;
import com.nnepoch.rendermanager.biz.entity.UserEntity;
import com.nnepoch.rendermanager.biz.mapper.SceneExperienceMapper;
import com.nnepoch.rendermanager.biz.service.SceneExperienceService;
import com.nnepoch.rendermanager.exception.BizException;
import com.nnepoch.rendermanager.utils.SqlOrderUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> Che 2024/6/18
 */
@RequiredArgsConstructor
@Service
public class SceneExperienceServiceImpl extends ServiceImpl<SceneExperienceMapper, SceneExperienceEntity> implements SceneExperienceService {
    private final SceneExperienceMapper sceneExperienceMapper;

    @Override
    public PageResDTO<SceneExperienceResDTO> getSceneExperiences(SceneExperienceQueryDTO queryDTO) {
        UserEntity currentUser = (UserEntity) SecurityContextHolder.getContext().getAuthentication().getPrincipal();

        Page<SceneExperienceEntity> pageObject = new Page<>(queryDTO.getPage(), queryDTO.getSize());

        QueryWrapper<SceneExperienceEntity> wrapper = new QueryWrapper<>();
        wrapper.apply("se.deleted_at IS NULL");
        wrapper.like(ObjectUtil.isNotEmpty(queryDTO.getSceneName()), "s.name", queryDTO.getSceneName());
        wrapper.like(ObjectUtil.isNotEmpty(queryDTO.getUsername()), "se.username", queryDTO.getUsername());
        wrapper.like(ObjectUtil.isNotEmpty(queryDTO.getOrganization()), "se.organization", queryDTO.getOrganization());
        wrapper.eq(ObjectUtil.isNotEmpty(queryDTO.getCreatedBy()), "se.created_by", queryDTO.getCreatedBy());
        wrapper.eq(currentUser.getRole() == UserRoleEnum.OPERATOR, "se.created_by", currentUser.getId());

        if (ObjectUtil.isNotEmpty(queryDTO.getStatus())) {
            wrapper.eq("se.status", queryDTO.getStatus().getCode());
        }

        if (CharSequenceUtil.isNotEmpty(queryDTO.getSort())) {
            pageObject.addOrder(SqlOrderUtil.parseOrderItem(queryDTO.getSort()));
        } else {
            pageObject.addOrder(SqlOrderUtil.parseOrderItem("se.id,desc"));
        }

        IPage<SceneExperienceResDTO> page = sceneExperienceMapper.selectByPage(pageObject, wrapper);

        return new PageResDTO<>(
            page.getRecords(),
            new PageResDTO.Pagination(page.getCurrent(), page.getSize(), page.getTotal())
        );
    }

    @Override
    public SceneExperienceResDTO getSceneExperience(Long id) {
        return sceneExperienceMapper.selectById(id);
    }

    @Override
    public SceneExperienceEntity getByHashId(String hashId) {
        SceneExperienceEntity sceneExperience = sceneExperienceMapper.selectOne(Wrappers.<SceneExperienceEntity>lambdaQuery().eq(SceneExperienceEntity::getHashId, hashId));
        if (sceneExperience == null) {
            throw new BizException(HttpStatus.NOT_FOUND, "场景体验不存在");
        }

        return sceneExperience;
    }

    @Override
    public SceneExperienceResDTO addSceneExperience(SceneExperienceReqDTO reqDTO) {
        SceneExperienceEntity entity = toEntity(reqDTO);
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        UserEntity user = (UserEntity) authentication.getPrincipal();
        entity.setHashId(NanoId.randomNanoId(16));
        entity.setCreatedBy(user.getId());
        sceneExperienceMapper.insert(entity);
        return getSceneExperience(entity.getId());
    }

    @Override
    public SceneExperienceResDTO updateSceneExperience(Long id, SceneExperienceReqDTO reqDTO) {
        SceneExperienceEntity entity = toEntity(reqDTO);
        entity.setId(id);
        sceneExperienceMapper.updateById(entity);
        return toDTO(entity);
    }

    @Override
    public ActionResDTO<Void> banSceneExperience(Long id) {
        SceneExperienceEntity entity = new SceneExperienceEntity();
        entity.setId(id);
        entity.setIsBanned(true);
        entity.setStatus(SceneExperienceStatusEnum.DISABLED);
        sceneExperienceMapper.updateById(entity);
        return ActionResDTO.success();
    }

    @Override
    public ActionResDTO<Void> permitSceneExperience(Long id) {
        SceneExperienceEntity entity = new SceneExperienceEntity();
        entity.setId(id);
        entity.setIsBanned(false);
        entity.setStatus(SceneExperienceStatusEnum.IDLE);
        sceneExperienceMapper.updateById(entity);
        return ActionResDTO.success();
    }

    private SceneExperienceEntity toEntity(SceneExperienceReqDTO reqDTO) {
        SceneExperienceEntity entity = new SceneExperienceEntity();
        entity.setSceneId(reqDTO.getSceneId());
        entity.setStatus(reqDTO.getStatus());
        entity.setUsername(reqDTO.getUsername());
        entity.setOrganization(reqDTO.getOrganization());
        entity.setMobile(reqDTO.getMobile());
        entity.setEmail(reqDTO.getEmail());
        entity.setDuration(reqDTO.getDuration());
        entity.setExpiredAt(reqDTO.getExpiredAt());
        return entity;
    }

    private SceneExperienceResDTO toDTO(SceneExperienceEntity entity) {
        if (entity == null) {
            return null;
        }
        SceneExperienceResDTO sceneExperienceResDTO = new SceneExperienceResDTO();
        sceneExperienceResDTO.setSceneId(entity.getSceneId());
        sceneExperienceResDTO.setStatus(entity.getStatus());
        sceneExperienceResDTO.setUsername(entity.getUsername());
        sceneExperienceResDTO.setOrganization(entity.getOrganization());
        sceneExperienceResDTO.setMobile(entity.getMobile());
        sceneExperienceResDTO.setEmail(entity.getEmail());
        sceneExperienceResDTO.setAvatar(entity.getAvatar());
        sceneExperienceResDTO.setDuration(entity.getDuration());
        sceneExperienceResDTO.setExpiredAt(entity.getExpiredAt());
        sceneExperienceResDTO.setViewedAt(entity.getViewedAt());
        sceneExperienceResDTO.setCreatedBy(entity.getCreatedBy());
        sceneExperienceResDTO.setId(entity.getId());
        sceneExperienceResDTO.setRemark(entity.getRemark());
        sceneExperienceResDTO.setCreatedAt(entity.getCreatedAt());
        sceneExperienceResDTO.setUpdatedAt(entity.getUpdatedAt());
        sceneExperienceResDTO.setDeletedAt(entity.getDeletedAt());
        return sceneExperienceResDTO;
    }
}
