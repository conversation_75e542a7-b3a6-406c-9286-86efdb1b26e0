package com.nnepoch.rendermanager.api.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * <AUTHOR> 2024/6/20
 */
@Data
@Schema(description = "登录请求参数")
public class SigninReqDTO {
    @NotBlank(message = "手机号不能为空")
    private String mobile;

    @NotBlank(message = "用户名或密码错误")
    private String keyId;

    @NotBlank(message = "密码不能为空, 经 rsa 公钥加密后的 base64 字符串")
    private String password;
}
