package com.nnepoch.rendermanager.domain.user.service;

import com.nnepoch.rendermanager.domain.user.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 用户领域服务
 * <AUTHOR> Migration
 */
@Service
@RequiredArgsConstructor
public class UserDomainService {
    
    private final UserRepository userRepository;
    
    /**
     * 验证用户创建的业务规则
     */
    public void validateUserCreation(String username, String email, String mobile) {
        if (userRepository.existsByUsername(username)) {
            throw new IllegalArgumentException("用户名已存在");
        }
        
        if (userRepository.existsByEmail(email)) {
            throw new IllegalArgumentException("邮箱已存在");
        }
        
        if (userRepository.existsByMobile(mobile)) {
            throw new IllegalArgumentException("手机号已存在");
        }
    }
    
    /**
     * 检查用户是否可以执行某个操作
     */
    public boolean canPerformAction(String username, String action) {
        return userRepository.findByUsername(username)
                .map(user -> user.canAccess() && hasPermission(user, action))
                .orElse(false);
    }
    
    private boolean hasPermission(com.nnepoch.rendermanager.domain.user.aggregate.User user, String action) {
        // 权限检查逻辑
        return true; // 简化实现
    }
}
