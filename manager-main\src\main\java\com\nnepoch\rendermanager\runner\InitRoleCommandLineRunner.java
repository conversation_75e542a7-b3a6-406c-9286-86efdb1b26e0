package com.nnepoch.rendermanager.runner;

import com.nnepoch.rendermanager.biz.entity.RoleEntity;
import com.nnepoch.rendermanager.biz.entity.enums.UserPermissionEnum;
import com.nnepoch.rendermanager.biz.mapper.RoleMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import java.util.EnumSet;
import java.util.List;

/**
 * <AUTHOR> <PERSON> 2024/8/5
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class InitRoleCommandLineRunner implements CommandLineRunner {
    private final RoleMapper roleMapper;

    @Override
    public void run(String... args) throws Exception {
        log.info("Running InitRoleCommandLineRunner");

        roleMapper.delete(null);

        RoleEntity adminRole = new RoleEntity();
        adminRole.setName("admin");
        adminRole.setPermissionSet(EnumSet.of(
            UserPermissionEnum.USER_READ,
            UserPermissionEnum.USER_CREATE,
            UserPermissionEnum.USER_UPDATE,
            UserPermissionEnum.USER_DELETE
        ));

        RoleEntity operatorRole = new RoleEntity();
        operatorRole.setName("operator");
        operatorRole.setPermissionSet(EnumSet.of(
            UserPermissionEnum.NODE_READ,
            UserPermissionEnum.NODE_CREATE,
            UserPermissionEnum.NODE_UPDATE,
            UserPermissionEnum.NODE_DELETE
        ));

        RoleEntity memberRole = new RoleEntity();
        memberRole.setName("member");
        memberRole.setPermissionSet(EnumSet.of(
            UserPermissionEnum.USER_READ_ME,
            UserPermissionEnum.USER_UPDATE_ME
        ));

        roleMapper.insert(List.of(adminRole, operatorRole, memberRole));

        log.info("InitRoleCommandLineRunner finished");
    }
}
