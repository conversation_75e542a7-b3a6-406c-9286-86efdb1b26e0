package com.nnepoch.rendermanager.biz.dto.ws;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Map;

/**
 * <AUTHOR> 2024/7/2
 */
@Set<PERSON>
@Getter
@AllArgsConstructor
@NoArgsConstructor
public class WebSocketMessageDTO {
    private String trigger;
    private Map<String, Object> data;
    private String to;

    public WebSocketMessageDTO(String trigger) {
        this.trigger = trigger;
    }
}
