package com.nnepoch.rendermanager.api.dto.response.base;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> 2024/6/14
 */
@Data
@AllArgsConstructor
@Schema(description = "分页返回结果")
public class PageResDTO<T> {
    @Schema(description = "数据列表")
    private List<T> data;

    @Schema(description = "分页信息")
    private Pagination pagination;

    @Data
    @AllArgsConstructor
    public static class Pagination {
        @Schema(description = "当前页码")
        private Long page;

        @Schema(description = "每页数量")
        private Long size;

        @Schema(description = "总页数")
        private Long count;
    }
}
