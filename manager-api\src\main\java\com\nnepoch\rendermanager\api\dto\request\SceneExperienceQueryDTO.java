package com.nnepoch.rendermanager.api.dto.request;

import com.nnepoch.rendermanager.api.dto.request.base.PageQueryDTO;
import com.nnepoch.rendermanager.api.enums.SceneExperienceStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> <PERSON> 2024/6/17
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "场景体验查询条件")
public class SceneExperienceQueryDTO extends PageQueryDTO {
    @Schema(description = "客户名")
    private String username;

    @Schema(description = "组织/单位名")
    private String organization;

    @Schema(description = "场景名")
    private String sceneName;

    @Schema(description = "创建人ID")
    private Long createdBy;

    @Schema(description = "访问状态")
    private SceneExperienceStatusEnum status;
}
