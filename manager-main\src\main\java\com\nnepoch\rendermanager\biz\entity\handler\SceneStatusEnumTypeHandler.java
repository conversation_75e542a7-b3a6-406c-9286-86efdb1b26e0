package com.nnepoch.rendermanager.biz.entity.handler;

import com.nnepoch.rendermanager.api.enums.SceneStatusEnum;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * <AUTHOR> 2024/7/30
 */
public class SceneStatusEnumTypeHandler extends BaseTypeHandler<SceneStatusEnum> {
    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, SceneStatusEnum parameter, JdbcType jdbcType) throws SQLException {
        ps.setInt(i, parameter.getCode());
    }

    @Override
    public SceneStatusEnum getNullableResult(ResultSet rs, String columnName) throws SQLException {
        Integer code = rs.getInt(columnName);
        return SceneStatusEnum.fromCode(code);
    }

    @Override
    public SceneStatusEnum getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        Integer code = rs.getInt(columnIndex);
        return SceneStatusEnum.fromCode(code);
    }

    @Override
    public SceneStatusEnum getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        Integer code = cs.getInt(columnIndex);
        return SceneStatusEnum.fromCode(code);
    }
}
