package com.nnepoch.rendermanager.domain.scene.aggregate;

import com.nnepoch.rendermanager.domain.scene.event.SceneBannedEvent;
import com.nnepoch.rendermanager.domain.scene.event.SceneCreatedEvent;
import com.nnepoch.rendermanager.domain.scene.event.SceneUnbannedEvent;
import com.nnepoch.rendermanager.domain.scene.valueobject.SceneConfig;
import com.nnepoch.rendermanager.domain.shared.aggregate.AggregateRoot;
import com.nnepoch.rendermanager.domain.shared.valueobject.SceneId;
import lombok.Getter;

import java.time.LocalDateTime;

/**
 * 场景聚合根
 * <AUTHOR> Migration
 */
@Getter
public class Scene extends AggregateRoot<SceneId> {
    private SceneConfig config;
    private String thumb;
    private String changelog;
    private boolean isBanned;
    private Integer bannedTime; // 封禁时长(分钟)
    private LocalDateTime viewedAt;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    
    // 运行时状态 (计算属性，不持久化)
    private transient int seatsTaken = 0;
    private transient int seatsTotal = 0;
    
    // 私有构造函数
    private Scene(SceneId id, SceneConfig config, String thumb) {
        super(id);
        this.config = config;
        this.thumb = thumb;
        this.isBanned = false;
        this.bannedTime = null;
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * 创建新场景
     */
    public static Scene create(SceneId id, SceneConfig config, String thumb) {
        Scene scene = new Scene(id, config, thumb);
        scene.addDomainEvent(new SceneCreatedEvent(id, config.getName(), config.getVersion()));
        return scene;
    }
    
    /**
     * 更新场景配置
     */
    public void updateConfig(SceneConfig newConfig) {
        this.config = newConfig;
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * 更新缩略图
     */
    public void updateThumbnail(String newThumb) {
        this.thumb = newThumb;
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * 更新变更日志
     */
    public void updateChangelog(String changelog) {
        this.changelog = changelog;
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * 封禁场景
     */
    public void ban(Integer bannedTimeInMinutes) {
        if (this.isBanned) {
            return; // 已经被封禁
        }
        
        this.isBanned = true;
        this.bannedTime = bannedTimeInMinutes;
        this.updatedAt = LocalDateTime.now();
        
        addDomainEvent(new SceneBannedEvent(this.id, config.getName(), bannedTimeInMinutes));
    }
    
    /**
     * 解封场景
     */
    public void unban() {
        if (!this.isBanned) {
            return; // 没有被封禁
        }
        
        this.isBanned = false;
        this.bannedTime = null;
        this.updatedAt = LocalDateTime.now();
        
        addDomainEvent(new SceneUnbannedEvent(this.id, config.getName()));
    }
    
    /**
     * 记录访问
     */
    public void recordView() {
        this.viewedAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * 检查场景是否可用
     */
    public boolean isAvailable() {
        return !isBanned;
    }
    
    /**
     * 检查是否为官方场景
     */
    public boolean isOfficial() {
        return config.isOfficialScene();
    }
    
    /**
     * 获取场景完整标识
     */
    public String getFullIdentifier() {
        return config.getFullIdentifier();
    }
    
    /**
     * 更新运行时座位信息 (由外部服务计算)
     */
    public void updateSeatInfo(int seatsTaken, int seatsTotal) {
        this.seatsTaken = seatsTaken;
        this.seatsTotal = seatsTotal;
    }
    
    /**
     * 检查是否有可用座位
     */
    public boolean hasAvailableSeats() {
        return seatsTaken < seatsTotal;
    }
    
    /**
     * 获取可用座位数
     */
    public int getAvailableSeats() {
        return Math.max(0, seatsTotal - seatsTaken);
    }
}
