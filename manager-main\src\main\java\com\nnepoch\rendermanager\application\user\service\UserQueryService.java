package com.nnepoch.rendermanager.application.user.service;

import com.nnepoch.rendermanager.application.user.dto.UserDTO;
import com.nnepoch.rendermanager.application.user.dto.UserStatisticsDTO;
import com.nnepoch.rendermanager.application.user.query.UserQuery;
import com.nnepoch.rendermanager.domain.user.repository.UserRepository;
import com.nnepoch.rendermanager.infrastructure.persistence.user.UserQueryRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 用户查询服务 (CQRS模式)
 * <AUTHOR> Migration
 */
@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)
public class UserQueryService {
    
    private final UserRepository userRepository;
    private final UserQueryRepository userQueryRepository; // 专门用于复杂查询的仓储
    
    /**
     * 分页查询用户
     */
    public Page<UserDTO> queryUsers(UserQuery query) {
        query.validate();
        query.withDefaults();
        
        return userQueryRepository.findByQuery(query);
    }
    
    /**
     * 根据ID查询用户详情
     */
    public Optional<UserDTO> getUserDetail(Long userId) {
        return userQueryRepository.findDetailById(userId);
    }
    
    /**
     * 搜索用户 (支持多字段模糊搜索)
     */
    public List<UserDTO> searchUsers(String keyword, int limit) {
        if (keyword == null || keyword.trim().isEmpty()) {
            return List.of();
        }
        
        return userQueryRepository.searchByKeyword(keyword.trim(), limit);
    }
    
    /**
     * 获取用户统计信息
     */
    public UserStatisticsDTO getUserStatistics() {
        return UserStatisticsDTO.builder()
            .totalUsers(userQueryRepository.countTotal())
            .activeUsers(userQueryRepository.countActive(LocalDateTime.now().minusDays(30)))
            .newUsersToday(userQueryRepository.countNewUsers(LocalDateTime.now().toLocalDate()))
            .newUsersThisWeek(userQueryRepository.countNewUsers(LocalDateTime.now().minusDays(7)))
            .newUsersThisMonth(userQueryRepository.countNewUsers(LocalDateTime.now().minusDays(30)))
            .bannedUsers(userQueryRepository.countBanned())
            .adminUsers(userQueryRepository.countByRole("ADMIN"))
            .operatorUsers(userQueryRepository.countByRole("OPERATOR"))
            .memberUsers(userQueryRepository.countByRole("MEMBER"))
            .build();
    }
    
    /**
     * 获取用户活跃度报告
     */
    public List<UserDTO> getActiveUsersReport(int days) {
        LocalDateTime since = LocalDateTime.now().minusDays(days);
        return userQueryRepository.findActiveUsersSince(since);
    }
    
    /**
     * 获取用户登录历史
     */
    public List<UserDTO> getUserLoginHistory(Long userId, int limit) {
        return userQueryRepository.findLoginHistory(userId, limit);
    }
    
    /**
     * 检查用户名是否可用
     */
    public boolean isUsernameAvailable(String username) {
        return !userRepository.existsByUsername(username);
    }
    
    /**
     * 检查邮箱是否可用
     */
    public boolean isEmailAvailable(String email) {
        return !userRepository.existsByEmail(email);
    }
    
    /**
     * 检查手机号是否可用
     */
    public boolean isMobileAvailable(String mobile) {
        return !userRepository.existsByMobile(mobile);
    }
    
    /**
     * 获取用户权限列表
     */
    public List<String> getUserPermissions(Long userId) {
        return userQueryRepository.findUserPermissions(userId);
    }
    
    /**
     * 导出用户数据
     */
    public List<UserDTO> exportUsers(UserQuery query) {
        // 导出时不分页，但限制最大数量
        query.setPage(null);
        query.setSize(null);
        
        List<UserDTO> users = userQueryRepository.findForExport(query);
        
        // 限制导出数量，防止内存溢出
        if (users.size() > 10000) {
            throw new IllegalStateException("导出数据量过大，请缩小查询范围");
        }
        
        return users;
    }
}
