package com.nnepoch.rendermanager.config;

import com.nnepoch.rendermanager.config.annotations.OpenApi;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.core.annotation.Order;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.servlet.mvc.method.RequestMappingInfo;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping;
import org.springframework.web.util.pattern.PathPattern;

import java.lang.reflect.Method;
import java.util.Set;

@Component
@Order(Ordered.HIGHEST_PRECEDENCE)
public class OpenApiHandlerMapping extends RequestMappingHandlerMapping {
    @Override
    protected void registerHandlerMethod(Object handler, Method method, RequestMappingInfo mapping) {
        // 首先注册原始映射
        super.registerHandlerMethod(handler, method, mapping);

        // 获取类和方法级别的 OpenApi 注解
        Class<?> handlerType = handler instanceof String ? obtainApplicationContext().getType((String) handler)
            : handler.getClass();
        OpenApi classOpenApi = AnnotationUtils.findAnnotation(handlerType, OpenApi.class);
        OpenApi methodOpenApi = AnnotationUtils.findAnnotation(method, OpenApi.class);

        if (classOpenApi == null && methodOpenApi == null) {
            return;
        }

        // 创建并注册新的 OpenApi 映射
        RequestMappingInfo openApiMapping = createOpenApiMapping(mapping, classOpenApi, methodOpenApi);
        if (openApiMapping != null) {
            super.registerHandlerMethod(handler, method, openApiMapping);
        }
    }

    private RequestMappingInfo createOpenApiMapping(RequestMappingInfo mapping, OpenApi classOpenApi, OpenApi methodOpenApi) {
        // 获取前缀，优先使用方法级别的注解值
        String prefix = methodOpenApi != null ? methodOpenApi.value() :
            (classOpenApi != null ? classOpenApi.value() : "/open");

        if (!StringUtils.hasText(prefix)) {
            prefix = "/open";
        }

        // 确保前缀以 "/" 开头
        prefix = prefix.startsWith("/") ? prefix : "/" + prefix;

        // 获取原始路径并添加前缀
        Set<PathPattern> patterns = mapping.getPathPatternsCondition().getPatterns();
        String finalPrefix = prefix;
        String[] prefixedPaths = patterns.stream()
            .map(pattern -> finalPrefix + pattern.getPatternString())
            .toArray(String[]::new);

        // 创建新的映射，保持原有的所有条件
        RequestMappingInfo.Builder builder = RequestMappingInfo.paths(prefixedPaths)
            .methods(mapping.getMethodsCondition().getMethods().toArray(new RequestMethod[0]))
            .params(mapping.getParamsCondition().getExpressions().toArray(new String[0]))
            .headers(mapping.getHeadersCondition().getExpressions().toArray(new String[0]))
            .consumes(mapping.getConsumesCondition().getConsumableMediaTypes().stream().map(MediaType::toString).toArray(String[]::new))
            .produces(mapping.getProducesCondition().getProducibleMediaTypes().stream().map(MediaType::toString).toArray(String[]::new));

        return builder.build();
    }
}