server:
  http_listen_port: 19080
  grpc_listen_port: 0

positions:
  filename: C:\workspace\rmp\promtail\positions.yaml  # 记录 Promtail 已读取的日志位置

clients:
  - url: http://host.docker.internal:13100/loki/api/v1/push  # Loki 服务的地址

scrape_configs:
  - job_name: windows-logs
    static_configs:
      - targets:
          - localhost
        labels:
          job: "windows-logs"
          __path__: C:\Users\<USER>\AppData\LocalLow\Antron\*\*.log  # 递归监听 logs 文件夹中的所有日志文件

    # relabel_configs 用于基于路径定义标签
    relabel_configs:
      - source_labels: [__path__]
        regex: '.+\\Antron\\([^\\]+)\\.*'
        target_label: folder_name
