package com.nnepoch.rendermanager.config;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;

import java.io.IOException;
import java.sql.Timestamp;

/**
 * <AUTHOR> 2024/7/9
 */
public class UnixTimestampDeserializer extends JsonDeserializer<Timestamp> {
    @Override
    public Timestamp deserialize(JsonParser jsonParser, DeserializationContext deserializationContext) throws IOException {
        long unixTimestamp = jsonParser.getValueAsLong();
        return new Timestamp(unixTimestamp * 1000);
    }
}
