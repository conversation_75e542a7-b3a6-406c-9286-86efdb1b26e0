package com.nnepoch.rendermanager.api.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR> 2024/6/20
 */
@RequiredArgsConstructor
@Getter
public enum SceneStatusEnum {
    RUNNING(1, "开启"),
    CLOSED(0, "关闭");

    public final Integer code;
    public final String desc;

    public static SceneStatusEnum fromCode(Integer code) {
        for (SceneStatusEnum status : SceneStatusEnum.values()) {
            if (status.code.equals(code)) {
                return status;
            }
        }
        return null;
    }
}
