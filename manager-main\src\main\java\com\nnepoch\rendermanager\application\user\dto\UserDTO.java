package com.nnepoch.rendermanager.application.user.dto;

import com.nnepoch.rendermanager.api.enums.UserRoleEnum;
import com.nnepoch.rendermanager.domain.user.aggregate.User;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户数据传输对象
 * <AUTHOR> Migration
 */
@Data
public class UserDTO {
    private Long id;
    private String username;
    private String email;
    private String mobile;
    private UserRoleEnum role;
    private boolean isBanned;
    private String avatar;
    private LocalDateTime loginAt;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    
    // 仅在创建用户时返回
    private String rawPassword;
    
    /**
     * 从领域对象转换为DTO
     */
    public static UserDTO fromDomain(User user) {
        UserDTO dto = new UserDTO();
        dto.setId(user.getId().getValue());
        dto.setUsername(user.getProfile().getUsername());
        dto.setEmail(user.getProfile().getEmail());
        dto.setMobile(user.getProfile().getMobile());
        dto.setRole(user.getRole());
        dto.setBanned(user.isBanned());
        dto.setAvatar(user.getProfile().getAvatar());
        dto.setLoginAt(user.getLoginAt());
        dto.setCreatedAt(user.getCreatedAt());
        dto.setUpdatedAt(user.getUpdatedAt());
        return dto;
    }
}
