package com.nnepoch.rendermanager.api.dto.request;

import com.nnepoch.rendermanager.api.dto.request.base.PageQueryDTO;
import com.nnepoch.rendermanager.api.enums.UserRoleEnum;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> 2024/6/13
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "用户查询条件")
public class UserQueryDTO extends PageQueryDTO {
    @Parameter(in = ParameterIn.QUERY, description = "用户名")
    private String username;

    @Parameter(in = ParameterIn.QUERY, description = "角色")
    private UserRoleEnum role;
}
