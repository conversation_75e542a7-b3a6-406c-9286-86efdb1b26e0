package com.nnepoch.rendermanager.biz.service.impl;

import cn.hutool.core.lang.id.NanoId;
import com.nnepoch.rendermanager.api.dto.response.StorageUploadResDTO;
import com.nnepoch.rendermanager.biz.service.StorageService;
import com.nnepoch.rendermanager.config.properties.AppMinioProperties;
import com.nnepoch.rendermanager.utils.MinioUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * <AUTHOR> <PERSON> 2024/6/21
 */

@RequiredArgsConstructor
@Service
@Slf4j
public class StorageServiceImpl implements StorageService {
    private final AppMinioProperties appMinioProperties;
    private final MinioUtil minioUtil;

    @Override
    public StorageUploadResDTO uploadFile(MultipartFile file) throws Exception {
        String bucketName = appMinioProperties.getPublicBucketName();

        String date = new SimpleDateFormat("yyyy/MM/dd").format(new Date());

        String fileName = file.getOriginalFilename();
        String suffix = null;
        if (fileName != null) {
            suffix = fileName.substring(fileName.lastIndexOf("."));
        }

        String objectName = date + "/" + NanoId.randomNanoId() + suffix;

        minioUtil.uploadFile(appMinioProperties.getPublicBucketName(), file, objectName, file.getContentType());


        return new StorageUploadResDTO(
            appMinioProperties.getUrl() + "/" + bucketName + "/" + objectName
        );
    }
}
