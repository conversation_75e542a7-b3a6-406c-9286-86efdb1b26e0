package com.nnepoch.rendermanager.infrastructure.persistence.user;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.nnepoch.rendermanager.api.enums.UserRoleEnum;
import com.nnepoch.rendermanager.biz.entity.UserEntity;
import com.nnepoch.rendermanager.biz.mapper.UserMapper;
import com.nnepoch.rendermanager.domain.shared.valueobject.UserId;
import com.nnepoch.rendermanager.domain.user.aggregate.User;
import com.nnepoch.rendermanager.domain.user.repository.UserRepository;
import com.nnepoch.rendermanager.domain.user.valueobject.UserProfile;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.time.ZoneId;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 用户仓储实现
 * <AUTHOR> Migration
 */
@Repository
@RequiredArgsConstructor
public class UserRepositoryImpl implements UserRepository {
    
    private final UserMapper userMapper;
    
    @Override
    public Optional<User> findById(UserId id) {
        UserEntity entity = userMapper.selectById(id.getValue());
        return entity != null ? Optional.of(toDomain(entity)) : Optional.empty();
    }
    
    @Override
    public void save(User user) {
        UserEntity entity = toEntity(user);
        if (entity.getId() != null && userMapper.selectById(entity.getId()) != null) {
            userMapper.updateById(entity);
        } else {
            userMapper.insert(entity);
        }
    }
    
    @Override
    public void delete(User user) {
        userMapper.deleteById(user.getId().getValue());
    }
    
    @Override
    public void deleteById(UserId id) {
        userMapper.deleteById(id.getValue());
    }
    
    @Override
    public boolean existsById(UserId id) {
        return userMapper.selectById(id.getValue()) != null;
    }
    
    @Override
    public Optional<User> findByUsername(String username) {
        LambdaQueryWrapper<UserEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserEntity::getUsername, username);
        UserEntity entity = userMapper.selectOne(wrapper);
        return entity != null ? Optional.of(toDomain(entity)) : Optional.empty();
    }
    
    @Override
    public Optional<User> findByMobile(String mobile) {
        LambdaQueryWrapper<UserEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserEntity::getMobile, mobile);
        UserEntity entity = userMapper.selectOne(wrapper);
        return entity != null ? Optional.of(toDomain(entity)) : Optional.empty();
    }
    
    @Override
    public Optional<User> findByEmail(String email) {
        LambdaQueryWrapper<UserEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserEntity::getEmail, email);
        UserEntity entity = userMapper.selectOne(wrapper);
        return entity != null ? Optional.of(toDomain(entity)) : Optional.empty();
    }
    
    @Override
    public List<User> findByRole(UserRoleEnum role) {
        LambdaQueryWrapper<UserEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserEntity::getRole, role);
        return userMapper.selectList(wrapper).stream()
                .map(this::toDomain)
                .collect(Collectors.toList());
    }
    
    @Override
    public boolean existsByUsername(String username) {
        LambdaQueryWrapper<UserEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserEntity::getUsername, username);
        return userMapper.selectCount(wrapper) > 0;
    }
    
    @Override
    public boolean existsByMobile(String mobile) {
        LambdaQueryWrapper<UserEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserEntity::getMobile, mobile);
        return userMapper.selectCount(wrapper) > 0;
    }
    
    @Override
    public boolean existsByEmail(String email) {
        LambdaQueryWrapper<UserEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserEntity::getEmail, email);
        return userMapper.selectCount(wrapper) > 0;
    }
    
    /**
     * 领域对象转换为持久化对象
     */
    private UserEntity toEntity(User user) {
        UserEntity entity = new UserEntity();
        entity.setId(user.getId().getValue());
        entity.setUsername(user.getProfile().getUsername());
        entity.setEmail(user.getProfile().getEmail());
        entity.setMobile(user.getProfile().getMobile());
        entity.setPassword(user.getPasswordHash());
        entity.setRole(user.getRole());
        entity.setIsBanned(user.isBanned());
        
        if (user.getLoginAt() != null) {
            entity.setLoginAt(Date.from(user.getLoginAt().atZone(ZoneId.systemDefault()).toInstant()));
        }
        if (user.getCreatedAt() != null) {
            entity.setCreatedAt(Date.from(user.getCreatedAt().atZone(ZoneId.systemDefault()).toInstant()));
        }
        if (user.getUpdatedAt() != null) {
            entity.setUpdatedAt(Date.from(user.getUpdatedAt().atZone(ZoneId.systemDefault()).toInstant()));
        }
        
        return entity;
    }
    
    /**
     * 持久化对象转换为领域对象
     */
    private User toDomain(UserEntity entity) {
        UserProfile profile = new UserProfile(entity.getUsername(), entity.getEmail(), entity.getMobile());
        UserId userId = UserId.of(entity.getId());
        
        // 使用反射或其他方式创建User对象，因为构造函数是私有的
        // 这里简化处理，实际项目中可能需要更复杂的转换逻辑
        return User.create(userId, profile, entity.getPassword(), entity.getRole());
    }
}
