package com.nnepoch.rendermanager.biz.dto.mq;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.nnepoch.rendermanager.config.UnixTimestampDeserializer;
import lombok.Data;

import java.sql.Timestamp;

/**
 * <AUTHOR> Che 2024/7/4
 */
@Data
public class SceneProcessMQDTO {
    private Integer pid;
    private String sessionId;
    private Long memory;
    private String exe;
    private Float cpuUsage;

    @JsonDeserialize(using = UnixTimestampDeserializer.class)
    private Timestamp startTime;
    private Integer runTime;
}
