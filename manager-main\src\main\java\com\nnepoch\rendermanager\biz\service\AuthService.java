package com.nnepoch.rendermanager.biz.service;

import com.nnepoch.rendermanager.api.dto.request.SigninReqDTO;
import com.nnepoch.rendermanager.api.dto.response.AuthPublicKeyResDTO;
import com.nnepoch.rendermanager.api.dto.response.SigninResDTO;

/**
 * <AUTHOR> 2024/6/20
 */
public interface AuthService {
    AuthPublicKeyResDTO getPublicKey();

    SigninResDTO signin(SigninReqDTO reqDTO);

    String decryptPassword(String keyId, String encryptedText);

    String encryptPassword(String keyId, String publicKey, String plainText);
}
