package com.nnepoch.rendermanager.exception;

import lombok.Getter;
import lombok.Setter;
import org.springframework.http.HttpStatus;

/**
 * <AUTHOR> 2024/6/22
 */

@Setter
@Getter
public class BizException extends BaseException {

    private HttpStatus httpStatus = HttpStatus.BAD_REQUEST;

    private final String code;

    private final String message;

    public BizException(String message) {
        super(message);
        this.code = HttpStatus.BAD_REQUEST.toString();
        this.message = message;
    }

    public BizException(String code, String message) {
        super(message);
        this.code = code;
        this.message = message;
    }

    public BizException(HttpStatus httpStatus, String message) {
        super(message);
        this.httpStatus = httpStatus;
        this.code = httpStatus.toString();
        this.message = message;
    }

    public BizException(HttpStatus httpStatus, String code, String message) {
        super(message);
        this.httpStatus = httpStatus;
        this.code = code;
        this.message = message;
    }

    public BizException(String code, String message, Throwable cause) {
        super(message, cause);
        this.code = code;
        this.message = message;
    }
}
