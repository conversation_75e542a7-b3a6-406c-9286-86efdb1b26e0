package com.nnepoch.rendermanager.biz.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nnepoch.rendermanager.api.dto.response.SimpleUserResDTO;
import com.nnepoch.rendermanager.api.enums.NodeStatusEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> 2024/6/17
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("node")
public class NodeEntity extends BaseEntity {
    private String hashId;
    private String sn;
    private String hostname;
    private NodeStatusEnum status;
    private Boolean enableLogCollection;
    private String os;
    private String osVersion;
    private Integer sceneCount;
    private Integer maxSceneInstanceCount;
    private Integer sceneRunningCount;
    private Integer sceneSeatsTaken;
    private String organization;
    private String mobile;
    private String email;
    private Long ownedBy;

    @TableField(exist = false)
    private SimpleUserResDTO owner;
}
