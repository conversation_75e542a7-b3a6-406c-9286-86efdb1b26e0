package com.nnepoch.rendermanager.biz.mq;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.nnepoch.rendermanager.biz.service.SceneService;
import com.nnepoch.rendermanager.config.RabbitConfig;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import java.util.Map;


@Slf4j
@RequiredArgsConstructor
@Component
public class DelayConsumer {
    private  final SceneService sceneService;
    private final ObjectMapper objectMapper = new ObjectMapper()
        .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

    @RabbitListener(queues = RabbitConfig.DELAY_QUEUE_NAME)
    public void receive(Message message) throws Exception {
       String msg = new String(message.getBody()) ;
       log.info("Received delay message: {}", msg);

        Map<String, Object> jsonMap = objectMapper.readValue(msg, new TypeReference<>() {});
        String type = jsonMap.get("type").toString();

        if (type.equals("scene.permit")) {
            sceneService.permitScene(Long.parseLong(jsonMap.get("sceneId").toString()));
        }
    }
}
