package com.nnepoch.rendermanager.biz.dto.mq;

import lombok.Data;

import java.util.List;

@Data
public class SceneInfoMQDTO {
    private String displayName;
    private String programName;
    private String exeName;
    private String version;
    private String changelog;
    private String resolution;
    private Short fps;
    private Integer bitrate;
    private String appDir;
    private CenterConfig centerConfig;
    private Integer maxSeatCount;
    private List<SceneProcessMQDTO> processes;

    @Data
    public static class CenterConfig {
        private String host;
        private String token;
        private String sceneName;
    }
}
