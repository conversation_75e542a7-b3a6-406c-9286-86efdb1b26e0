package com.nnepoch.rendermanager.domain.user.valueobject;

import lombok.EqualsAndHashCode;
import lombok.Getter;

/**
 * 用户档案值对象
 * <AUTHOR> Migration
 */
@Getter
@EqualsAndHashCode
public class UserProfile {
    private final String username;
    private final String email;
    private final String mobile;
    
    public UserProfile(String username, String email, String mobile) {
        if (username == null || username.trim().isEmpty()) {
            throw new IllegalArgumentException("Username cannot be empty");
        }
        if (email == null || !isValidEmail(email)) {
            throw new IllegalArgumentException("Invalid email format");
        }
        if (mobile == null || !isValidMobile(mobile)) {
            throw new IllegalArgumentException("Invalid mobile format");
        }
        
        this.username = username.trim();
        this.email = email.trim().toLowerCase();
        this.mobile = mobile.trim();
    }
    
    private boolean isValidEmail(String email) {
        return email.matches("^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$");
    }
    
    private boolean isValidMobile(String mobile) {
        return mobile.matches("^1[3-9]\\d{9}$");
    }
    
    public String getAvatar() {
        return "/avatar/9.x/big-smile/svg?seed=" + username;
    }
}
