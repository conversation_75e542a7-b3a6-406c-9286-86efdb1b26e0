package com.nnepoch.rendermanager.controller;

import com.nnepoch.rendermanager.api.dto.request.SigninReqDTO;
import com.nnepoch.rendermanager.api.dto.response.AuthPublicKeyResDTO;
import com.nnepoch.rendermanager.api.dto.response.SigninResDTO;
import com.nnepoch.rendermanager.biz.service.AuthService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * <AUTHOR> <PERSON> 2024/6/19
 */
@RestController
@RequestMapping(value = "/api/v1/auth", produces = "application/json")
@RequiredArgsConstructor
@Tag(name = "Auth", description = "认证相关接口")
public class AuthController {
    private final AuthService authService;

    @GetMapping("pubkey")
    @Operation(summary = "获取密码加签公钥")
    public AuthPublicKeyResDTO getPublicKey() {
        return authService.getPublicKey();
    }

    @PostMapping(value = "encrypt-password", produces = "text/plain")
    @Operation(summary = "加密密码(测试使用)", description = "前端不应该直接调用该接口")
    public String encryptPassword(@Valid @RequestBody Map<String, String> req) {
        String keyId = req.get("keyId");
        String publicKey = req.get("publicKey");
        String password = req.get("password");
        return authService.encryptPassword(keyId, publicKey, password);
    }

    @PostMapping(value = "decrypt-password", produces = "text/plain")
    @Operation(summary = "解密密码(测试使用)", description = "前端不应该直接调用该接口")
    public String decryptPassword(@Valid @RequestBody Map<String, String> req) {
        String keyId = req.get("keyId");
        String encryptedPassword = req.get("encryptedPassword");
        return authService.decryptPassword(keyId, encryptedPassword);
    }

    @PostMapping("signin")
    @Operation(summary = "登录接口", description = "登录接口")
    public SigninResDTO signin(@Valid @RequestBody SigninReqDTO reqDTO) {
        return authService.signin(reqDTO);
    }
}
